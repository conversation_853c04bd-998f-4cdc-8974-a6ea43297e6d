/* P04春日樱语色卡样式 - 按照PP\p04.html 1:1复刻 */

.p04-card-container {
  width: 800rpx;
  height: 1066rpx;
  position: relative;
  background-color: #FFFFFF;
  overflow: hidden;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

/* 上半部分 - 高度526rpx (1052/2132 * 1066) */
.p04-top-section {
  width: 100%;
  height: 526rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}

/* 标题样式 - 按P04位置 (424, 421) */
.p04-title {
  position: absolute;
  top: 188rpx; /* 421/2132 * 1066 */
  left: 212rpx; /* 424/1600 * 800 */
  font-size: 94rpx; /* 188/1600 * 800 */
  font-weight: 650;
  line-height: 1;
  white-space: nowrap;
}

/* P04 SVG花瓣容器 - 按P04位置 (847) */
.p04-svg-petals {
  position: absolute;
  top: 424rpx; /* 847/2132 * 1066 */
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 155rpx; /* 310/1600 * 800 */
  transform: translateY(-50%); /* 垂直居中 */
}

/* 单个P04 SVG花瓣容器 - 按P04尺寸 406x406 */
.p04-svg-petal {
  width: 203rpx; /* 406/1600 * 800 */
  height: 203rpx;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  /* 添加阴影效果 */
  filter: drop-shadow(0 4rpx 12rpx rgba(0, 0, 0, 0.15));
  /* 添加悬停效果 */
  transition: all 0.3s ease;
}

/* SVG图像样式 */
.petal-svg {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 加载状态样式 */
.petal-loading {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 50%;
  font-size: 24rpx;
  color: #999;
}

/* 悬停效果 */
.p04-svg-petal:active {
  transform: scale(0.95);
  filter: drop-shadow(0 6rpx 16rpx rgba(0, 0, 0, 0.2));
}



/* 下半部分 - 高度540rpx */
.p04-bottom-section {
  width: 100%;
  height: 540rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* 颜色代码容器 - 按P04位置 (1337) */
.p04-color-codes {
  position: absolute;
  top: 144rpx; /* (1337-1066)/2132 * 1066 */
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 156rpx; /* 312/1600 * 800 */
}

/* 单个颜色代码 - 按P04样式 */
.p04-color-code {
  font-size: 21rpx; /* 42/1600 * 800 */
  color: #7D7D7D;
  font-weight: 650;
  font-family: 'Courier New', monospace;
  text-align: center;
}

/* 圆形色块容器 - 按P04位置 (1546) */
.p04-circles {
  position: absolute;
  top: 254rpx; /* (1546-1066)/2132 * 1066 */
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 156rpx; /* 312/1600 * 800 */
}

/* 单个P04样式圆形色块 - 按P04尺寸 202x202 */
.p04-circle {
  width: 101rpx; /* 202/1600 * 800 */
  height: 101rpx;
  position: relative;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  /* 使用clip-path模拟P04的复杂圆形SVG形状 */
  clip-path: polygon(
    50% 10%,  /* 顶部突出 */
    65% 20%,
    80% 35%,
    90% 50%,  /* 右侧突出 */
    80% 65%,
    65% 80%,
    50% 90%,  /* 底部突出 */
    35% 80%,
    20% 65%,
    10% 50%,  /* 左侧突出 */
    20% 35%,
    35% 20%
  );
}
