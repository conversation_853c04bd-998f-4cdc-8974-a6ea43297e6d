// utils/storageOptimizer.js - Storage 访问优化工具
// 用于检测和优化重复的 storage 访问

const { storageCache } = require('./storageCache');
const logUtils = require('./logUtils');

class StorageOptimizer {
  constructor() {
    // 访问记录
    this.accessLog = new Map();
    // 重复访问阈值
    this.duplicateThreshold = 3;
    // 监控开关
    this.monitoring = false;
  }

  /**
   * 开始监控 storage 访问
   */
  startMonitoring() {
    this.monitoring = true;
    this.accessLog.clear();
    logUtils.log('[StorageOptimizer] 开始监控 storage 访问');
  }

  /**
   * 停止监控
   */
  stopMonitoring() {
    this.monitoring = false;
    logUtils.log('[StorageOptimizer] 停止监控 storage 访问');
  }

  /**
   * 记录访问
   * @param {string} key 存储键
   * @param {string} operation 操作类型 (get/set/remove)
   * @param {string} source 调用来源
   */
  logAccess(key, operation, source = 'unknown') {
    if (!this.monitoring) return;

    const timestamp = Date.now();
    const accessKey = `${key}_${operation}`;
    
    if (!this.accessLog.has(accessKey)) {
      this.accessLog.set(accessKey, []);
    }

    this.accessLog.get(accessKey).push({
      timestamp,
      source,
      key,
      operation
    });

    // 检查是否有重复访问
    this.checkDuplicateAccess(accessKey);
  }

  /**
   * 检查重复访问
   * @param {string} accessKey 访问键
   */
  checkDuplicateAccess(accessKey) {
    const accesses = this.accessLog.get(accessKey);
    if (!accesses || accesses.length < this.duplicateThreshold) return;

    // 检查最近的访问是否在短时间内重复
    const recentAccesses = accesses.slice(-this.duplicateThreshold);
    const timeSpan = recentAccesses[recentAccesses.length - 1].timestamp - recentAccesses[0].timestamp;

    // 如果在1秒内有多次相同访问，认为是重复访问
    if (timeSpan < 1000) {
      logUtils.warn(`[StorageOptimizer] 检测到重复访问: ${accessKey}`, {
        count: recentAccesses.length,
        timeSpan: timeSpan + 'ms',
        sources: recentAccesses.map(a => a.source)
      });

      // 建议使用缓存
      this.suggestCacheUsage(recentAccesses[0].key);
    }
  }

  /**
   * 建议使用缓存
   * @param {string} key 存储键
   */
  suggestCacheUsage(key) {
    logUtils.log(`[StorageOptimizer] 建议对 "${key}" 使用缓存机制以避免重复访问`);
  }

  /**
   * 获取访问统计
   * @returns {Object} 统计信息
   */
  getAccessStats() {
    const stats = {
      totalAccesses: 0,
      uniqueKeys: new Set(),
      duplicateAccesses: 0,
      keyStats: {}
    };

    for (const [accessKey, accesses] of this.accessLog.entries()) {
      const [key, operation] = accessKey.split('_');
      stats.uniqueKeys.add(key);
      stats.totalAccesses += accesses.length;

      if (!stats.keyStats[key]) {
        stats.keyStats[key] = {
          get: 0,
          set: 0,
          remove: 0,
          total: 0
        };
      }

      stats.keyStats[key][operation] += accesses.length;
      stats.keyStats[key].total += accesses.length;

      // 检查重复访问
      if (accesses.length >= this.duplicateThreshold) {
        stats.duplicateAccesses++;
      }
    }

    stats.uniqueKeys = stats.uniqueKeys.size;
    return stats;
  }

  /**
   * 生成优化建议
   * @returns {Array} 建议列表
   */
  generateOptimizationSuggestions() {
    const suggestions = [];
    const stats = this.getAccessStats();

    // 检查高频访问的键
    for (const [key, keyStats] of Object.entries(stats.keyStats)) {
      if (keyStats.get > 5) {
        suggestions.push({
          type: 'cache',
          key,
          message: `"${key}" 被读取 ${keyStats.get} 次，建议使用缓存`,
          priority: keyStats.get > 10 ? 'high' : 'medium'
        });
      }

      if (keyStats.total > 10) {
        suggestions.push({
          type: 'batch',
          key,
          message: `"${key}" 总访问次数 ${keyStats.total}，考虑批量操作`,
          priority: 'low'
        });
      }
    }

    return suggestions;
  }

  /**
   * 输出优化报告
   */
  generateOptimizationReport() {
    const stats = this.getAccessStats();
    const suggestions = this.generateOptimizationSuggestions();

    logUtils.log('\n=== Storage 访问优化报告 ===');
    logUtils.log(`总访问次数: ${stats.totalAccesses}`);
    logUtils.log(`唯一键数量: ${stats.uniqueKeys}`);
    logUtils.log(`重复访问检测: ${stats.duplicateAccesses} 项`);

    logUtils.log('\n--- 键访问统计 ---');
    for (const [key, keyStats] of Object.entries(stats.keyStats)) {
      logUtils.log(`${key}: 读取${keyStats.get}次, 写入${keyStats.set}次, 删除${keyStats.remove}次`);
    }

    if (suggestions.length > 0) {
      logUtils.log('\n--- 优化建议 ---');
      suggestions.forEach((suggestion, index) => {
        logUtils.log(`${index + 1}. [${suggestion.priority.toUpperCase()}] ${suggestion.message}`);
      });
    } else {
      logUtils.log('\n--- 优化建议 ---');
      logUtils.log('当前访问模式良好，无需特别优化');
    }

    logUtils.log('\n--- 缓存使用统计 ---');
    logUtils.log(storageCache.getCacheStats());
    logUtils.log('========================\n');

    return {
      stats,
      suggestions
    };
  }

  /**
   * 重置监控数据
   */
  reset() {
    this.accessLog.clear();
    logUtils.log('[StorageOptimizer] 监控数据已重置');
  }
}

// 创建全局实例
const storageOptimizer = new StorageOptimizer();

// 在开发环境下自动开始监控
if (typeof __wxConfig !== 'undefined' && __wxConfig.debug) {
  storageOptimizer.startMonitoring();
  
  // 定期输出报告
  setInterval(() => {
    if (storageOptimizer.monitoring) {
      storageOptimizer.generateOptimizationReport();
    }
  }, 30000); // 每30秒输出一次报告
}

module.exports = {
  storageOptimizer,
  StorageOptimizer
};
