<!--pages/template/template.wxml-->
<view class="page-wrapper">
  <view class="scroll-area">
    <!-- 模式切换按钮 -->
    <view class="mode-switch-container">
      <view class="mode-switch">
        <view
          class="mode-switch-item {{currentMode === 'image' ? 'active' : ''}}"
          bindtap="switchMode"
          data-mode="image"
        >
          图片主题色
        </view>
        <view
          class="mode-switch-item {{currentMode === 'custom' ? 'active' : ''}}"
          bindtap="switchMode"
          data-mode="custom"
        >
          自定义色卡
        </view>
      </view>
    </view>

    <view class="page-subtitle">
      {{currentMode === 'image' ? '请选择您喜欢的色卡样式' : '请选择自定义色卡模板'}}
    </view>
    <!-- 图片主题色模板 -->
    <view wx:if="{{currentMode === 'image'}}" class="templates-container">
      <view
        wx:for="{{templates}}"
        wx:key="id"
        class="template-item {{item.selected ? 'selected' : ''}} {{item.isComingSoon ? 'coming-soon' : ''}}"
        bindtap="selectTemplate"
        data-id="{{item.id}}"
      >
        <view class="template-image-container">
          <image class="template-image" src="{{item.imagePath}}" mode="aspectFill"></image>
          <view wx:if="{{item.isComingSoon}}" class="coming-soon-overlay">
            <view class="coming-soon-text">敬请期待...</view>
          </view>
        </view>
        <view class="template-name">{{item.name}}</view>
      </view>
    </view>

    <!-- 自定义色卡模板 - 滑动展示 -->
    <view wx:elif="{{currentMode === 'custom'}}" class="custom-templates-swiper-container">
      <swiper
        class="custom-templates-swiper"
        current="{{currentCustomTemplateIndex}}"
        bindchange="onCustomTemplateChange"
        indicator-dots="{{false}}"
        circular="{{true}}"
        autoplay="{{false}}"
        duration="{{300}}"
        easing-function="easeInOutCubic"
      >
        <swiper-item
          wx:for="{{customTemplates}}"
          wx:key="id"
          class="custom-template-slide"
        >
          <view class="custom-template-card">
            <!-- 蜜桃汽水模板预览 (ID: 101) -->
            <view wx:if="{{item.id === 101}}" class="custom-template-preview-large">
              <!-- 上半部分背景 -->
              <view class="custom-template-top-bg">
                <view class="custom-template-title-large">{{item.name}}</view>
              </view>
              <!-- 圆形色块区域 - 跨越上下背景 -->
              <view class="custom-circles-area">
                <view class="custom-color-blocks-large">
                  <view
                    wx:for="{{item.colors}}"
                    wx:for-item="color"
                    wx:for-index="colorIndex"
                    wx:key="colorIndex"
                    class="custom-color-block-large"
                    style="background-color: {{color}};"
                  ></view>
                </view>
              </view>
              <!-- 下半部分背景 -->
              <view class="custom-template-bottom-bg">
                <!-- 颜色代码 -->
                <view class="custom-color-codes-large">
                  <view
                    wx:for="{{item.colors}}"
                    wx:for-item="color"
                    wx:for-index="colorIndex"
                    wx:key="colorIndex"
                    class="custom-color-code-large"
                  >{{color}}</view>
                </view>
                <!-- 小矩形色块预览 -->
                <view class="custom-small-blocks">
                  <view
                    wx:for="{{item.colors}}"
                    wx:for-item="color"
                    wx:for-index="colorIndex"
                    wx:key="colorIndex"
                    class="custom-small-block"
                    style="background-color: {{color}};"
                  ></view>
                </view>
              </view>
            </view>

            <!-- 海盐气泡模板预览 (ID: 102) -->
            <view wx:elif="{{item.id === 102}}" class="custom-template-preview-p02">
              <view class="custom-template-title-p02">{{item.name}}</view>
              <!-- 长条形色块 -->
              <view class="custom-long-blocks">
                <view
                  wx:for="{{item.colors}}"
                  wx:for-item="color"
                  wx:for-index="colorIndex"
                  wx:key="colorIndex"
                  class="custom-long-block"
                  style="background-color: {{color}};"
                ></view>
              </view>
              <!-- 颜色代码 -->
              <view class="custom-color-codes-p02">
                <view
                  wx:for="{{item.colors}}"
                  wx:for-item="color"
                  wx:for-index="colorIndex"
                  wx:key="colorIndex"
                  class="custom-color-code-p02"
                  style="color: {{color}};"
                >{{color}}</view>
              </view>
              <!-- 圆形色块 -->
              <view class="custom-circles-p02">
                <view
                  wx:for="{{item.colors}}"
                  wx:for-item="color"
                  wx:for-index="colorIndex"
                  wx:key="colorIndex"
                  class="custom-circle-p02"
                  style="background-color: {{color}};"
                ></view>
              </view>
            </view>

            <!-- 落日漫旅模板预览 (ID: 103) -->
            <view wx:elif="{{item.id === 103}}" class="custom-template-preview-p03">
              <!-- 上半部分背景 -->
              <view class="custom-template-top-bg-p03">
                <!-- 标题 -->
                <view class="custom-template-title-p03">{{item.name}}</view>

                <!-- 旋转方形色块 -->
                <view class="custom-diamond-blocks">
                  <view
                    wx:for="{{item.colors}}"
                    wx:for-item="color"
                    wx:for-index="colorIndex"
                    wx:key="colorIndex"
                    class="custom-diamond-block"
                    style="background-color: {{color}};"
                  ></view>
                </view>
              </view>

              <!-- 下半部分背景 -->
              <view class="custom-template-bottom-bg-p03">
                <!-- 颜色代码 -->
                <view class="custom-color-codes-p03">
                  <view
                    wx:for="{{item.colors}}"
                    wx:for-item="color"
                    wx:for-index="colorIndex"
                    wx:key="colorIndex"
                    class="custom-color-code-p03"
                  >{{color}}</view>
                </view>

                <!-- 圆形色块 -->
                <view class="custom-circles-p03">
                  <view
                    wx:for="{{item.colors}}"
                    wx:for-item="color"
                    wx:for-index="colorIndex"
                    wx:key="colorIndex"
                    class="custom-circle-p03"
                    style="background-color: {{color}};"
                  ></view>
                </view>
              </view>
            </view>

            <!-- 春日樱语模板预览 (ID: 104) -->
            <view wx:elif="{{item.id === 104}}" class="custom-template-preview-p04">
              <!-- 上半部分背景 -->
              <view class="custom-template-top-bg-p04">
                <!-- 标题 -->
                <view class="custom-template-title-p04">{{item.name}}</view>

                <!-- 花瓣形状色块 -->
                <view class="custom-petal-shapes">
                  <view
                    wx:for="{{item.colors}}"
                    wx:for-item="color"
                    wx:for-index="colorIndex"
                    wx:key="colorIndex"
                    class="custom-petal-svg-container"
                    data-color="{{color}}"
                    data-template-id="{{item.id}}"
                    data-index="{{colorIndex}}"
                  >
                    <image
                      wx:if="{{p04PetalDataUrls && p04PetalDataUrls.length > colorIndex && p04PetalDataUrls[colorIndex]}}"
                      class="custom-petal-svg"
                      src="{{p04PetalDataUrls[colorIndex]}}"
                    />
                    <view wx:else class="custom-petal-loading">
                      <text>加载中...</text>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 下半部分背景 -->
              <view class="custom-template-bottom-bg-p04">
                <!-- 颜色代码 -->
                <view class="custom-color-codes-p04">
                  <view
                    wx:for="{{item.colors}}"
                    wx:for-item="color"
                    wx:for-index="colorIndex"
                    wx:key="colorIndex"
                    class="custom-color-code-p04"
                  >{{color}}</view>
                </view>

                <!-- 星形圆形色块 -->
                <view class="custom-star-circles">
                  <view
                    wx:for="{{item.colors}}"
                    wx:for-item="color"
                    wx:for-index="colorIndex"
                    wx:key="colorIndex"
                    class="custom-star-svg-container"
                    data-color="{{color}}"
                    data-template-id="{{item.id}}"
                    data-index="{{colorIndex}}"
                  >
                    <image
                      wx:if="{{p04CircleDataUrls && p04CircleDataUrls.length > colorIndex && p04CircleDataUrls[colorIndex]}}"
                      class="custom-star-svg"
                      src="{{p04CircleDataUrls[colorIndex]}}"
                    />
                    <view wx:else class="custom-circle-loading">
                      <text>加载中...</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </swiper-item>
      </swiper>

      <!-- 滑动提示 - 改为相对定位 -->
      <view class="swipe-hint-relative">
        <view class="swipe-hint-text">左右滑动查看模板</view>
        <view class="swipe-hint-dots">
          <view
            wx:for="{{customTemplates}}"
            wx:key="id"
            class="hint-dot {{index === currentCustomTemplateIndex ? 'active' : ''}}"
          ></view>
        </view>
      </view>
    </view>
  </view>

  <view class="btn-container">
    <button class="next-btn" bindtap="goToNext">
      {{currentMode === 'image' ? '下一步，选择图片' : '下一步，编辑色值'}}
    </button>
  </view>
</view>
