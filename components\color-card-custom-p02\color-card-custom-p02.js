// components/color-card-custom-p02/color-card-custom-p02.js
Component({
  properties: {
    colors: {
      type: Array,
      value: ['#21115E', '#1533AC']
    },
    title: {
      type: String,
      value: '海盐气泡'
    },
    subTitle: {
      type: String,
      value: '@KALA配色'
    },
    backgroundColor: {
      type: String,
      value: '#FFFFFF'
    },
    titleColor: {
      type: String,
      value: '#5865B1'
    }
  },

  data: {
    canvasWidth: 1200, // 增加Canvas宽度以容纳5个颜色，原设计1600px
    canvasHeight: 1378, // 按比例缩放，原设计1838px
  },

  lifetimes: {
    attached() {
      // 组件实例进入页面节点树时执行
      console.log('P02自定义色卡组件已加载');
    },

    ready() {
      // 在组件在视图层布局完成后执行
      console.log('P02自定义色卡组件布局完成，开始生成色卡');
      // 延迟一段时间后开始生成，确保Canvas已准备好
      setTimeout(() => {
        this.generateColorCard();
      }, 100);
    }
  },

  methods: {
    // 安全地获取设备像素比
    getDevicePixelRatio() {
      try {
        // 优先使用全局缓存的系统信息
        const app = getApp();
        if (app && app.globalData && app.globalData.systemInfo && app.globalData.systemInfo.pixelRatio) {
          return app.globalData.systemInfo.pixelRatio;
        }

        // 如果全局信息不可用，尝试获取设备信息
        const deviceInfo = wx.getDeviceInfo();
        return deviceInfo.pixelRatio || 2;
      } catch (error) {
        // 生产环境中静默处理
        return 2; // 默认值
      }
    },

    // 生成自定义色卡
    async generateColorCard() {
      try {
        const { colors, title, subTitle, backgroundColor, titleColor } = this.properties;
        const { canvasWidth, canvasHeight } = this.data;

        console.log('开始生成P02样式自定义色卡:', { colors, title, backgroundColor });
        console.log('颜色数组长度:', colors ? colors.length : 'undefined');
        console.log('颜色数组内容:', colors);

        // 验证颜色数据
        if (!colors || !Array.isArray(colors) || colors.length === 0) {
          console.error('P02色卡生成失败：颜色数据无效');
          this.triggerEvent('generated', { path: '' });
          return;
        }

        // 创建Canvas上下文 - 修复Promise错误处理
        const canvas = await this.getCanvasNode();
        if (!canvas || !canvas.node) {
          console.error('P02色卡生成失败：Canvas节点获取失败');
          this.triggerEvent('generated', { path: '', error: 'Canvas节点获取失败' });
          return;
        }

        const ctx = canvas.node.getContext('2d');

        // 验证上下文是否正确获取
        if (!ctx || typeof ctx.clearRect !== 'function') {
          console.error('P02色卡生成失败：Canvas上下文获取失败');
          this.triggerEvent('generated', { path: '', error: 'Canvas上下文获取失败' });
          return;
        }

        const dpr = this.getDevicePixelRatio();

        // 设置Canvas尺寸
        canvas.node.width = canvasWidth * dpr;
        canvas.node.height = canvasHeight * dpr;
        ctx.scale(dpr, dpr);

        console.log('Canvas设置完成，开始绘制');

        // 绘制背景
        ctx.fillStyle = backgroundColor || '#FFFFFF';
        ctx.fillRect(0, 0, canvasWidth, canvasHeight);

        // 绘制内容
        this.drawTitle(ctx, title || '海盐气泡', subTitle, titleColor);
        this.drawColorBlocks(ctx, colors);

        console.log('绘制完成，准备保存');

        // 保存Canvas
        setTimeout(() => {
          this.saveCanvas(canvas.node);
        }, 300);

      } catch (err) {
        console.error('绘制P02样式自定义色卡失败:', err);
        this.handleCanvasError(err);
      }
    },

    // 获取Canvas节点 - 带超时和错误处理
    getCanvasNode: function() {
      return new Promise((resolve, reject) => {
        const query = wx.createSelectorQuery().in(this);

        // 设置超时机制
        const timeout = setTimeout(() => {
          reject(new Error('Canvas节点获取超时'));
        }, 5000);

        query.select('#colorCardCanvas')
          .fields({ node: true, size: true })
          .exec((res) => {
            clearTimeout(timeout);

            console.log('Canvas查询结果:', res);
            if (res && res[0] && res[0].node) {
              resolve(res[0]);
            } else {
              reject(new Error('Canvas节点获取失败：节点不存在或无效'));
            }
          });
      });
    },

    // Canvas错误处理
    handleCanvasError: function(error) {
      console.error('Canvas操作错误:', error);

      let errorMessage = '色卡生成失败';
      if (error.message) {
        if (error.message.includes('超时')) {
          errorMessage = 'Canvas初始化超时';
        } else if (error.message.includes('节点')) {
          errorMessage = 'Canvas节点获取失败';
        } else if (error.message.includes('上下文')) {
          errorMessage = 'Canvas上下文获取失败';
        }
      }

      // 触发错误事件，让父组件知道生成失败
      this.triggerEvent('generated', {
        path: '',
        error: errorMessage
      });
    },

    // 绘制标题和副标题 - 按照p02.html的设计
    drawTitle(ctx, title, subTitle, titleColor) {
      const { canvasWidth, canvasHeight } = this.data;

      // 按照原设计比例计算位置和大小
      // 主标题：调整位置为上方
      const titleX = canvasWidth / 2; // 居中显示
      const titleY = Math.floor(240 * canvasHeight / 1838); // 调整Y位置，为副标题留出空间
      const titleFontSize = Math.floor(130 * canvasWidth / 1600); // 按比例计算字体大小

      // 设置主标题样式 - 使用动态标题颜色
      ctx.fillStyle = titleColor || '#5865B1';
      ctx.font = `bold ${titleFontSize}px PingFang SC, Arial, sans-serif`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';

      // 绘制主标题
      ctx.fillText(title, titleX, titleY);

      // 副标题：在主标题下方，位置再下移，字号调大一点
      const subTitleY = Math.floor(380 * canvasHeight / 1838); // 位置再下移
      const subTitleFontSize = Math.floor(58 * canvasWidth / 1600); // 字号调大一点

      // 设置副标题样式 - 使用动态标题颜色，透明度较低
      ctx.fillStyle = titleColor || '#5865B1';
      ctx.globalAlpha = 0.8; // 设置透明度
      ctx.font = `normal ${subTitleFontSize}px PingFang SC, Arial, sans-serif`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';

      // 绘制副标题（只有当副标题不为空时才绘制）
      if (subTitle && subTitle !== '' && subTitle.trim().length > 0) {
        ctx.fillText(subTitle, titleX, subTitleY);
      }

      // 重置透明度
      ctx.globalAlpha = 1.0;
    },

    // 绘制颜色块 - 按照p02.html的设计
    drawColorBlocks(ctx, colors) {
      const { canvasWidth, canvasHeight } = this.data;

      // 绘制长条形色块
      this.drawLongBlocks(ctx, colors);
      
      // 绘制颜色代码
      this.drawColorCodes(ctx, colors);
      
      // 绘制圆形色块
      this.drawCircleBlocks(ctx, colors);
    },

    // 绘制长条形色块
    drawLongBlocks(ctx, colors) {
      const { canvasWidth, canvasHeight } = this.data;

      console.log('绘制长条形色块，颜色数量:', colors.length);
      console.log('绘制长条形色块，颜色数据:', colors);

      // 固定色块尺寸，根据颜色数量调整间距
      const blockWidth = Math.floor(168 * canvasWidth / 1600); // 固定宽度，按比例缩放
      const blockHeight = Math.floor(612 * canvasHeight / 1838); // 固定高度
      const borderRadius = Math.floor(84 * canvasWidth / 1600); // 固定圆角半径
      const colorCount = colors.length;

      // 计算可用宽度和动态间距，设置最大间距限制
      const maxWidth = canvasWidth * 0.9; // 使用Canvas宽度的90%
      const totalBlockWidth = colorCount * blockWidth;
      const availableGapWidth = maxWidth - totalBlockWidth;
      const maxGap = Math.floor(200 * canvasWidth / 1600); // 设置最大间距限制，按比例缩放
      let gap = colorCount > 1 ? Math.floor(availableGapWidth / (colorCount - 1)) : 0;
      gap = Math.min(gap, maxGap); // 限制最大间距

      // 计算总宽度并居中
      const totalWidth = totalBlockWidth + (colorCount - 1) * gap;
      const startX = (canvasWidth - totalWidth) / 2;
      const startY = Math.floor(579 * canvasHeight / 1838);

      console.log('计算结果 - 固定色块宽度:', blockWidth, '动态间距:', gap, '总宽度:', totalWidth, 'Canvas宽度:', canvasWidth);

      for (let i = 0; i < colors.length; i++) {
        const x = startX + i * (blockWidth + gap);
        const y = startY;

        console.log(`绘制第${i + 1}个长条形色块，颜色: ${colors[i]}, 位置: (${x}, ${y}), 尺寸: ${blockWidth}x${blockHeight}`);

        // 绘制圆角矩形 - 使用兼容性更好的方法
        ctx.fillStyle = colors[i];
        this.drawRoundedRect(ctx, x, y, blockWidth, blockHeight, borderRadius);
      }
    },

    // 绘制圆角矩形的兼容方法
    drawRoundedRect(ctx, x, y, width, height, radius) {
      ctx.beginPath();
      ctx.moveTo(x + radius, y);
      ctx.lineTo(x + width - radius, y);
      ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
      ctx.lineTo(x + width, y + height - radius);
      ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
      ctx.lineTo(x + radius, y + height);
      ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
      ctx.lineTo(x, y + radius);
      ctx.quadraticCurveTo(x, y, x + radius, y);
      ctx.closePath();
      ctx.fill();
    },

    // 绘制颜色代码
    drawColorCodes(ctx, colors) {
      const { canvasWidth, canvasHeight } = this.data;

      console.log('绘制颜色代码，颜色数量:', colors.length);

      // 使用与长条形色块相同的布局计算（固定宽度，动态间距）
      const blockWidth = Math.floor(168 * canvasWidth / 1600); // 与长条形色块相同的固定宽度
      const colorCount = colors.length;

      // 计算动态间距，设置最大间距限制
      const maxWidth = canvasWidth * 0.9;
      const totalBlockWidth = colorCount * blockWidth;
      const availableGapWidth = maxWidth - totalBlockWidth;
      const maxGap = Math.floor(200 * canvasWidth / 1600); // 设置最大间距限制
      let gap = colorCount > 1 ? Math.floor(availableGapWidth / (colorCount - 1)) : 0;
      gap = Math.min(gap, maxGap); // 限制最大间距

      const codeY = Math.floor(1281 * canvasHeight / 1838);
      const fontSize = Math.floor(48 * canvasWidth / 1600);
      const totalWidth = totalBlockWidth + (colorCount - 1) * gap;
      const startX = (canvasWidth - totalWidth) / 2;

      ctx.font = `${fontSize}px Arial, sans-serif`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';

      for (let i = 0; i < colors.length; i++) {
        const x = startX + i * (blockWidth + gap) + blockWidth / 2;
        // 使用对应色块的颜色作为文字颜色
        ctx.fillStyle = colors[i];
        console.log(`绘制第${i + 1}个颜色代码: ${colors[i]}, 位置: (${x}, ${codeY}), 颜色: ${colors[i]}`);
        ctx.fillText(colors[i], x, codeY);
      }
    },

    // 绘制圆形色块
    drawCircleBlocks(ctx, colors) {
      const { canvasWidth, canvasHeight } = this.data;

      console.log('绘制圆形色块，颜色数量:', colors.length);

      // 使用与长条形色块相同的布局计算（固定大小，动态间距）
      const circleSize = Math.floor(168 * canvasWidth / 1600); // 与长条形色块相同的固定宽度
      const colorCount = colors.length;

      // 计算动态间距，设置最大间距限制
      const maxWidth = canvasWidth * 0.9;
      const totalCircleWidth = colorCount * circleSize;
      const availableGapWidth = maxWidth - totalCircleWidth;
      const maxGap = Math.floor(200 * canvasWidth / 1600); // 设置最大间距限制
      let gap = colorCount > 1 ? Math.floor(availableGapWidth / (colorCount - 1)) : 0;
      gap = Math.min(gap, maxGap); // 限制最大间距

      const startY = Math.floor(1432 * canvasHeight / 1838);
      const totalWidth = totalCircleWidth + (colorCount - 1) * gap;
      const startX = (canvasWidth - totalWidth) / 2;

      for (let i = 0; i < colors.length; i++) {
        const x = startX + i * (circleSize + gap);
        const y = startY;

        console.log(`绘制第${i + 1}个圆形色块，颜色: ${colors[i]}, 位置: (${x}, ${y}), 大小: ${circleSize}`);

        // 绘制圆形
        ctx.fillStyle = colors[i];
        ctx.beginPath();
        ctx.arc(x + circleSize / 2, y + circleSize / 2, circleSize / 2, 0, 2 * Math.PI);
        ctx.fill();
      }
    },

    // 保存Canvas - 修复Promise错误处理
    async saveCanvas(canvasNode) {
      try {
        const { canvasWidth, canvasHeight } = this.data;

        // 添加超时控制的Canvas保存
        const res = await Promise.race([
          new Promise((resolve, reject) => {
            wx.canvasToTempFilePath({
              canvas: canvasNode,
              x: 0,
              y: 0,
              width: canvasWidth,
              height: canvasHeight,
              destWidth: canvasWidth,
              destHeight: canvasHeight,
              fileType: 'png',
              quality: 1,
              success: resolve,
              fail: reject
            }, this);
          }),
          new Promise((_, reject) => {
            setTimeout(() => {
              reject(new Error('Canvas保存超时'));
            }, 10000);
          })
        ]);

        // Canvas保存成功
        console.log('P02色卡保存成功:', res.tempFilePath);
        this.triggerEvent('generated', { path: res.tempFilePath });
      } catch (err) {
        console.error('保存P02样式自定义色卡失败:', err);
        this.handleCanvasError(err);
      }
    }
  }
});
