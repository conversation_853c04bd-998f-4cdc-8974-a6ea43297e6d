// components/colorWheelCanvas/colorWheelCanvas.js
const colorUtils = require('../../utils/colorUtils');
Component({
  properties: {
    canvasId: {
      type: String,
      value: 'colorWheelCanvas'
    },
    width: {
      type: Number,
      value: 300
    },
    height: {
      type: Number,
      value: 300
    },
    wheelSize: {
      type: Number,
      value: 280 // 增大色轮的大小
    },
    selectedColor: {
      type: String,
      value: '#c83c23',
      observer: function(newVal) {
        this.updateSelectedPoint(newVal);
        // 当选中颜色变化时，更新相关颜色点
        this.updateRelatedColors();
      }
    },
    paletteType: {
      type: String,
      value: 'complementary',
      observer: function(newVal) {
        // 当配色方案类型改变时，更新相关颜色点
        this.updateRelatedColors();
      }
    },
    colorPalette: {
      type: Array,
      value: [],
      observer: function(newVal) {
        // 当配色方案颜色改变时，更新相关颜色点
        this.updateRelatedColors();
      }
    },

  },

  data: {
    ctx: null,
    centerX: 0,
    centerY: 0,
    radius: 0,
    selectedPoint: { x: 0, y: 0 },
    brightness: 50, // 亮度值，默认为50%
    palettePoints: [], // 配色方案颜色点位置
    paletteColors: [], // 配色方案颜色值
    connectionLines: [] // 连线数据（包含起点、长度、角度和类型）
  },

  lifetimes: {
    attached: function() {
      // 计算色轮的中心点和半径
      const centerX = this.properties.wheelSize / 2;
      const centerY = this.properties.wheelSize / 2;
      // 减小半径，为边缘留出空间
      const radius = this.properties.wheelSize / 2 - 10;

      // 延迟设置数据，避免 "too early" 错误
      setTimeout(() => {
        this.setData({
          centerX: centerX,
          centerY: centerY,
          radius: radius
        });

        // 初始化颜色点位置
        this.updateSelectedPoint(this.properties.selectedColor);
        this.updateRelatedColors();
      }, 50);
    },

    ready: function() {
      // 组件准备就绪
      console.log('组件准备就绪');
    },

    detached: function() {
      // 组件卸载时清理资源
      console.log('组件卸载，清理资源');
    }
  },

  pageLifetimes: {
    // 页面显示时更新颜色点位置
    show: function() {
      console.log('页面显示');
      // 重新计算中心点和半径
      const centerX = this.properties.wheelSize / 2;
      const centerY = this.properties.wheelSize / 2;
      const radius = this.properties.wheelSize / 2 - 10;

      this.setData({
        centerX: centerX,
        centerY: centerY,
        radius: radius
      }, () => {
        this.updateSelectedPoint(this.properties.selectedColor);
        this.updateRelatedColors();
        // 确保连线也会更新
        this.updateConnectionLines();
      });
    },

    // 页面尺寸变化时更新位置
    resize: function(size) {
      console.log('页面尺寸变化', size);
      // 重新计算中心点和半径
      const centerX = this.properties.wheelSize / 2;
      const centerY = this.properties.wheelSize / 2;
      const radius = this.properties.wheelSize / 2 - 10;

      this.setData({
        centerX: centerX,
        centerY: centerY,
        radius: radius
      }, () => {
        this.updateSelectedPoint(this.properties.selectedColor);
        this.updateRelatedColors();
        // 确保连线也会更新
        this.updateConnectionLines();
      });
    }
  },

  methods: {
    // 更新选中点的位置
    updateSelectedPoint: function(color) {
      // 将颜色转换为HSL
      const hsl = colorUtils.hexToHSL(color);

      // 使用calculatePointPosition方法计算位置
      const point = this.calculatePointPosition(hsl.h, hsl.s);

      // 更新亮度值
      this.setData({
        selectedPoint: point,
        brightness: hsl.l
      });
    },

    // 更新相关颜色点的位置
    updateRelatedColors: function() {
      // 获取当前HSL值
      const hsl = colorUtils.hexToHSL(this.properties.selectedColor);

      // 存储配色方案颜色点的位置和颜色
      const palettePoints = [];
      const paletteColors = [];
      const connectionLines = [];

      // 单色调方案不显示颜色点
      if (this.properties.paletteType === 'monochromatic') {
        this.setData({
          palettePoints: [],
          paletteColors: [],
          connectionLines: []
        });
        return;
      }

      // 如果有传入的配色方案，使用传入的配色方案
      if (this.properties.colorPalette && this.properties.colorPalette.length > 0) {
        // 根据配色方案类型决定要显示的颜色点
        switch(this.properties.paletteType) {
          case 'complementary':
            // 互补色只显示一个点（第二个颜色）
            if (this.properties.colorPalette.length > 1) {
              const color = this.properties.colorPalette[1];
              const colorHSL = colorUtils.hexToHSL(color);
              const point = this.calculatePointPosition(colorHSL.h, colorHSL.s);
              palettePoints.push(point);
              paletteColors.push(color);
            }
            break;

          case 'analogous':
            // 类似色显示两个点（左右两侧的颜色）
            if (this.properties.colorPalette.length > 2) {
              // 左侧类似色
              const color1 = this.properties.colorPalette[0];
              const colorHSL1 = colorUtils.hexToHSL(color1);
              const point1 = this.calculatePointPosition(colorHSL1.h, colorHSL1.s);
              palettePoints.push(point1);
              paletteColors.push(color1);

              // 右侧类似色
              const color2 = this.properties.colorPalette[2];
              const colorHSL2 = colorUtils.hexToHSL(color2);
              const point2 = this.calculatePointPosition(colorHSL2.h, colorHSL2.s);
              palettePoints.push(point2);
              paletteColors.push(color2);
            }
            break;

          case 'triadic':
            // 三角配色显示两个点（相差120度的两个颜色）
            if (this.properties.colorPalette.length > 2) {
              // 三角色1
              const color1 = this.properties.colorPalette[1];
              const colorHSL1 = colorUtils.hexToHSL(color1);
              const point1 = this.calculatePointPosition(colorHSL1.h, colorHSL1.s);
              palettePoints.push(point1);
              paletteColors.push(color1);

              // 三角色2
              const color2 = this.properties.colorPalette[2];
              const colorHSL2 = colorUtils.hexToHSL(color2);
              const point2 = this.calculatePointPosition(colorHSL2.h, colorHSL2.s);
              palettePoints.push(point2);
              paletteColors.push(color2);
            }
            break;

          case 'splitComplementary':
            // 分裂互补色显示两个点
            if (this.properties.colorPalette.length > 2) {
              // 分裂互补色1
              const color1 = this.properties.colorPalette[1];
              const colorHSL1 = colorUtils.hexToHSL(color1);
              const point1 = this.calculatePointPosition(colorHSL1.h, colorHSL1.s);
              palettePoints.push(point1);
              paletteColors.push(color1);

              // 分裂互补色2
              const color2 = this.properties.colorPalette[2];
              const colorHSL2 = colorUtils.hexToHSL(color2);
              const point2 = this.calculatePointPosition(colorHSL2.h, colorHSL2.s);
              palettePoints.push(point2);
              paletteColors.push(color2);
            }
            break;

          case 'tetradic':
            // 四角色显示三个点
            if (this.properties.colorPalette.length > 3) {
              // 四角色1
              const color1 = this.properties.colorPalette[1];
              const colorHSL1 = colorUtils.hexToHSL(color1);
              const point1 = this.calculatePointPosition(colorHSL1.h, colorHSL1.s);
              palettePoints.push(point1);
              paletteColors.push(color1);

              // 四角色2
              const color2 = this.properties.colorPalette[2];
              const colorHSL2 = colorUtils.hexToHSL(color2);
              const point2 = this.calculatePointPosition(colorHSL2.h, colorHSL2.s);
              palettePoints.push(point2);
              paletteColors.push(color2);

              // 四角色3
              const color3 = this.properties.colorPalette[3];
              const colorHSL3 = colorUtils.hexToHSL(color3);
              const point3 = this.calculatePointPosition(colorHSL3.h, colorHSL3.s);
              palettePoints.push(point3);
              paletteColors.push(color3);
            }
            break;
        }
      }

      // 更新配色方案颜色点的位置和颜色
      this.setData({
        palettePoints,
        paletteColors
      }, () => {
        // 更新连线
        this.updateConnectionLines();
      });
    },

    // 计算连线的长度和角度
    calculateConnectionLine: function(startPoint, endPoint, type = 'default') {
      // 计算两点之间的距离（线的长度）
      const dx = endPoint.x - startPoint.x;
      const dy = endPoint.y - startPoint.y;
      const length = Math.sqrt(dx * dx + dy * dy);

      // 计算线的角度（弧度转角度）
      let angle = Math.atan2(dy, dx) * 180 / Math.PI;

      return {
        startX: startPoint.x,
        startY: startPoint.y,
        endX: endPoint.x,
        endY: endPoint.y,
        length: length,
        angle: angle,
        type: type
      };
    },

    // 计算颜色点在色轮上的位置
    calculatePointPosition: function(hue, saturation) {
      const { centerX, centerY, radius } = this.data;

      // 在标准HSL色轮中：
      // 0度是红色(右侧)，90度是黄色(顶部)，180度是绿色(左侧)，270度是蓝色(底部)
      // 但在canvas坐标系中，0度是在右侧，顺时针旋转
      // 所以我们需要将HSL的角度转换为canvas坐标系的角度

      // 将色相角度转换为弧度，并调整方向
      const angleInRadians = (90 - hue) * Math.PI / 180;

      // 饱和度决定距离中心的距离
      const distance = radius * (saturation / 100);

      // 计算坐标
      const x = centerX + Math.cos(angleInRadians) * distance;
      const y = centerY - Math.sin(angleInRadians) * distance;

      return { x, y };
    },

    // 明度指示器方法 - 仅显示当前颜色的明度，不可交互

    // 更新亮度值
    updateBrightness: function(brightness) {
      // 四舍五入到整数
      brightness = Math.round(brightness);

      if (brightness === this.data.brightness) return;

      this.setData({
        brightness: brightness
      });

      // 更新颜色
      this.updateColorWithBrightness();
    },

    // 移除重复的updateSelectedPoint方法，已在上面定义

    // 更新连线
    updateConnectionLines: function() {
      if (this.data.palettePoints.length === 0) return;

      const connectionLines = [];
      const basePoint = this.data.selectedPoint;
      const centerPoint = { x: this.data.centerX, y: this.data.centerY };
      const palettePoints = this.data.palettePoints;

      // 根据配色方案类型生成不同的连线
      switch(this.properties.paletteType) {
        case 'complementary':
          // 互补色：基础颜色点到互补色点的直线，经过中心点
          if (palettePoints.length > 0) {
            // 基础颜色点到中心点
            connectionLines.push(this.calculateConnectionLine(basePoint, centerPoint, 'base-to-center'));
            // 中心点到互补色点
            connectionLines.push(this.calculateConnectionLine(centerPoint, palettePoints[0], 'center-to-palette'));
          }
          break;

        case 'analogous':
          // 类似色：基础颜色点到中心点，中心点到两个类似色点
          if (palettePoints.length >= 2) {
            // 基础颜色点到中心点
            connectionLines.push(this.calculateConnectionLine(basePoint, centerPoint, 'base-to-center'));
            // 中心点到两个类似色点
            connectionLines.push(this.calculateConnectionLine(centerPoint, palettePoints[0], 'center-to-palette'));
            connectionLines.push(this.calculateConnectionLine(centerPoint, palettePoints[1], 'center-to-palette'));
          }
          break;

        case 'triadic':
          // 三角色：三个颜色点之间形成三角形
          if (palettePoints.length >= 2) {
            // 基础颜色点到两个三角色点
            connectionLines.push(this.calculateConnectionLine(basePoint, palettePoints[0], 'base-to-palette'));
            connectionLines.push(this.calculateConnectionLine(basePoint, palettePoints[1], 'base-to-palette'));
            // 两个三角色点之间
            connectionLines.push(this.calculateConnectionLine(palettePoints[0], palettePoints[1], 'palette-to-palette'));
          }
          break;

        case 'splitComplementary':
          // 分裂互补色：基础颜色点到两个分裂互补色点
          if (palettePoints.length >= 2) {
            // 基础颜色点到中心点
            connectionLines.push(this.calculateConnectionLine(basePoint, centerPoint, 'base-to-center'));
            // 中心点到两个分裂互补色点
            connectionLines.push(this.calculateConnectionLine(centerPoint, palettePoints[0], 'center-to-palette'));
            connectionLines.push(this.calculateConnectionLine(centerPoint, palettePoints[1], 'center-to-palette'));
          }
          break;

        case 'tetradic':
          // 四角色：四个颜色点形成四边形
          if (palettePoints.length >= 3) {
            // 基础颜色点到第一个四角色点
            connectionLines.push(this.calculateConnectionLine(basePoint, palettePoints[0], 'base-to-palette'));
            // 第一个四角色点到第二个四角色点
            connectionLines.push(this.calculateConnectionLine(palettePoints[0], palettePoints[1], 'palette-to-palette'));
            // 第二个四角色点到第三个四角色点
            connectionLines.push(this.calculateConnectionLine(palettePoints[1], palettePoints[2], 'palette-to-palette'));
            // 第三个四角色点到基础颜色点，闭合四边形
            connectionLines.push(this.calculateConnectionLine(palettePoints[2], basePoint, 'palette-to-base'));
          }
          break;

        default:
          // 默认情况：基础颜色点到中心点，中心点到所有配色方案颜色点
          connectionLines.push(this.calculateConnectionLine(basePoint, centerPoint, 'base-to-center'));
          for (let i = 0; i < palettePoints.length; i++) {
            connectionLines.push(this.calculateConnectionLine(centerPoint, palettePoints[i], 'center-to-palette'));
          }
      }

      this.setData({ connectionLines });
    },

    // 更新颜色亮度
    updateColorWithBrightness: function() {
      // 获取当前HSL值 - 保持色相和饱和度不变
      const hsl = colorUtils.hexToHSL(this.properties.selectedColor);

      // 使用新的亮度值生成颜色
      const color = colorUtils.hslToHex(hsl.h, hsl.s, this.data.brightness);

      // 触发颜色变化事件
      this.triggerEvent('colorChange', { color });
    },

    // 颜色转换函数已移至 utils/colorUtils.js,

    // HSL转RGB
    hslToRgb: function(h, s, l) {
      h /= 360;
      s /= 100;
      l /= 100;

      let r, g, b;

      if (s === 0) {
        // 如果饱和度为0，则为灰色
        r = g = b = l;
      } else {
        const hue2rgb = (p, q, t) => {
          if (t < 0) t += 1;
          if (t > 1) t -= 1;
          if (t < 1/6) return p + (q - p) * 6 * t;
          if (t < 1/2) return q;
          if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
          return p;
        };

        const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
        const p = 2 * l - q;

        r = hue2rgb(p, q, h + 1/3);
        g = hue2rgb(p, q, h);
        b = hue2rgb(p, q, h - 1/3);
      }

      return {
        r: Math.round(r * 255),
        g: Math.round(g * 255),
        b: Math.round(b * 255)
      };
    }
  }
});
