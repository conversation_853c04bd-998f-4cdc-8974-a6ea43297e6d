/**
 * 去广告功能工具函数 - 优化版本
 * 使用缓存机制避免重复调用 getStorageSync
 */

const { storageCache } = require('./storageCache');
const logUtils = require('./logUtils');

/**
 * 检查是否已激活去广告功能（带缓存）
 * @returns {boolean} 是否已激活去广告
 */
function isAdFreeActivated() {
  try {
    const adFreeStatus = storageCache.getStorageSync('adFreeStatus', false);
    return adFreeStatus === true;
  } catch (error) {
    logUtils.error('检查去广告状态失败', error);
    return false;
  }
}

/**
 * 异步检查是否已激活去广告功能
 * @returns {Promise<boolean>} 是否已激活去广告
 */
async function isAdFreeActivatedAsync() {
  try {
    const adFreeStatus = await storageCache.getStorage('adFreeStatus', false);
    return adFreeStatus === true;
  } catch (error) {
    logUtils.error('异步检查去广告状态失败', error);
    return false;
  }
}

/**
 * 激活去广告功能
 * @param {string} activationCode 激活码
 * @returns {boolean} 是否激活成功
 */
function activateAdFree(activationCode) {
  if (activationCode === 'KALA666') {
    try {
      storageCache.setStorageSync('adFreeStatus', true);
      logUtils.log('[AdFreeUtils] 去广告功能已激活');
      return true;
    } catch (error) {
      logUtils.error('激活去广告功能失败', error);
      return false;
    }
  }
  return false;
}

/**
 * 异步激活去广告功能
 * @param {string} activationCode 激活码
 * @returns {Promise<boolean>} 是否激活成功
 */
async function activateAdFreeAsync(activationCode) {
  if (activationCode === 'KALA666') {
    try {
      await storageCache.setStorage('adFreeStatus', true);
      logUtils.log('[AdFreeUtils] 去广告功能已异步激活');
      return true;
    } catch (error) {
      logUtils.error('异步激活去广告功能失败', error);
      return false;
    }
  }
  return false;
}

/**
 * 取消激活去广告功能
 * @returns {boolean} 是否取消成功
 */
function deactivateAdFree() {
  try {
    storageCache.setStorageSync('adFreeStatus', false);
    logUtils.log('[AdFreeUtils] 去广告功能已取消激活');
    return true;
  } catch (error) {
    logUtils.error('取消激活去广告功能失败', error);
    return false;
  }
}

/**
 * 异步取消激活去广告功能
 * @returns {Promise<boolean>} 是否取消成功
 */
async function deactivateAdFreeAsync() {
  try {
    await storageCache.setStorage('adFreeStatus', false);
    logUtils.log('[AdFreeUtils] 去广告功能已异步取消激活');
    return true;
  } catch (error) {
    logUtils.error('异步取消激活去广告功能失败', error);
    return false;
  }
}

/**
 * 预加载去广告状态（在应用启动时调用）
 */
async function preloadAdFreeStatus() {
  try {
    await storageCache.getStorage('adFreeStatus', false);
    logUtils.log('[AdFreeUtils] 去广告状态已预加载');
  } catch (error) {
    logUtils.error('预加载去广告状态失败', error);
  }
}

/**
 * 获取去广告功能的统计信息
 * @returns {Object} 统计信息
 */
function getAdFreeStats() {
  return storageCache.getStats();
}

module.exports = {
  isAdFreeActivated,
  isAdFreeActivatedAsync,
  activateAdFree,
  activateAdFreeAsync,
  deactivateAdFree,
  deactivateAdFreeAsync,
  preloadAdFreeStatus,
  getAdFreeStats
};
