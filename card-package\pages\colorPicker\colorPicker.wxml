<!--pages/colorPicker/colorPicker.wxml-->
<view class="page-wrapper">
  <!-- 颜色选择区域 -->
  <view class="colors-section">
    <view class="colors-title" style="margin-top: {{titleMarginTop || '0'}};">
      <view class="title-left">
        <view class="info-dot"></view>
        <view class="title-text">
          <text>选中要编辑的颜色(可拖动排序)，长按图片</text>
          <text>进行取色</text>
        </view>
      </view>
      <view class="algorithm-switch" hover-class="algorithm-switch-hover" bindtap="showAlgorithmSelector">
        <text class="switch-text">切换图片取色算法</text>
      </view>
    </view>
    <view class="colors-row">
      <!-- 颜色加载状态 -->
      <view wx:if="{{colorsLoading}}" class="colors-loading" style="display: flex; justify-content: center; align-items: center; height: 200rpx; flex-direction: column;">
        <view class="loading-spinner" style="width: 60rpx; height: 60rpx; border: 4rpx solid #f3f3f3; border-top: 4rpx solid #3498db; border-radius: 50%; animation: spin 1s linear infinite; margin-bottom: 20rpx;"></view>
        <view style="color: #666; font-size: 28rpx;">正在提取颜色...</view>
      </view>

      <!-- 颜色块显示容器 -->
      <view wx:elif="{{colors && colors.length > 0}}" class="colors-container {{colorsTransitioning ? 'transitioning' : ''}}">
        <!-- 颜色块显示 - 直接在colors-row中水平排列 -->
        <view
          wx:for="{{colors}}"
          wx:key="index"
          class="color-item {{dragIndex === index ? 'dragging' : ''}} {{colorsTransitioning ? 'color-transitioning' : ''}}"
          data-index="{{index}}"
          bindtouchstart="handleColorTouchStart"
          bindtouchmove="handleColorTouchMove"
          bindtouchend="handleColorTouchEnd"
          bindtouchcancel="handleColorTouchCancel"
          style="{{dragIndex === index ? 'z-index: 100; position: relative; left: ' + dragOffsetX + 'px;' : ''}}"
        >
          <!-- 颜色块 -->
          <view
            class="color-block {{selectedColorIndex === index ? 'selected' : ''}}"
            style="background-color: {{item}};"
            bindtap="selectColor"
            data-index="{{index}}"
          >
            <view class="color-number">{{index + 1}}</view>
          </view>

          <!-- 颜色信息 - 仅在选中时显示 -->
          <view class="color-info" wx:if="{{selectedColorIndex === index}}">
            <view class="color-hex" bindtap="copyColorCode" data-color="{{item}}">{{item}}</view>
          </view>
        </view>
      </view>

      <!-- 颜色提取失败状态 -->
      <view wx:else class="colors-error" style="text-align: center; padding: 40rpx; color: #999;">
        <view style="font-size: 32rpx; margin-bottom: 20rpx;">颜色提取中...</view>
        <view style="font-size: 24rpx;">请稍候</view>
      </view>
    </view>
  </view>

  <!-- 图片预览区域 -->
  <view class="image-section">
    <view class="image-container"
      catch:touchstart="handleContainerTouchStart"
      catch:touchmove="handleContainerTouchMove"
      catch:touchend="handleContainerTouchEnd"
      catch:touchcancel="handleContainerTouchCancel">
      <image
        wx:if="{{!croppedImagePath}}"
        class="preview-image original-image"
        src="{{imagePath}}"
        mode="aspectFit"
        binderror="handleImageError"
        bindload="handleImageLoad"
      ></image>
      <image
        wx:if="{{croppedImagePath}}"
        class="preview-image cropped-image"
        src="{{croppedImagePath}}"
        mode="widthFix"
        binderror="handleImageError"
      ></image>
      <view class="image-loading" wx:if="{{imageLoading}}">
        <view class="loading-spinner"></view>
      </view>
      <!-- 放大镜效果 -->
      <view class="magnifier-container" wx:if="{{magnifierVisible}}">
        <!-- 连接线 -->
        <view class="connector-line" style="left: {{targetX}}px; top: {{targetY}}px; width: {{Math.sqrt(Math.pow(magnifierX - targetX, 2) + Math.pow(magnifierY - targetY, 2))}}px; transform: rotate({{Math.atan2(magnifierY - targetY, magnifierX - targetX) * 180 / Math.PI}}deg); transform-origin: 0 50%;"></view>

        <!-- 目标点 -->
        <view class="target-point" style="left: {{targetX}}px; top: {{targetY}}px;"></view>

        <!-- 放大镜 - 使用图片裁剪实现放大效果 -->
        <view class="magnifier" style="left: {{magnifierX}}px; top: {{magnifierY}}px;">
          <view class="magnifier-content">
            <!-- 使用同一张图片，但放大并裁剪到特定位置 -->
            <image
              class="magnifier-image"
              src="{{croppedImagePath || imagePath}}"
              style="width: {{imageRect.width * magnificationRatio}}px; height: {{imageRect.height * magnificationRatio}}px; transform: translate(-{{magnifierOffsetX}}px, -{{magnifierOffsetY}}px);"
            ></image>
            <!-- 十字线 -->
            <view class="magnifier-crosshair-h"></view>
            <view class="magnifier-crosshair-v"></view>
            <!-- 当前颜色指示器 -->
            <view class="color-indicator" style="background-color: {{currentColor || '#ffffff'}};"></view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 颜色信息区域已移除，改为在颜色块下方显示 -->

    <!-- 取色器弹窗已移除 -->

  <view class="btn-container">
    <button class="next-btn" bindtap="goToNext">下一步，制作色卡</button>
  </view>

  <!-- 隐藏的canvas用于分析图片颜色 -->
  <canvas type="2d" id="colorAnalysisCanvas" style="width: 200px; height: 200px; position: absolute; left: -9999px;"></canvas>

  <!-- 隐藏的canvas用于颜色提取 -->
  <canvas type="2d" id="tempCanvas" style="width: 200px; height: 200px; position: absolute; left: -9999px;"></canvas>

  <!-- 隐藏的canvas用于裁剪图片 -->
  <canvas type="2d" id="cropCanvas" style="width: 300px; height: 400px; position: absolute; left: -9999px;"></canvas>

  <!-- 算法选择弹窗 -->
  <view class="algorithm-modal" wx:if="{{showAlgorithmModal}}">
    <view class="algorithm-container">
      <view class="algorithm-header">
        <view class="algorithm-title">选择图片取色算法</view>
        <view class="algorithm-close" hover-class="algorithm-close-hover" bindtap="hideAlgorithmSelector">×</view>
      </view>
      <view class="algorithm-content">
        <view
          wx:for="{{algorithms}}"
          wx:key="id"
          class="algorithm-item {{tempAlgorithm === item.id ? 'selected' : ''}}"
          hover-class="algorithm-item-hover"
          bindtap="selectAlgorithm"
          data-id="{{item.id}}"
        >
          <view class="algorithm-name">{{item.name}}</view>
          <view class="algorithm-desc">{{item.description}} <text>{{item.usage}}</text></view>
        </view>
      </view>
      <view class="algorithm-footer">
        <view class="algorithm-cancel" hover-class="algorithm-cancel-hover" bindtap="hideAlgorithmSelector">取消</view>
        <view class="algorithm-confirm" hover-class="algorithm-confirm-hover" bindtap="confirmAlgorithm">确定</view>
      </view>
    </view>
  </view>
</view>
