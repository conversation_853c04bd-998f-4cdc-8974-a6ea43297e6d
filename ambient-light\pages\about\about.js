// pages/about/about.js
const { storageCache } = require('../../../utils/storageCache');

Page({
  data: {
    // 小程序信息
    appInfo: {
      authorMessage: '作为个人开发者，从功能构思、界面设计、代码编写、到测试优化，每一行代码、每一个按钮、每一处交互细节，都倾注了我的心血与坚持。目前所有功能免费开放，但开发与运营的成本始终存在，为了让小程序能长久陪伴大家，只能通过植入适量广告补贴项目开支。如果您在使用时不介意这些小广告，就是对我最大的支持！\n\n同时，我也非常期待能与你建立更紧密的连接。你的每一条反馈、每一个建议，都是推动小程序进步的宝贵动力。无论是发现了小 Bug，还是对功能有新想法，都欢迎随时通过 [联系作者] 按钮给我留言。未来，我会继续保持对技术的热情，持续迭代更新，让 [KAlA 配色] 变得更好，陪伴你走过更多的时光！'
    },
    // 去广告相关
    showAdFreeModal: false,
    verificationCode: '',
    adFreeStatus: false
  },

  onLoad: function() {
    // 异步初始化去广告状态，避免阻塞首屏渲染
    wx.nextTick(() => {
      this.initAdFreeStatusAsync();
    });
  },

  /**
   * 初始化去广告状态（同步版本，保留兼容性）
   */
  initAdFreeStatus: function() {
    try {
      const adFreeStatus = storageCache.getStorageSync('adFreeStatus');
      this.setData({
        adFreeStatus: adFreeStatus || false
      });
      console.log('[About] 去广告状态初始化完成（使用缓存）');
    } catch (error) {
      console.error('初始化去广告状态失败', error);
      this.setData({
        adFreeStatus: false
      });
    }
  },

  /**
   * 异步初始化去广告状态（优化版本）
   */
  async initAdFreeStatusAsync() {
    try {
      // 使用缓存的异步方式获取存储数据
      const adFreeStatus = await storageCache.getStorage('adFreeStatus', false);

      this.setData({
        adFreeStatus: adFreeStatus || false
      });

      console.log('[About] 去广告状态异步初始化完成（使用缓存）');
    } catch (error) {
      console.error('异步获取去广告状态失败', error);
      this.setData({
        adFreeStatus: false
      });
    }
  },

  /**
   * 显示去广告弹窗
   */
  showAdFreeModal: function() {
    this.setData({
      showAdFreeModal: true,
      verificationCode: ''
    });
  },

  /**
   * 隐藏去广告弹窗
   */
  hideAdFreeModal: function() {
    this.setData({
      showAdFreeModal: false,
      verificationCode: ''
    });
  },

  /**
   * 验证码输入处理
   */
  onVerificationCodeInput: function(e) {
    this.setData({
      verificationCode: e.detail.value
    });
  },

  /**
   * 激活去广告功能
   */
  activateAdFree: function() {
    const verificationCode = this.data.verificationCode.trim();

    if (!verificationCode) {
      wx.showToast({
        title: '请输入验证码',
        icon: 'none',
        duration: 1500
      });
      return;
    }

    if (verificationCode === 'KALA666') {
      // 验证成功
      try {
        storageCache.setStorageSync('adFreeStatus', true);
        this.setData({
          adFreeStatus: true,
          verificationCode: ''
        });

        wx.showToast({
          title: '验证成功，视频广告已去除',
          icon: 'success',
          duration: 2000
        });

        // 延迟关闭弹窗，让用户看到成功状态
        setTimeout(() => {
          this.setData({
            showAdFreeModal: false
          });
        }, 2000);

      } catch (error) {
        console.error('保存去广告状态失败', error);
        wx.showToast({
          title: '验证失败，请重试',
          icon: 'none',
          duration: 1500
        });
      }
    } else {
      // 验证码错误
      wx.showToast({
        title: '验证码错误',
        icon: 'none',
        duration: 1500
      });

      // 清空输入框
      this.setData({
        verificationCode: ''
      });
    }
  },

  /**
   * 取消激活去广告功能
   */
  deactivateAdFree: function() {
    wx.showModal({
      title: '确认取消激活',
      content: '取消激活后，使用系统功能将重新显示视频广告',
      confirmText: '确认',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          try {
            storageCache.setStorageSync('adFreeStatus', false);
            this.setData({
              adFreeStatus: false,
              showAdFreeModal: false
            });

            wx.showToast({
              title: '已取消激活',
              icon: 'success',
              duration: 1500
            });
          } catch (error) {
            console.error('取消激活失败', error);
            wx.showToast({
              title: '操作失败，请重试',
              icon: 'none',
              duration: 1500
            });
          }
        }
      }
    });
  },

  // 用户点击右上角分享或使用分享按钮
  onShareAppMessage: function() {
    return {
      title: 'KALA配色 - 专业的色彩工具小程序',
      path: '/pages/index/index',
      imageUrl: '/assets/images/share.png' // 分享图片，可选
    };
  }
})
