// card-package/pages/quickColorPicker/quickColorPicker.js
const { SafeNavigation } = require('../../../utils/safeNavigation');

Page({
  data: {
    imagePath: '',
    colors: [],
    colorsLoading: false,
    imageLoading: false,
    selectedCount: 0,
    maxColors: 5,
    titleMarginTop: '0rpx',

    // 取色相关状态
    pickingMode: false,
    pickingIndex: -1,
    imageRect: null,

    // 放大镜相关
    magnifierVisible: false,
    currentPickedColor: '#ffffff',
    currentPickedRgb: '',
    imageInfo: null,

    // 图片缩放相关
    scale: 1,
    minScale: 1,
    maxScale: 5,
    translateX: 0,
    translateY: 0,

    // 取色点位置
    pickingX: 0,
    pickingY: 0,
    showCrosshair: false,

    // 放大镜位置（参考主题色提取页面）
    targetX: 0,
    targetY: 0,
    magnifierX: 0,
    magnifierY: 0,

    // Canvas相关
    magnifierCanvas: null,
    magnifierCtx: null,
    offscreenCanvas: null,
    offscreenCtx: null,

    // 触摸交互状态
    lastTapTime: 0,
    isPanning: false,
    isScaling: false,
    isDragging: false,
    isZooming: false
  },

  onLoad(options) {
    // 页面加载标记
    this.isPageLoaded = true;

    // 获取传入的参数
    const maxColors = parseInt(options.maxColors) || 5;

    // 从全局变量中获取图片路径，避免URL过长导致的跳转超时
    const app = getApp();
    const imagePath = (app.globalData && app.globalData.selectedImagePath) || '';

    console.log('onLoad options:', options);
    console.log('maxColors:', maxColors);
    console.log('imagePath from globalData:', imagePath);

    // 清理全局变量
    if (app.globalData) {
      delete app.globalData.selectedImagePath;
    }

    // 安全的setData调用
    this.safeSetData({
      maxColors,
      imagePath
    });

    // 延迟处理图片，避免阻塞页面加载
    if (imagePath) {
      // 先显示页面，再处理图片
      this.safeSetData({
        colorsLoading: true
      });

      // 延迟处理图片，确保页面先完成渲染
      setTimeout(() => {
        if (this.isPageLoaded) {
          this.processImageAfterLoad(imagePath);
        }
      }, 100);
    } else {
      // 没有图片路径，显示选择图片按钮
      this.initDefaultState();
    }

    // 延迟初始化Canvas，确保页面完全渲染
    setTimeout(() => {
      if (this.isPageLoaded) {
        this.initCanvases();
      }
    }, 200);
  },

  /**
   * 页面卸载
   */
  onUnload() {
    console.log('页面卸载，开始清理资源');
    this.isPageLoaded = false;

    // 清理Canvas引用
    this.magnifierCanvas = null;
    this.magnifierCtx = null;
    this.offscreenCanvas = null;
    this.offscreenCtx = null;

    // 清理重试计数器
    this.canvasRetryCount = 0;
    this.offscreenCanvasRetryCount = 0;
    this.magnifierCanvasRetryCount = 0;

    // 清理触摸状态
    this.isDragging = false;
    this.isZooming = false;

    console.log('页面资源清理完成');
  },

  /**
   * 安全的setData方法
   */
  safeSetData(data) {
    // 检查页面实例是否有效
    if (!this || typeof this.setData !== 'function') {
      console.warn('页面实例无效，跳过setData');
      return false;
    }

    // 检查页面是否已加载
    if (!this.isPageLoaded) {
      console.warn('页面未加载完成，跳过setData');
      return false;
    }

    try {
      this.setData(data);
      return true;
    } catch (error) {
      console.error('setData error:', error);
      return false;
    }
  },

  /**
   * 检查页面实例是否有效
   */
  isPageValid() {
    return this && typeof this.setData === 'function' && this.isPageLoaded;
  },

  /**
   * 检查取色功能是否准备就绪
   */
  checkColorPickingReady() {
    // 首先检查页面实例是否有效
    if (!this.isPageValid()) {
      console.warn('页面实例无效');
      return false;
    }

    const checks = {
      imagePath: !!this.data.imagePath,
      imageInfo: !!this.data.imageInfo,
      imageRect: !!this.data.imageRect,
      offscreenCtx: !!this.offscreenCtx,
      magnifierCtx: !!this.magnifierCtx
    };

    console.log('取色功能准备状态检查:', checks);

    const allReady = Object.values(checks).every(ready => ready);

    if (!allReady) {
      const missing = Object.keys(checks).filter(key => !checks[key]);
      console.log('缺少的组件:', missing);
    }

    return allReady;
  },

  /**
   * 初始化默认状态
   */
  initDefaultState() {
    const defaultColors = [
      { color: '#FF6B6B', selected: false },
      { color: '#4ECDC4', selected: false },
      { color: '#45B7D1', selected: false },
      { color: '#96CEB4', selected: false },
      { color: '#FFEAA7', selected: false }
    ];

    this.safeSetData({
      colors: defaultColors,
      selectedCount: 0,
      colorsLoading: false
    });
  },

  /**
   * 页面加载后处理图片
   */
  processImageAfterLoad(imagePath) {
    console.log('开始处理图片:', imagePath);

    // 确保页面已经完全加载
    if (!imagePath) {
      this.initDefaultState();
      return;
    }

    // 先获取图片信息，确保图片信息可用
    wx.getImageInfo({
      src: imagePath,
      success: (res) => {
        console.log('预先获取图片信息成功:', res);
        this.safeSetData({
          imageInfo: res
        });

        // 分步骤处理，避免阻塞
        setTimeout(() => {
          this.extractColorsFromImage(imagePath);
        }, 300);
      },
      fail: (err) => {
        console.error('预先获取图片信息失败:', err);
        // 即使失败也尝试提取颜色
        setTimeout(() => {
          this.extractColorsFromImage(imagePath);
        }, 300);
      }
    });
  },



  /**
   * 选择图片
   */
  chooseImage() {
    wx.chooseImage({
      count: 1,
      sizeType: ['original', 'compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const imagePath = res.tempFilePaths[0];
        this.safeSetData({
          imagePath,
          colorsLoading: true,
          colors: []
        });

        // 延迟提取颜色，确保图片加载完成
        setTimeout(() => {
          this.extractColorsFromImage(imagePath);
        }, 500);
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
        // 返回上一页
        this.goBack();
      }
    });
  },

  /**
   * 图片加载完成
   */
  handleImageLoad() {
    console.log('图片加载完成事件触发');

    this.safeSetData({
      imageLoading: false
    });

    // 确保有图片路径
    if (!this.data.imagePath) {
      console.error('图片路径为空');
      return;
    }

    // 获取图片信息
    wx.getImageInfo({
      src: this.data.imagePath,
      success: (res) => {
        console.log('图片信息获取成功:', res);

        this.safeSetData({
          imageInfo: res
        });

        // 延迟初始化Canvas和获取图片区域，确保DOM完全渲染
        setTimeout(() => {
          this.initCanvases();
          this.getImageRect();
        }, 300);

        // 如果颜色还没有提取或者提取失败，重新提取
        if (!this.data.colors || this.data.colors.length === 0 || this.data.colorsLoading) {
          console.log('图片加载完成，重新提取颜色');
          setTimeout(() => {
            this.extractColorsFromImage(this.data.imagePath);
          }, 500);
        }
      },
      fail: (err) => {
        console.error('获取图片信息失败:', err);
        wx.showToast({
          title: '图片信息获取失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 初始化Canvas
   */
  initCanvases() {
    // 检查页面实例是否有效
    if (!this.isPageValid()) {
      console.warn('页面实例无效，跳过Canvas初始化');
      return;
    }

    console.log('开始初始化Canvas');

    let retryCount = this.canvasRetryCount || 0;

    try {
      // 初始化离屏Canvas
      this.initOffscreenCanvas();

      // 初始化放大镜Canvas
      if (this.data.imagePath) {
        this.initMagnifierCanvas();
      }

      // 重置重试计数
      this.canvasRetryCount = 0;
    } catch (error) {
      console.error('Canvas初始化失败:', error);

      // 增加重试计数
      retryCount++;
      this.canvasRetryCount = retryCount;

      if (retryCount < 3 && this.isPageValid()) {
        // 延迟重试，最多重试3次
        setTimeout(() => {
          if (this.isPageValid()) {
            this.initCanvases();
          }
        }, 300);
      } else if (this.isPageValid()) {
        // 超过重试次数，显示错误
        wx.showToast({
          title: '画布初始化失败，请重新进入页面',
          icon: 'none'
        });
      }
    }
  },

  /**
   * 初始化离屏Canvas
   */
  initOffscreenCanvas() {
    // 增加重试计数器
    const retryCount = this.offscreenCanvasRetryCount || 0;

    const query = wx.createSelectorQuery();
    query.select('#offscreenCanvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        // 检查页面实例是否仍然有效
        if (!this.isPageValid()) {
          console.warn('页面实例在离屏Canvas异步回调中变为无效');
          return;
        }

        if (res[0] && res[0].node) {
          const canvas = res[0].node;
          const ctx = canvas.getContext('2d');

          // 验证Canvas上下文
          if (!ctx || typeof ctx.clearRect !== 'function') {
            console.log('离屏Canvas上下文获取失败，准备重试');
            this.retryOffscreenCanvasInit(retryCount);
            return;
          }

          canvas.width = 10;
          canvas.height = 10;

          this.offscreenCanvas = canvas;
          this.offscreenCtx = ctx;

          // 重置重试计数
          this.offscreenCanvasRetryCount = 0;
          console.log('离屏Canvas初始化成功');
        } else {
          console.log('离屏Canvas节点获取失败，准备重试');
          this.retryOffscreenCanvasInit(retryCount);
        }
      });
  },

  /**
   * 重试离屏Canvas初始化
   */
  retryOffscreenCanvasInit(currentRetryCount) {
    // 检查页面实例是否有效
    if (!this.isPageValid()) {
      console.warn('页面实例无效，停止离屏Canvas重试');
      return;
    }

    const newRetryCount = currentRetryCount + 1;
    this.offscreenCanvasRetryCount = newRetryCount;

    if (newRetryCount < 5) {
      console.log(`离屏Canvas初始化失败，第${newRetryCount}次重试`);
      setTimeout(() => {
        if (this.isPageValid()) {
          this.initOffscreenCanvas();
        }
      }, 500 * newRetryCount);
    } else {
      console.error('离屏Canvas初始化失败，已达到最大重试次数');
      if (this.isPageValid()) {
        wx.showToast({
          title: '取色功能初始化失败',
          icon: 'none'
        });
      }
    }
  },

  /**
   * 初始化放大镜Canvas
   */
  initMagnifierCanvas() {
    if (!this.data.imagePath) {
      console.log('没有图片路径，跳过放大镜Canvas初始化');
      return;
    }

    // 增加重试计数器
    const retryCount = this.magnifierCanvasRetryCount || 0;

    // 添加延迟确保DOM完全渲染，特别是条件渲染的元素
    setTimeout(() => {
      // 检查页面实例是否仍然有效
      if (!this.isPageValid()) {
        console.warn('页面实例在放大镜Canvas延迟初始化中变为无效');
        return;
      }

      const query = wx.createSelectorQuery();
      query.select('#magnifierCanvas')
        .fields({ node: true, size: true })
        .exec((res) => {
          // 再次检查页面实例是否仍然有效
          if (!this.isPageValid()) {
            console.warn('页面实例在放大镜Canvas异步回调中变为无效');
            return;
          }

          if (res[0] && res[0].node) {
            const canvas = res[0].node;
            const ctx = canvas.getContext('2d');

            // 验证Canvas上下文
            if (!ctx || typeof ctx.clearRect !== 'function') {
              console.log('放大镜Canvas上下文获取失败，准备重试');
              this.retryMagnifierCanvasInit(retryCount);
              return;
            }

            const dpr = wx.getSystemInfoSync().pixelRatio || 2;
            canvas.width = 120 * dpr; // 调整为新WXML中的尺寸
            canvas.height = 120 * dpr;
            ctx.scale(dpr, dpr);

            this.magnifierCanvas = canvas;
            this.magnifierCtx = ctx;

            // 重置重试计数
            this.magnifierCanvasRetryCount = 0;
            console.log('放大镜Canvas初始化成功');
          } else {
            console.log('放大镜Canvas节点获取失败，准备重试');
            this.retryMagnifierCanvasInit(retryCount);
          }
        });
    }, 500 + retryCount * 300); // 增加延迟时间，确保DOM完全渲染
  },

  /**
   * 重试放大镜Canvas初始化
   */
  retryMagnifierCanvasInit(currentRetryCount) {
    // 检查页面实例是否有效
    if (!this.isPageValid()) {
      console.warn('页面实例无效，停止放大镜Canvas重试');
      return;
    }

    const newRetryCount = currentRetryCount + 1;
    this.magnifierCanvasRetryCount = newRetryCount;

    if (newRetryCount < 5) {
      console.log(`放大镜Canvas初始化失败，第${newRetryCount}次重试`);
      setTimeout(() => {
        if (this.isPageValid()) {
          this.initMagnifierCanvas();
        }
      }, 500 * newRetryCount);
    } else {
      console.log('放大镜Canvas初始化失败，已达到最大重试次数，将在图片加载后重新尝试');
      // 不显示错误提示，因为这不是致命错误，页面仍可正常使用
    }
  },

  /**
   * 获取图片区域信息
   */
  getImageRect() {
    const query = wx.createSelectorQuery();
    // 使用更通用的选择器，因为新的WXML结构中图片没有ID
    query.select('.preview-image').boundingClientRect();
    query.exec((res) => {
      if (res && res[0]) {
        this.safeSetData({
          imageRect: res[0]
        });
        console.log('图片区域信息获取成功:', res[0]);
      } else {
        console.log('获取图片区域失败，尝试其他选择器');
        // 尝试使用容器选择器
        this.getImageRectFallback();
      }
    });
  },

  /**
   * 备用的图片区域获取方法
   */
  getImageRectFallback() {
    const query = wx.createSelectorQuery();
    query.select('.image-container').boundingClientRect();
    query.exec((res) => {
      if (res && res[0]) {
        this.safeSetData({
          imageRect: res[0]
        });
        console.log('通过容器获取图片区域信息成功:', res[0]);
      } else {
        console.log('获取图片区域失败，请稍后重试');
        wx.showToast({
          title: '获取图片区域失败，请稍后重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 图片加载错误
   */
  handleImageError() {
    wx.showToast({
      title: '图片加载失败',
      icon: 'none'
    });
    this.safeSetData({
      imageLoading: false
    });
  },

  /**
   * 从图片中提取颜色
   */
  extractColorsFromImage(imagePath) {
    if (!imagePath) {
      console.error('图片路径为空');
      this.handleColorExtractionError();
      return;
    }

    console.log('开始提取颜色，图片路径:', imagePath);

    // 获取图片信息
    wx.getImageInfo({
      src: imagePath,
      success: (imageInfo) => {
        console.log('图片信息获取成功:', imageInfo);

        // 延迟创建canvas，避免阻塞
        setTimeout(() => {
          this.createCanvasForColorAnalysis(imageInfo);
        }, 100);
      },
      fail: (err) => {
        console.error('获取图片信息失败:', err);
        this.handleColorExtractionError();
      }
    });
  },

  /**
   * 创建Canvas进行颜色分析
   */
  createCanvasForColorAnalysis(imageInfo) {
    const query = wx.createSelectorQuery();
    query.select('#colorAnalysisCanvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (res[0] && res[0].node) {
          // 延迟分析，避免阻塞UI
          setTimeout(() => {
            this.analyzeImageColors(res[0].node, imageInfo);
          }, 50);
        } else {
          console.error('Canvas节点获取失败');
          this.handleColorExtractionError();
        }
      });
  },

  /**
   * 分析图片颜色
   */
  analyzeImageColors(canvas, imageInfo) {
    try {
      const ctx = canvas.getContext('2d');
      const { width, height } = imageInfo;

      // 设置canvas尺寸，限制大小以提高性能
      const canvasWidth = Math.min(width, 200);
      const canvasHeight = Math.min(height, 200);
      canvas.width = canvasWidth;
      canvas.height = canvasHeight;

      console.log('Canvas尺寸设置:', { canvasWidth, canvasHeight });

      // 创建图片对象
      const img = canvas.createImage();
      const self = this; // 保存this引用

      img.onload = function() {
        try {
          console.log('图片加载成功，开始绘制');

          // 绘制图片到canvas
          ctx.drawImage(img, 0, 0, canvasWidth, canvasHeight);

          // 使用requestAnimationFrame来避免阻塞UI
          setTimeout(() => {
            self.processImageData(ctx, canvasWidth, canvasHeight);
          }, 10);

        } catch (error) {
          console.error('图片绘制失败:', error);
          self.handleColorExtractionError();
        }
      };

      img.onerror = function(error) {
        console.error('图片加载失败:', error);
        self.handleColorExtractionError();
      };

      img.src = imageInfo.path;
    } catch (error) {
      console.error('Canvas初始化失败:', error);
      this.handleColorExtractionError();
    }
  },

  /**
   * 处理图片数据并提取颜色
   */
  processImageData(ctx, canvasWidth, canvasHeight) {
    try {
      console.log('开始处理图片数据');

      // 获取图片像素数据
      const imageData = ctx.getImageData(0, 0, canvasWidth, canvasHeight);
      const pixels = imageData.data;

      // 分批处理像素数据，避免阻塞
      setTimeout(() => {
        this.extractAndSetColors(pixels);
      }, 10);

    } catch (error) {
      console.error('图片数据处理失败:', error);
      this.handleColorExtractionError();
    }
  },

  /**
   * 提取颜色并设置状态
   */
  extractAndSetColors(pixels) {
    try {
      console.log('开始提取主要颜色');

      // 提取主要颜色
      const extractedColors = this.extractMainColors(pixels, 10);

      // 初始化5个颜色槽，前5个使用提取的颜色，其余为默认颜色
      const colors = [];
      for (let i = 0; i < 5; i++) {
        colors.push({
          color: extractedColors[i] || '#CCCCCC',
          selected: false
        });
      }

      // 更新状态
      this.safeSetData({
        colorsLoading: false,
        colors: colors,
        selectedCount: 0  // 初始化选中数量
      });

      console.log('颜色提取完成，提取到的颜色:', colors);

    } catch (error) {
      console.error('颜色提取失败:', error);
      this.handleColorExtractionError();
    }
  },

  /**
   * 提取主要颜色的算法（优化版）
   */
  extractMainColors(pixels, count) {
    const colorMap = new Map();
    const step = 4; // RGBA，每4个值代表一个像素
    const sampleRate = 16; // 增加采样率，提高性能

    console.log('开始颜色统计，像素总数:', pixels.length / 4);

    // 采样像素，统计颜色频率
    for (let i = 0; i < pixels.length; i += step * sampleRate) {
      const r = pixels[i];
      const g = pixels[i + 1];
      const b = pixels[i + 2];
      const a = pixels[i + 3];

      // 跳过透明像素和过于接近白色/黑色的像素
      if (a < 128) continue;
      if (r > 240 && g > 240 && b > 240) continue; // 跳过接近白色
      if (r < 15 && g < 15 && b < 15) continue; // 跳过接近黑色

      // 颜色量化，减少相似颜色
      const quantizedR = Math.floor(r / 32) * 32; // 增加量化步长
      const quantizedG = Math.floor(g / 32) * 32;
      const quantizedB = Math.floor(b / 32) * 32;

      const colorKey = `${quantizedR},${quantizedG},${quantizedB}`;
      colorMap.set(colorKey, (colorMap.get(colorKey) || 0) + 1);
    }

    console.log('颜色统计完成，找到', colorMap.size, '种颜色');

    // 如果颜色太少，降低过滤条件
    if (colorMap.size < count) {
      console.log('颜色数量不足，重新采样');
      return this.extractMainColorsWithLowerThreshold(pixels, count);
    }

    // 按频率排序并转换为hex格式
    const sortedColors = Array.from(colorMap.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, count)
      .map(([colorKey]) => {
        const [r, g, b] = colorKey.split(',').map(Number);
        return '#' + this.rgbToHex(r, g, b);
      })
      .filter((color, index, arr) => arr.indexOf(color) === index); // 去重

    console.log('最终提取的颜色:', sortedColors);
    return sortedColors;
  },

  /**
   * 降低阈值的颜色提取算法
   */
  extractMainColorsWithLowerThreshold(pixels, count) {
    const colorMap = new Map();
    const step = 4;
    const sampleRate = 12; // 更密集的采样

    for (let i = 0; i < pixels.length; i += step * sampleRate) {
      const r = pixels[i];
      const g = pixels[i + 1];
      const b = pixels[i + 2];
      const a = pixels[i + 3];

      // 只跳过完全透明的像素
      if (a < 64) continue;

      // 更宽松的颜色过滤
      if (r > 250 && g > 250 && b > 250) continue; // 只跳过纯白色
      if (r < 5 && g < 5 && b < 5) continue; // 只跳过纯黑色

      // 更细的量化
      const quantizedR = Math.floor(r / 24) * 24;
      const quantizedG = Math.floor(g / 24) * 24;
      const quantizedB = Math.floor(b / 24) * 24;

      const colorKey = `${quantizedR},${quantizedG},${quantizedB}`;
      colorMap.set(colorKey, (colorMap.get(colorKey) || 0) + 1);
    }

    const sortedColors = Array.from(colorMap.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, count)
      .map(([colorKey]) => {
        const [r, g, b] = colorKey.split(',').map(Number);
        return '#' + this.rgbToHex(r, g, b);
      })
      .filter((color, index, arr) => arr.indexOf(color) === index);

    return sortedColors;
  },

  /**
   * RGB转HEX
   */
  rgbToHex(r, g, b) {
    return [r, g, b].map(x => {
      const hex = x.toString(16);
      return hex.length === 1 ? '0' + hex : hex;
    }).join('').toUpperCase();
  },

  /**
   * 处理颜色提取错误
   */
  handleColorExtractionError() {
    console.error('颜色提取失败，使用默认颜色');

    // 即使提取失败，也初始化5个默认颜色
    const colors = [];
    const defaultColors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'];

    for (let i = 0; i < 5; i++) {
      colors.push({
        color: defaultColors[i] || '#CCCCCC',
        selected: false
      });
    }

    this.safeSetData({
      colorsLoading: false,
      colors: colors,
      selectedCount: 0
    });

    wx.showToast({
      title: '颜色提取失败，已使用默认颜色',
      icon: 'none'
    });
  },

  /**
   * 选择图片
   */
  chooseImage() {
    wx.chooseImage({
      count: 1,
      sizeType: ['original', 'compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const imagePath = res.tempFilePaths[0];

        this.safeSetData({
          imagePath,
          colorsLoading: true
        });

        // 延迟处理图片
        setTimeout(() => {
          this.processImageAfterLoad(imagePath);
        }, 100);
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 选择颜色进行取色
   */
  selectColorForPicking(e) {
    console.log('selectColorForPicking called', e);

    const index = e.currentTarget.dataset.index;
    console.log('Selected index for picking:', index);

    if (index === undefined || index === null) {
      console.error('Index is undefined');
      return;
    }

    // 设置当前选中的颜色索引
    this.safeSetData({
      pickingIndex: index
    });

    // 提供触觉反馈
    wx.vibrateShort({
      type: 'light'
    });
  },

  /**
   * 切换颜色选中状态
   */
  toggleColorSelection(e) {
    console.log('toggleColorSelection called', e);

    // 如果正在取色模式，不处理点击
    if (this.data.pickingMode) {
      console.log('In picking mode, ignoring tap');
      return;
    }

    const index = e.currentTarget.dataset.index;
    console.log('Selected index:', index);

    if (index === undefined || index === null) {
      console.error('Index is undefined');
      return;
    }

    const colors = [...this.data.colors];

    if (!colors[index]) {
      console.error('Color at index not found:', index);
      return;
    }

    // 检查是否已达到最大选择数量
    const currentSelectedCount = colors.filter(item => item.selected).length;
    console.log('Current selected count:', currentSelectedCount);
    console.log('Max colors allowed:', this.data.maxColors);
    console.log('Color at index selected:', colors[index].selected);

    if (!colors[index].selected && currentSelectedCount >= this.data.maxColors) {
      console.log('Reached max colors limit');
      wx.showToast({
        title: `最多只能选择${this.data.maxColors}个颜色`,
        icon: 'none'
      });
      return;
    }

    colors[index].selected = !colors[index].selected;
    console.log('Color selected state changed:', colors[index].selected);

    const selectedCount = colors.filter(item => item.selected).length;
    console.log('New selected count:', selectedCount);

    this.safeSetData({
      colors,
      selectedCount
    });

    // 提供触觉反馈
    wx.vibrateShort({
      type: 'light'
    });
  },

  /**
   * 复制颜色代码
   */
  copyColorCode(e) {
    const color = e.currentTarget.dataset.color;
    wx.setClipboardData({
      data: color,
      success: () => {
        wx.showToast({
          title: '颜色代码已复制',
          icon: 'success',
          duration: 1000
        });
      }
    });
  },

  /**
   * 开始颜色拾取
   */
  startColorPicking(e) {
    console.log('startColorPicking called');

    const index = e.currentTarget.dataset.index;
    console.log('Start picking for index:', index);

    // 检查基本条件
    if (!this.data.imagePath) {
      wx.showToast({
        title: '请先选择图片',
        icon: 'none'
      });
      return;
    }

    // 设置取色模式
    this.safeSetData({
      pickingMode: true,
      pickingIndex: index,
      magnifierVisible: true,
      scale: 1,
      translateX: 0,
      translateY: 0,
      showCrosshair: false
    });

    // 检查取色功能是否准备就绪
    if (!this.checkColorPickingReady()) {
      console.log('取色功能未准备就绪，开始初始化');

      wx.showToast({
        title: '正在初始化取色功能...',
        icon: 'loading',
        duration: 2000
      });

      // 强制重新初始化所有组件
      this.forceInitializeColorPicking();
    } else {
      console.log('取色功能已准备就绪');
      wx.showToast({
        title: '点击或拖拽图片取色，可缩放查看',
        icon: 'none',
        duration: 2000
      });
      this.initializeColorPicking();
    }
  },

  /**
   * 强制初始化取色功能
   */
  forceInitializeColorPicking() {
    console.log('强制初始化取色功能');

    // 检查页面实例是否有效
    if (!this.isPageValid()) {
      console.error('页面实例无效，无法进行强制初始化');
      return;
    }

    // 重新获取图片信息
    if (!this.data.imageInfo && this.data.imagePath) {
      wx.getImageInfo({
        src: this.data.imagePath,
        success: (res) => {
          // 再次检查页面实例是否有效
          if (!this.isPageValid()) {
            console.warn('页面实例在异步回调中变为无效');
            return;
          }

          console.log('强制获取图片信息成功:', res);
          if (this.safeSetData({
            imageInfo: res
          })) {
            this.continueForceInit();
          }
        },
        fail: (err) => {
          console.error('强制获取图片信息失败:', err);
          if (this.isPageValid()) {
            wx.showToast({
              title: '图片信息获取失败',
              icon: 'none'
            });
          }
        }
      });
    } else {
      this.continueForceInit();
    }
  },

  /**
   * 继续强制初始化
   */
  continueForceInit() {
    // 检查页面实例是否有效
    if (!this.isPageValid()) {
      console.error('页面实例无效，停止强制初始化');
      return;
    }

    // 重新初始化Canvas
    this.initCanvases();

    // 重新获取图片区域
    setTimeout(() => {
      if (!this.isPageValid()) {
        console.warn('页面实例在延迟回调中变为无效');
        return;
      }

      this.getImageRect();

      // 延迟检查并启动取色
      setTimeout(() => {
        if (!this.isPageValid()) {
          console.warn('页面实例在最终检查时变为无效');
          return;
        }

        if (this.checkColorPickingReady()) {
          console.log('强制初始化成功，启动取色功能');
          this.initializeColorPicking();
        } else {
          console.error('强制初始化失败');
          wx.showToast({
            title: '取色功能初始化失败，请重试',
            icon: 'none'
          });
        }
      }, 1000);
    }, 500);
  },

  /**
   * 初始化取色功能
   */
  initializeColorPicking() {
    console.log('初始化取色功能');

    // 检查页面实例是否有效
    if (!this.isPageValid()) {
      console.error('页面实例无效，无法初始化取色功能');
      return;
    }

    // 检查必要条件
    if (!this.data.imageInfo) {
      console.error('缺少图片信息');
      if (this.isPageValid()) {
        wx.showToast({
          title: '缺少图片信息',
          icon: 'none'
        });
      }
      return;
    }

    if (!this.data.imageRect) {
      console.log('图片区域信息缺失，重新获取');
      this.getImageRect();

      // 延迟重试
      setTimeout(() => {
        if (!this.isPageValid()) {
          console.warn('页面实例在延迟重试中变为无效');
          return;
        }

        if (this.data.imageRect) {
          this.initializeColorPicking();
        } else {
          console.error('获取图片区域失败');
          wx.showToast({
            title: '获取图片区域失败，请重试',
            icon: 'none'
          });
        }
      }, 500);
      return;
    }

    // 初始化放大镜显示图片中心
    const centerX = this.data.imageInfo.width / 2;
    const centerY = this.data.imageInfo.height / 2;

    console.log('初始化放大镜，中心点:', { centerX, centerY });

    // 设置初始放大镜位置（在图片容器中心）
    const imageRect = this.data.imageRect;
    if (imageRect) {
      const initialTargetX = imageRect.width / 2;
      const initialTargetY = imageRect.height / 2;
      const initialMagnifierX = Math.min(initialTargetX + 50, imageRect.width - 80);
      const initialMagnifierY = Math.max(initialTargetY - 100, 80);

      this.safeSetData({
        targetX: initialTargetX,
        targetY: initialTargetY,
        magnifierX: initialMagnifierX,
        magnifierY: initialMagnifierY
      });
    }

    // 延迟执行，确保Canvas完全初始化
    setTimeout(() => {
      if (!this.isPageValid()) {
        console.warn('页面实例在Canvas初始化延迟中变为无效');
        return;
      }

      this.updateMagnifier(centerX, centerY);
      this.getColorFromImage(centerX, centerY);
    }, 300);
  },

  /**
   * 确认选择
   */
  confirmSelection() {
    console.log('confirmSelection called');

    // 获取所有颜色（不再需要选中状态，直接返回所有颜色）
    const allColors = this.data.colors.map(item => item.color);
    console.log('All colors:', allColors);

    if (allColors.length === 0) {
      wx.showToast({
        title: '没有可用的颜色',
        icon: 'none'
      });
      return;
    }

    // 通过事件通信返回颜色
    const eventChannel = this.getOpenerEventChannel();
    if (eventChannel) {
      // 发送选中的颜色事件
      eventChannel.emit('acceptSelectedColors', allColors);
    }

    // 返回上一页
    SafeNavigation.navigateBack();
  },

  /**
   * 返回上一页
   */
  goBack() {
    SafeNavigation.navigateBack();
  },

  /**
   * 复制当前取色的HEX值
   */
  copyPickedColor() {
    const color = this.data.currentPickedColor;
    if (!color) return;

    wx.setClipboardData({
      data: color,
      success: () => {
        wx.showToast({
          title: 'HEX值已复制',
          icon: 'success',
          duration: 1000
        });
      }
    });
  },

  /**
   * 复制当前取色的RGB值
   */
  copyPickedRgb() {
    const rgb = this.data.currentPickedRgb;
    if (!rgb) return;

    wx.setClipboardData({
      data: rgb,
      success: () => {
        wx.showToast({
          title: 'RGB值已复制',
          icon: 'success',
          duration: 1000
        });
      }
    });
  },

  /**
   * 取消取色
   */
  cancelColorPick() {
    this.safeSetData({
      pickingMode: false,
      pickingIndex: -1,
      magnifierVisible: false,
      currentPickedColor: '#ffffff',
      currentPickedRgb: '',
      scale: 1,
      translateX: 0,
      translateY: 0,
      showCrosshair: false
    });
  },

  /**
   * 确认取色
   */
  confirmColorPick() {
    const { currentPickedColor, pickingIndex } = this.data;
    if (pickingIndex < 0 || !currentPickedColor) return;

    const colors = [...this.data.colors];
    colors[pickingIndex].color = currentPickedColor;

    // 计算当前选中数量
    const selectedCount = colors.filter(item => item.selected).length;

    this.safeSetData({
      colors,
      selectedCount,
      pickingMode: false,
      pickingIndex: -1,
      magnifierVisible: false,
      scale: 1,
      translateX: 0,
      translateY: 0,
      showCrosshair: false
    });

    wx.showToast({
      title: '取色成功',
      icon: 'success',
      duration: 1000
    });
  },

  /**
   * 取消取色（兼容旧方法名）
   */
  cancelColorPicking() {
    this.cancelColorPick();
  },

  /**
   * 确认取色（兼容旧方法名）
   */
  confirmColorPicking() {
    this.confirmColorPick();
  },

  /**
   * 处理容器触摸开始（新方法名）
   */
  handleContainerTouchStart(e) {
    if (!this.data.pickingMode) return;
    this.handleImageTouchStart(e);
  },

  /**
   * 处理容器触摸移动（新方法名）
   */
  handleContainerTouchMove(e) {
    if (!this.data.pickingMode) return;
    this.handleImageTouchMove(e);
  },

  /**
   * 处理容器触摸结束（新方法名）
   */
  handleContainerTouchEnd(e) {
    if (!this.data.pickingMode) return;
    this.handleImageTouchEnd(e);
  },

  /**
   * 处理容器触摸取消（新方法名）
   */
  handleContainerTouchCancel(e) {
    if (!this.data.pickingMode) return;
    this.handleImageTouchCancel(e);
  },

  /**
   * 处理图片触摸开始
   */
  handleImageTouchStart(e) {
    if (!this.data.pickingMode) return;

    const touches = e.touches;

    if (touches.length === 1) {
      // 单指操作 - 准备取色或平移
      const touch = touches[0];

      // 记录初始触摸位置，用于判断是否为长按或拖动
      this.touchStartTime = Date.now();
      this.touchStartX = touch.pageX;
      this.touchStartY = touch.pageY;
      this.isDragging = false;
      this.isZooming = false;
      this.isPanning = false;

      // 立即进行取色，提供即时反馈
      this.calculateTouchPosition(touch, true);

      // 设置长按定时器，长按才进入平移模式
      this.longPressTimer = setTimeout(() => {
        // 只有在缩放比例大于1时才允许平移
        if (this.data.scale > 1) {
          this.isPanning = true;

          // 震动反馈，表示进入平移模式
          try {
            wx.vibrateShort({
              type: 'medium'
            });
          } catch (e) {
            console.log('震动反馈失败:', e);
          }

          wx.showToast({
            title: '进入平移模式',
            icon: 'none',
            duration: 1500
          });

          console.log('[QuickColorPicker] 进入平移模式，当前缩放比例:', this.data.scale);
        } else {
          console.log('[QuickColorPicker] 缩放比例不足，无法进入平移模式:', this.data.scale);
        }
      }, 800); // 设置为800ms长按判定，避免误触
    } else if (touches.length === 2) {
      // 双指操作 - 缩放
      // 清除长按定时器
      if (this.longPressTimer) {
        clearTimeout(this.longPressTimer);
        this.longPressTimer = null;
      }

      this.isZooming = true;
      this.isDragging = false;
      this.isPanning = false;

      const touch1 = touches[0];
      const touch2 = touches[1];

      // 计算初始距离
      this.initialDistance = this.getDistance(touch1, touch2);
      this.initialScale = this.data.scale;

      // 计算初始中心点
      this.initialCenterX = (touch1.pageX + touch2.pageX) / 2;
      this.initialCenterY = (touch1.pageY + touch2.pageY) / 2;
    }
  },

  /**
   * 处理图片触摸移动
   */
  handleImageTouchMove(e) {
    if (!this.data.pickingMode) return;

    const touches = e.touches;

    if (touches.length === 1 && !this.isZooming) {
      const touch = touches[0];

      // 计算移动距离
      const deltaX = touch.pageX - this.touchStartX;
      const deltaY = touch.pageY - this.touchStartY;
      const moveDistance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

      // 如果是平移模式（长按后）
      if (this.isPanning && this.data.scale > 1) {
        // 直接计算平移偏移量，避免复杂的边界检查以提高响应速度
        const newTranslateX = this.data.translateX + deltaX / this.data.scale;
        const newTranslateY = this.data.translateY + deltaY / this.data.scale;

        this.safeSetData({
          translateX: newTranslateX,
          translateY: newTranslateY
        });

        // 更新触摸起始位置
        this.touchStartX = touch.pageX;
        this.touchStartY = touch.pageY;

        console.log('[QuickColorPicker] 平移更新:', {
          deltaX: deltaX,
          deltaY: deltaY,
          scale: this.data.scale,
          newTranslateX: newTranslateX,
          newTranslateY: newTranslateY
        });
      }
      // 如果不是平移模式，则进行取色
      else {
        // 只有在移动距离较大时才清除长按定时器
        // 这样可以允许用户在轻微移动时仍然触发长按平移
        if (moveDistance > 10 && this.longPressTimer) {
          clearTimeout(this.longPressTimer);
          this.longPressTimer = null;
          console.log('[QuickColorPicker] 移动距离过大，取消长按检测:', moveDistance);
        }

        // 如果移动距离超过阈值，则进行取色
        if (moveDistance > 2) { // 2像素的移动阈值
          this.isDragging = true;

          // 使用节流函数限制取色频率
          this.throttle(() => {
            this.calculateTouchPosition(touch, true);
          }, 50); // 50ms的节流延迟

          // 更新最后触摸位置
          this.touchStartX = touch.pageX;
          this.touchStartY = touch.pageY;
        }
      }
    } else if (touches.length === 2) {
      // 清除长按定时器
      if (this.longPressTimer) {
        clearTimeout(this.longPressTimer);
        this.longPressTimer = null;
      }

      // 双指缩放
      const touch1 = touches[0];
      const touch2 = touches[1];

      const currentDistance = this.getDistance(touch1, touch2);
      const scaleRatio = currentDistance / this.initialDistance;
      let newScale = this.initialScale * scaleRatio;

      // 限制缩放范围
      newScale = Math.max(this.data.minScale || 1, Math.min(this.data.maxScale || 5, newScale));

      this.safeSetData({
        scale: newScale
      });
    }
  },

  /**
   * 处理图片触摸结束
   */
  handleImageTouchEnd(e) {
    if (!this.data.pickingMode) return;

    // 清除长按定时器
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }

    const touches = e.touches;

    if (touches.length === 0) {
      // 所有手指离开

      // 如果是缩放模式，结束缩放
      if (this.isZooming) {
        this.isZooming = false;

        // 如果缩放比例接近1，自动重置为1并居中
        if (this.data.scale < 1.05 && this.data.scale > 0.95) {
          this.resetScale();
        } else {
          // 缩放结束后，如果有触摸点，进行取色
          if (e.changedTouches && e.changedTouches.length > 0) {
            const touch = e.changedTouches[0];
            setTimeout(() => {
              this.calculateTouchPosition(touch, true);
            }, 50);
          }
        }
        return;
      }

      // 如果是平移模式，结束平移
      if (this.isPanning) {
        this.isPanning = false;

        // 平移结束后，如果有触摸点，进行取色
        if (e.changedTouches && e.changedTouches.length > 0) {
          const touch = e.changedTouches[0];
          setTimeout(() => {
            this.calculateTouchPosition(touch, true);
          }, 50);
        }
        return;
      }

      // 如果是拖拽取色，最后取一次色
      if (this.isDragging && !this.isZooming) {
        const touch = e.changedTouches[0];
        this.calculateTouchPosition(touch, true);
      }

      // 重置状态
      this.isDragging = false;
      this.isZooming = false;
      this.isPanning = false;
    } else if (touches.length === 1 && this.isZooming) {
      // 从双指变为单指，重新开始单指操作
      this.isZooming = false;
      this.isPanning = false;
      const touch = touches[0];
      this.touchStartX = touch.pageX;
      this.touchStartY = touch.pageY;
    }
  },

  /**
   * 重置缩放
   */
  resetScale() {
    this.safeSetData({
      scale: 1,
      translateX: 0,
      translateY: 0
    });
  },

  /**
   * 处理图片触摸取消
   */
  handleImageTouchCancel() {
    if (!this.data.pickingMode) return;

    // 重置所有触摸状态
    this.isDragging = false;
    this.isZooming = false;

    console.log('触摸取消，重置状态');
  },

  /**
   * 计算两点间距离
   */
  getDistance(touch1, touch2) {
    const dx = touch1.pageX - touch2.pageX;
    const dy = touch1.pageY - touch2.pageY;
    return Math.sqrt(dx * dx + dy * dy);
  },

  /**
   * 计算触摸位置并更新放大镜
   */
  calculateTouchPosition(touch, shouldPickColor = true) {
    const { imageInfo, imageRect, scale, translateX, translateY } = this.data;
    if (!imageInfo || !imageRect) {
      console.log('缺少图片信息');
      return;
    }

    try {
      // 计算触摸点相对于容器的位置
      const touchX = touch.pageX - imageRect.left;
      const touchY = touch.pageY - imageRect.top;

      // 计算图片的实际显示尺寸
      const { width: originalWidth, height: originalHeight } = imageInfo;
      const containerWidth = imageRect.width;
      const containerHeight = imageRect.height;

      // 计算图片显示尺寸和偏移量
      const { displayWidth, displayHeight, baseOffsetX, baseOffsetY } =
        this.calculateImageDisplaySize(imageInfo, containerWidth, containerHeight);

      // 考虑缩放和平移的影响 - 使用更精确的计算方法
      const scaledWidth = displayWidth * scale;
      const scaledHeight = displayHeight * scale;

      // 计算容器中心点
      const containerCenterX = containerWidth / 2;
      const containerCenterY = containerHeight / 2;

      // 计算图片中心点在容器中的位置（考虑平移）
      const imageCenterX = containerCenterX + (translateX || 0) * scale;
      const imageCenterY = containerCenterY + (translateY || 0) * scale;

      // 计算触摸点相对于图片中心的偏移
      const relativeToImageCenterX = touchX - imageCenterX;
      const relativeToImageCenterY = touchY - imageCenterY;

      // 计算图片中心在原始图片坐标系中的位置
      const originalCenterX = originalWidth / 2;
      const originalCenterY = originalHeight / 2;

      // 计算触摸点相对于图片中心的比例
      const relativeRatioX = relativeToImageCenterX / (displayWidth * scale / 2);
      const relativeRatioY = relativeToImageCenterY / (displayHeight * scale / 2);

      // 使用比例计算在原始图片上的坐标
      let mappedX = originalCenterX + relativeRatioX * originalWidth / 2;
      let mappedY = originalCenterY + relativeRatioY * originalHeight / 2;

      // 处理边缘情况，确保坐标计算准确
      let x, y;
      if (mappedX < 0) {
        x = 0;
      } else if (mappedX >= originalWidth) {
        x = originalWidth - 1;
      } else {
        x = mappedX;
      }

      if (mappedY < 0) {
        y = 0;
      } else if (mappedY >= originalHeight) {
        y = originalHeight - 1;
      } else {
        y = mappedY;
      }

      // 确保坐标是整数
      x = Math.round(x);
      y = Math.round(y);

      console.log('计算后的图片坐标:', {
        x, y, originalWidth, originalHeight, scale,
        touchX, touchY, imageCenterX, imageCenterY,
        relativeRatioX, relativeRatioY, mappedX, mappedY
      });

      // 计算放大镜位置（参考主题色提取页面的逻辑）
      const magnifierX = Math.min(Math.max(touchX + 50, 80), imageRect.width - 80);
      const magnifierY = Math.max(touchY - 100, 80);

      // 更新十字准星位置和放大镜位置
      this.safeSetData({
        pickingX: touchX,
        pickingY: touchY,
        showCrosshair: true,
        targetX: touchX,
        targetY: touchY,
        magnifierX: magnifierX,
        magnifierY: magnifierY
      });

      // 更新放大镜
      this.updateMagnifier(x, y);

      // 如果需要取色，则获取颜色
      if (shouldPickColor) {
        this.getColorFromImage(x, y);
      }
    } catch (error) {
      console.error('计算触摸位置失败:', error);
    }
  },

  /**
   * 计算图片在容器中的显示尺寸和位置
   */
  calculateImageDisplaySize(imageInfo, containerWidth, containerHeight) {
    const { width: originalWidth, height: originalHeight } = imageInfo;
    const imageRatio = originalWidth / originalHeight;
    const containerRatio = containerWidth / containerHeight;

    let displayWidth, displayHeight, baseOffsetX, baseOffsetY;

    if (imageRatio > containerRatio) {
      // 图片更宽，以宽度为准
      displayWidth = containerWidth;
      displayHeight = containerWidth / imageRatio;
      baseOffsetX = 0;
      baseOffsetY = (containerHeight - displayHeight) / 2;
    } else {
      // 图片更高，以高度为准
      displayHeight = containerHeight;
      displayWidth = containerHeight * imageRatio;
      baseOffsetX = (containerWidth - displayWidth) / 2;
      baseOffsetY = 0;
    }

    return { displayWidth, displayHeight, baseOffsetX, baseOffsetY };
  },

  /**
   * 更新放大镜显示
   */
  updateMagnifier(x, y) {
    const { imageInfo, scale } = this.data;
    if (!imageInfo) return;

    try {
      // 获取放大镜Canvas上下文
      const ctx = this.getMagnifierContext();

      // 如果上下文无效，直接返回，不显示错误
      if (!ctx) {
        return;
      }

      // 获取放大镜Canvas的尺寸
      const magnifierSize = 120; // 固定尺寸
      const radius = magnifierSize / 2;

      // 再次验证上下文（可能在异步过程中失效）
      if (!ctx || typeof ctx.clearRect !== 'function') {
        // 静默处理，不显示错误日志，避免频繁输出
        return;
      }
      ctx.clearRect(0, 0, magnifierSize, magnifierSize);

      // 绘制圆形裁剪区域
      ctx.save();
      ctx.beginPath();
      ctx.arc(radius, radius, radius, 0, Math.PI * 2);
      ctx.clip();

      // 计算放大镜的缩放比例
      // 根据当前图片缩放比例动态调整放大镜的放大倍数
      const baseMagnification = 3.0; // 基础放大倍数
      const scaleFactor = Math.max(1, scale); // 确保缩放因子至少为1
      const magnifierScale = baseMagnification * scaleFactor; // 动态调整放大倍数

      const sourceSize = magnifierSize / magnifierScale;
      const sourceX = x - sourceSize / 2;
      const sourceY = y - sourceSize / 2;

      console.log('放大镜参数:', {
        x, y,
        sourceX, sourceY,
        sourceSize,
        magnifierSize,
        scale,
        magnifierScale,
        magnificationRatio: magnifierScale
      });

      // 创建图片对象用于Canvas 2D API
      const img = this.magnifierCanvas.createImage();
      img.onload = () => {
        try {
          // 绘制图片到放大镜
          ctx.drawImage(
            img,
            sourceX,
            sourceY,
            sourceSize,
            sourceSize,
            0,
            0,
            magnifierSize,
            magnifierSize
          );

          // 绘制十字线 - 参考主题色提取页面的样式
          ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
          ctx.lineWidth = 1;
          ctx.beginPath();
          ctx.moveTo(radius, 0);
          ctx.lineTo(radius, magnifierSize);
          ctx.moveTo(0, radius);
          ctx.lineTo(magnifierSize, radius);
          ctx.stroke();

          // 绘制中心点
          ctx.beginPath();
          ctx.arc(radius, radius, 2, 0, Math.PI * 2);
          ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
          ctx.fill();

          // 恢复上下文
          ctx.restore();

          // 绘制边框
          ctx.strokeStyle = 'rgba(0, 0, 0, 0.3)';
          ctx.lineWidth = 1;
          ctx.beginPath();
          ctx.arc(radius, radius, radius, 0, Math.PI * 2);
          ctx.stroke();

          console.log('放大镜更新成功:', { x, y, sourceX, sourceY, sourceSize });
        } catch (drawError) {
          console.error('放大镜绘制失败:', drawError);
        }
      };
      img.onerror = () => {
        console.log('放大镜图片加载失败');
      };
      img.src = imageInfo.path || this.data.imagePath;
    } catch (error) {
      // 更新放大镜失败，静默处理
      console.log('放大镜更新错误:', error);
    }
  },

  /**
   * 获取放大镜Canvas上下文
   */
  getMagnifierContext() {
    if (!this.magnifierCtx && this.magnifierCanvas) {
      try {
        this.magnifierCtx = this.magnifierCanvas.getContext('2d');
      } catch (error) {
        console.log('获取放大镜Canvas上下文失败:', error);
        return null;
      }
    }
    return this.magnifierCtx;
  },

  /**
   * 从图片获取指定位置的颜色
   */
  getColorFromImage(x, y) {
    const { imagePath, imageInfo } = this.data;
    if (!imagePath || !imageInfo) {
      console.error('缺少图片信息');
      return;
    }

    try {
      // 检查坐标是否在图片范围内
      const isOutOfBounds = x < 0 || y < 0 || x >= imageInfo.width || y >= imageInfo.height;

      // 如果坐标超出图片范围，使用最近的边缘像素
      const safeX = Math.max(0, Math.min(x, imageInfo.width - 1));
      const safeY = Math.max(0, Math.min(y, imageInfo.height - 1));

      console.log('取色坐标:', {
        originalX: x,
        originalY: y,
        safeX: safeX,
        safeY: safeY,
        imageWidth: imageInfo.width,
        imageHeight: imageInfo.height,
        isOutOfBounds: isOutOfBounds
      });

      // 如果坐标超出图片范围，直接使用边缘像素
      if (isOutOfBounds) {
        this.getEdgePixelColor(safeX, safeY);
        return;
      }

      // 在离屏Canvas上绘制图片的指定区域
      const ctx = this.getOffscreenContext();
      if (!ctx) {
        // 上下文无效，使用备用方案
        this.useFixedEdgeColor(x, y);
        return;
      }
      ctx.clearRect(0, 0, 10, 10);

      // 计算源区域，确保不会超出图片边界
      let sourceX = safeX - 1;
      let sourceY = safeY - 1;
      let sourceWidth = 3;
      let sourceHeight = 3;
      let destX = 0;
      let destY = 0;

      // 处理左边界
      if (sourceX < 0) {
        destX = Math.abs(sourceX);
        sourceWidth -= destX;
        sourceX = 0;
      }

      // 处理上边界
      if (sourceY < 0) {
        destY = Math.abs(sourceY);
        sourceHeight -= destY;
        sourceY = 0;
      }

      // 处理右边界
      if (sourceX + sourceWidth > imageInfo.width) {
        sourceWidth = imageInfo.width - sourceX;
      }

      // 处理下边界
      if (sourceY + sourceHeight > imageInfo.height) {
        sourceHeight = imageInfo.height - sourceY;
      }

      // 创建图片对象用于Canvas 2D API
      const img = this.offscreenCanvas.createImage();
      img.onload = () => {
        // 绘制图片的指定区域
        if (sourceWidth > 0 && sourceHeight > 0) {
          try {
            ctx.drawImage(
              img,
              sourceX,
              sourceY,
              sourceWidth,
              sourceHeight,
              destX,
              destY,
              sourceWidth,
              sourceHeight
            );

            // 获取像素数据 - 直接获取中心点的颜色
            try {
              const imageData = ctx.getImageData(1, 1, 1, 1);
              const data = imageData.data;

              // 提取RGB值
              const r = data[0];
              const g = data[1];
              const b = data[2];

              // 转换为HEX格式
              const hexColor = '#' + this.rgbToHex(r, g, b);
              const rgbColor = `rgb(${r},${g},${b})`;

              // 更新颜色值
              this.safeSetData({
                currentPickedColor: hexColor,
                currentPickedRgb: rgbColor
              });

              console.log('取色成功:', { hexColor, rgbColor });
            } catch (error) {
              console.error('获取像素数据失败:', error);
              this.getEdgePixelColor(safeX, safeY);
            }
          } catch (err) {
            console.error('绘制图片区域失败:', err);
            this.useFixedEdgeColor(x, y);
          }
        } else {
          console.error('无效的源区域尺寸');
          this.useFixedEdgeColor(x, y);
        }
      };
      img.onerror = () => {
        console.error('图片加载失败');
        this.useFixedEdgeColor(x, y);
      };
      img.src = imagePath;
    } catch (error) {
      // 取色失败，使用备用方案
      this.useFixedEdgeColor(x, y);

      // 显示轻量级错误提示
      wx.showToast({
        title: '取色遇到问题，已使用备用颜色',
        icon: 'none',
        duration: 1500
      });
    }
  },

  /**
   * 获取离屏Canvas上下文
   */
  getOffscreenContext() {
    if (!this.offscreenCtx && this.offscreenCanvas) {
      try {
        this.offscreenCtx = this.offscreenCanvas.getContext('2d');
      } catch (error) {
        console.log('获取离屏Canvas上下文失败:', error);
        return null;
      }
    }
    return this.offscreenCtx;
  },

  /**
   * 获取边缘像素颜色 - 备用方法
   * 优化版本，使用小尺寸Canvas，只绘制需要的区域
   */
  getEdgePixelColor(x, y) {
    const { imagePath, imageInfo } = this.data;
    if (!imagePath || !imageInfo) return;

    try {
      // 在离屏Canvas上绘制图片的边缘区域
      const ctx = this.getOffscreenContext();

      // 验证上下文
      if (!ctx) {
        // 上下文无效，使用固定颜色
        this.useFixedEdgeColor(x, y);
        return;
      }
      ctx.clearRect(0, 0, 10, 10);

      // 计算源区域 - 只绘制边缘附近的小区域
      // 使用5x5的区域，确保有足够的像素可以取色
      const size = 5;
      const halfSize = Math.floor(size / 2);

      // 计算源区域的左上角坐标，确保不会超出图片边界
      const sourceX = Math.max(0, x - halfSize);
      const sourceY = Math.max(0, y - halfSize);

      // 计算源区域的宽高，确保不会超出图片边界
      const sourceWidth = Math.min(size, imageInfo.width - sourceX);
      const sourceHeight = Math.min(size, imageInfo.height - sourceY);

      // 计算目标区域的左上角坐标
      const destX = x - sourceX;
      const destY = y - sourceY;

      console.log('边缘取色区域:', {
        sourceX, sourceY,
        sourceWidth, sourceHeight,
        destX, destY,
        x, y
      });

      // 创建图片对象用于Canvas 2D API
      const img = this.offscreenCanvas.createImage();
      img.onload = () => {
        // 绘制图片的边缘区域
        ctx.drawImage(
          img,
          sourceX,
          sourceY,
          sourceWidth,
          sourceHeight,
          0,
          0,
          sourceWidth,
          sourceHeight
        );

        // 获取指定像素的颜色 - 使用相对于Canvas的坐标
        try {
          const pixelX = Math.min(destX, sourceWidth - 1);
          const pixelY = Math.min(destY, sourceHeight - 1);
          const imageData = ctx.getImageData(pixelX, pixelY, 1, 1);
          const data = imageData.data;

          // 提取RGB值
          const r = data[0];
          const g = data[1];
          const b = data[2];

          // 转换为HEX格式
          const hexColor = '#' + this.rgbToHex(r, g, b);
          const rgbColor = `rgb(${r},${g},${b})`;

          // 更新颜色值
          this.safeSetData({
            currentPickedColor: hexColor,
            currentPickedRgb: rgbColor
          });

          console.log('边缘取色成功:', { hexColor, rgbColor });
        } catch (err) {
          console.error('边缘取色失败:', err);
          // 如果边缘取色失败，使用固定的边缘颜色
          this.useFixedEdgeColor(x, y);
        }
      };
      img.onerror = () => {
        console.error('边缘取色图片加载失败');
        this.useFixedEdgeColor(x, y);
      };
      img.src = imagePath;
    } catch (error) {
      // 边缘取色失败，静默处理
      console.log('边缘取色错误详情:', error);

      // 如果边缘取色失败，使用固定的边缘颜色
      this.useFixedEdgeColor(x, y);

      // 不显示错误提示，因为这是备用方法，已经有主方法的错误提示了
    }
  },

  /**
   * 使用固定的边缘颜色 - 最后的备用方法
   */
  useFixedEdgeColor(x, y) {
    const { imageInfo } = this.data;
    if (!imageInfo) return;

    // 根据坐标位置，确定使用哪个边缘的颜色
    let edgeColor;

    // 左边缘
    if (x <= 0) {
      edgeColor = '#000000'; // 黑色
    }
    // 右边缘
    else if (x >= imageInfo.width - 1) {
      edgeColor = '#FFFFFF'; // 白色
    }
    // 上边缘
    else if (y <= 0) {
      edgeColor = '#808080'; // 灰色
    }
    // 下边缘
    else if (y >= imageInfo.height - 1) {
      edgeColor = '#C0C0C0'; // 银色
    }
    // 默认
    else {
      edgeColor = '#FF0000'; // 红色
    }

    // 更新颜色值
    this.safeSetData({
      currentPickedColor: edgeColor,
      currentPickedRgb: this.hexToRgb(edgeColor)
    });

    console.log('使用固定边缘颜色:', edgeColor);
  },

  /**
   * HEX转RGB
   */
  hexToRgb(hex) {
    // 移除#号
    hex = hex.replace(/^#/, '');

    // 解析RGB值
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    return `rgb(${r},${g},${b})`;
  },

  /**
   * 节流函数
   */
  throttle(func, delay) {
    if (!this.throttleTimer) {
      this.throttleTimer = {};
    }

    const key = func.toString();
    if (this.throttleTimer[key]) return;

    this.throttleTimer[key] = setTimeout(() => {
      func();
      delete this.throttleTimer[key];
    }, delay);
  }
})