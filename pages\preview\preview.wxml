<!--pages/preview/preview.wxml-->
<view class="page-wrapper">
  <view class="scroll-area" style="padding-top: {{scrollPaddingTop || '0'}}">
    <view class="card-container">
      <view class="color-card">
        <!-- 加载状态指示器 - 当色卡正在生成时显示 -->
        <view wx:if="{{!cardPath && imagePath}}" class="loading-container">
          <view class="loading-spinner"></view>
          <text class="loading-text">制作中...</text>
        </view>

        <!-- 直接显示生成的配色卡图片 -->
        <image
          wx:if="{{cardPath}}"
          class="card-image"
          src="{{cardPath}}"
          mode="widthFix"
          bindtap="previewImage"
        ></image>
      </view>

    <!-- 使用条件渲染实现用时注入 - 只渲染当前需要的组件 -->
    <!-- 自定义色卡模板 -->
    <block wx:if="{{isCustom && colors && colors.length > 0 && pageReady}}">
      <!-- 蜜桃汽水模板 - ID 101 -->
      <view wx:if="{{templateId == 101}}" id="cardContainer">
        <color-card-custom-p01
          colors="{{colors}}"
          title="{{customTitle}}"
          subTitle="{{customSubTitle}}"
          topBackgroundColor="{{customTopBackgroundColor}}"
          bottomBackgroundColor="{{customBottomBackgroundColor}}"
          fontColor="{{customFontColor}}"
          bindgenerated="onCardGenerated"
          style="position: absolute; left: -9999px; width: 1px; height: 1px; overflow: hidden;"
        ></color-card-custom-p01>
      </view>

      <!-- 海盐气泡模板 - ID 102 -->
      <view wx:elif="{{templateId == 102}}" id="cardContainer">
        <color-card-custom-p02
          colors="{{colors}}"
          title="{{customTitle}}"
          subTitle="{{customSubTitle}}"
          backgroundColor="{{customBackgroundColor}}"
          titleColor="{{customTitleColor}}"
          bindgenerated="onCardGenerated"
          style="position: absolute; left: -9999px; width: 1px; height: 1px; overflow: hidden;"
        ></color-card-custom-p02>
      </view>

      <!-- 落日漫旅模板 - ID 103 -->
      <view wx:elif="{{templateId == 103}}" id="cardContainer">
        <color-card-custom-p03
          colors="{{colors}}"
          title="{{customTitle}}"
          subTitle="{{customSubTitle}}"
          topBackgroundColor="{{customTopBackgroundColor}}"
          bottomBackgroundColor="{{customBottomBackgroundColor}}"
          fontColor="{{customFontColor}}"
          codeColor="{{customCodeColor}}"
          bindgenerated="onCardGenerated"
          style="position: absolute; left: -9999px; width: 1px; height: 1px; overflow: hidden;"
        ></color-card-custom-p03>
      </view>

      <!-- 春日樱语模板 - ID 104 -->
      <view wx:elif="{{templateId == 104}}" id="cardContainer">
        <color-card-custom-p04
          colors="{{colors}}"
          title="{{customTitle}}"
          subTitle="{{customSubTitle}}"
          topBackgroundColor="{{customTopBackgroundColor}}"
          bottomBackgroundColor="{{customBottomBackgroundColor}}"
          fontColor="{{customFontColor}}"
          bindgenerated="onCardGenerated"
          style="position: absolute; left: -9999px; width: 1px; height: 1px; overflow: hidden;"
        ></color-card-custom-p04>
      </view>
    </block>

    <!-- 图片主题色模板 -->
    <block wx:elif="{{!isCustom && imagePath && colors && colors.length > 0 && pageReady}}">
      <!-- 色卡A (1:1)模板 - ID 3 -->
      <view wx:if="{{templateId == 3}}" id="cardContainer">
        <color-card-a
          colors="{{colors}}"
          imagePath="{{imagePath}}"
          bindgenerated="onCardGenerated"
          style="position: absolute; left: -9999px; width: 1px; height: 1px; overflow: hidden;"
        ></color-card-a>
      </view>

      <!-- 色卡B (4:3)模板 - ID 4 -->
      <view wx:elif="{{templateId == 4}}" id="cardContainer">
        <color-card-b
          colors="{{colors}}"
          imagePath="{{imagePath}}"
          bindgenerated="onCardGenerated"
          style="position: absolute; left: -9999px; width: 1px; height: 1px; overflow: hidden;"
        ></color-card-b>
      </view>

      <!-- 色卡C (3:4)模板 - ID 5 -->
      <view wx:elif="{{templateId == 5}}" id="cardContainer">
        <color-card-c
          colors="{{colors}}"
          imagePath="{{imagePath}}"
          bindgenerated="onCardGenerated"
          style="position: absolute; left: -9999px; width: 1px; height: 1px; overflow: hidden;"
        ></color-card-c>
      </view>

      <!-- 色卡D (3:4)模板 - ID 6 -->
      <view wx:elif="{{templateId == 6}}" id="cardContainer">
        <color-card-d
          colors="{{colors}}"
          imagePath="{{imagePath}}"
          bindgenerated="onCardGenerated"
          style="position: absolute; left: -9999px; width: 1px; height: 1px; overflow: hidden;"
        ></color-card-d>
      </view>

      <!-- 色卡E (3:4)模板 - ID 7 -->
      <view wx:elif="{{templateId == 7}}" id="cardContainer">
        <color-card-e
          colors="{{colors}}"
          imagePath="{{imagePath}}"
          bindgenerated="onCardGenerated"
          style="position: absolute; left: -9999px; width: 1px; height: 1px; overflow: hidden;"
        ></color-card-e>
      </view>

      <!-- 色卡F (16:9)模板 - ID 8 -->
      <view wx:elif="{{templateId == 8}}" id="cardContainer">
        <color-card-f
          colors="{{colors}}"
          imagePath="{{imagePath}}"
          bindgenerated="onCardGenerated"
          style="position: absolute; left: -9999px; width: 1px; height: 1px; overflow: hidden;"
        ></color-card-f>
      </view>

      <!-- 色卡G (4:5)模板 - ID 9 -->
      <view wx:elif="{{templateId == 9}}" id="cardContainer">
        <color-card-g
          colors="{{colors}}"
          imagePath="{{imagePath}}"
          bindgenerated="onCardGenerated"
          style="position: absolute; left: -9999px; width: 1px; height: 1px; overflow: hidden;"
        ></color-card-g>
      </view>

      <!-- 色卡H (9:16)模板 - ID 10 -->
      <view wx:elif="{{templateId == 10}}" id="cardContainer">
        <color-card-h
          colors="{{colors}}"
          imagePath="{{imagePath}}"
          bindgenerated="onCardGenerated"
          style="position: absolute; left: -9999px; width: 1px; height: 1px; overflow: hidden;"
        ></color-card-h>
      </view>

      <!-- 色卡I (9:16)模板 - ID 11 -->
      <view wx:elif="{{templateId == 11}}" id="cardContainer">
        <color-card-i
          colors="{{colors}}"
          imagePath="{{imagePath}}"
          bindgenerated="onCardGenerated"
          style="position: absolute; left: -9999px; width: 1px; height: 1px; overflow: hidden;"
        ></color-card-i>
      </view>
    </block>


  </view>

    <!-- 色卡信息提示 -->
    <view class="card-info" wx:if="{{cardPath}}">
      <view class="info-text-simple">点击图片可查看大图</view>
    </view>
  </view>

  <view class="btn-container">
    <button class="save-btn" bindtap="saveToAlbum">保存到相册</button>
    <button class="home-btn" bindtap="goToHome">返回首页</button>
  </view>

  <!-- 保存成功提示 -->
  <view class="save-success {{showSaveSuccess ? 'show' : ''}}">
    <view class="success-icon"></view>
    <view class="success-text">色卡已保存到相册</view>
  </view>
</view>
