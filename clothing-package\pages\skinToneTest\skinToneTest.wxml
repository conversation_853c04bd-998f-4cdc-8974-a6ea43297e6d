<!--pages/skinToneTest/skinToneTest.wxml-->
<view class="page">
  <view class="container">
    <!-- 使用说明 -->
    <view class="section">
      <view class="section-title">使用说明</view>
      <view class="instruction-list">
        <view class="instruction-item">
          <text class="instruction-number">1</text>
          <text class="instruction-text">准备一张<text class="highlight-green">原相机照片</text>（室内自然光拍摄，衣服和背景最好是白色或浅色）</text>
        </view>
        <view class="instruction-item">
          <text class="instruction-number">2</text>
          <text class="instruction-text">点击"<text class="highlight-green">添加肤色</text>"按钮吸取照片脸颊的颜色（肤色不均匀可以吸取多个颜色，程序会自动计算其平均色值）</text>
        </view>
        <view class="instruction-item">
          <text class="instruction-number">3</text>
          <text class="instruction-text">点击"<text class="highlight-green">生成测试报告</text>"按钮后，查看个人肤色及与肤色色卡的相似度分析结果</text>
        </view>
      </view>
    </view>

    <!-- 图片吸取肤色预览 -->
    <view class="section">
      <view class="section-title">图片吸取肤色</view>
      <view class="section-subtitle">最多可添加3个肤色，系统会自动计算<text class="highlight-green">平均肤色</text></view>

      <!-- 多肤色预览区域 -->
      <view class="multi-color-container">
        <view class="color-list">
          <view
            class="color-item"
            wx:for="{{selectedColors}}"
            wx:key="index"
            data-index="{{index}}"
          >
            <view class="color-preview-small" style="background-color: {{item}};" bindtap="editColor" data-index="{{index}}">
              <view class="color-hex-small" style="color: {{colorTextColors[index]}};">{{item}}</view>
            </view>
            <view class="color-actions-small">
              <view class="action-btn-small" bindtap="copyColorCode" data-color="{{item}}">
                <text class="action-text-small">复制</text>
              </view>
              <view class="action-btn-small delete" bindtap="removeColor" data-index="{{index}}">
                <text class="action-text-small">删除</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 添加肤色按钮 -->
        <view class="add-color-container" wx:if="{{selectedColors.length < 3}}">
          <view class="add-color-btn" bindtap="addColor">
            <text class="add-color-icon">+</text>
            <text class="add-color-text">添加肤色</text>
          </view>
        </view>


      </view>
    </view>

    <!-- 色卡1 -->
    <view class="section">
      <view class="section-header">
        <view class="section-title">色卡1</view>
        <view class="section-actions">
          <view class="reset-btn" bindtap="resetSkinToneCards">重置</view>
        </view>
      </view>
      <view class="section-subtitle">点击颜色（如：偏冷暖一白）可进行编辑</view>
      <view class="skin-tone-grid">
        <view
          class="skin-tone-item editable"
          wx:for="{{skinToneColorsOne}}"
          wx:key="name"
          bindtap="editSkinToneCard"
          data-type="one"
          data-index="{{index}}"
        >
          <view class="skin-tone-color" style="background-color: {{item.color}};"></view>
          <view class="skin-tone-name">{{item.name}}</view>
          <view class="edit-indicator">✎</view>
        </view>
      </view>
    </view>

    <!-- 色卡2 -->
    <view class="section">
      <view class="section-header">
        <view class="section-title">色卡2</view>
      </view>
      <view class="section-subtitle">点击颜色（如：粉一白）可进行编辑</view>
      <view class="skin-tone-grid">
        <view
          class="skin-tone-item editable"
          wx:for="{{skinToneColorsTwo}}"
          wx:key="name"
          bindtap="editSkinToneCard"
          data-type="two"
          data-index="{{index}}"
        >
          <view class="skin-tone-color" style="background-color: {{item.color}};"></view>
          <view class="skin-tone-name">{{item.name}}</view>
          <view class="edit-indicator">✎</view>
        </view>
      </view>
    </view>

  </view>

  <!-- 固定在底部的生成报告按钮 -->
  <view class="btn-container">
    <button class="generate-btn" bindtap="generateReport">生成测试报告</button>
  </view>

  <!-- 颜色选择器弹窗 -->
  <view class="color-picker-modal" wx:if="{{showColorPicker}}">
    <view class="color-picker-container">
      <view class="color-picker-header">
        <view class="color-picker-title">{{editingIndex >= 0 ? '编辑肤色' : '添加肤色'}}</view>
        <view class="color-picker-close" bindtap="hideColorPicker">×</view>
      </view>
      <!-- 集成颜色选择器组件 - 用时注入 -->
      <color-picker
        wx:if="{{showColorPicker}}"
        color="{{selectedColor}}"
        bindchange="onColorPickerChange"
        bindconfirm="onColorPickerConfirm"
        bindcancel="hideColorPicker"
      ></color-picker>
    </view>
  </view>

  <!-- 色卡编辑弹窗 -->
  <view class="card-edit-modal" wx:if="{{showCardEditModal}}">
    <view class="card-edit-container">
      <view class="card-edit-header">
        <view class="card-edit-title">编辑色卡</view>
        <view class="card-edit-close" bindtap="hideCardEditModal">×</view>
      </view>

      <view class="card-edit-content">
        <view class="edit-field">
          <view class="edit-label">颜色名称</view>
          <input
            class="edit-input"
            placeholder="请输入颜色名称"
            value="{{editingCardName}}"
            bindinput="onEditNameInput"
            maxlength="20"
          />
        </view>

        <view class="edit-field">
          <view class="edit-label">颜色值</view>
          <view class="color-input-row">
            <view class="color-input-container">
              <input
                class="edit-input color-input"
                placeholder="请输入颜色值 (如: #F5EBE2)"
                value="{{editingCardColor}}"
                bindinput="onEditColorInput"
                bindconfirm="onEditColorConfirm"
                maxlength="7"
              />
              <view class="color-preview" style="background-color: {{editingCardColor}};"></view>
            </view>
            <!-- 图片取色按钮 -->
            <view class="image-pick-btn" bindtap="pickCardColorFromImage">图片取色</view>
          </view>

          <!-- 小字说明 -->
          <view class="color-input-tip">点击"图片取色"按钮可从图片中选择颜色</view>
        </view>
      </view>

      <view class="card-edit-footer">
        <view class="card-edit-cancel" bindtap="hideCardEditModal">取消</view>
        <view class="card-edit-confirm" bindtap="saveCardEdit">保存</view>
      </view>
    </view>
  </view>
</view>
