/* pages/template/template.wxss */
.page-wrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  box-sizing: border-box;
  background-color: #ffffff;
}

/* 模式切换样式 - 幽灵按钮风格 */
.mode-switch-container {
  padding: 15rpx 30rpx;
  margin-bottom: 20rpx;
}

.mode-switch {
  display: flex;
  background-color: transparent;
  border-radius: 0;
  padding: 0;
  position: relative;
  gap: 0; /* 确保按钮紧挨着 */
}

.mode-switch-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 24rpx;
  font-size: 28rpx;
  color: #666666;
  border-radius: 8rpx;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
  border: 2rpx solid #e0e0e0; /* 每个按钮独立边框 */
  background-color: transparent;
  font-weight: 400;
  margin-right: -2rpx; /* 重叠边框，避免双重边框 */
}

.mode-switch-item:last-child {
  margin-right: 0; /* 最后一个按钮不需要右边距 */
}

.mode-switch-item:first-child {
  border-radius: 8rpx 0 0 8rpx; /* 左侧圆角 */
}

.mode-switch-item:last-child {
  border-radius: 0 8rpx 8rpx 0; /* 右侧圆角 */
}

.mode-switch-item.active {
  background-color: transparent;
  color: #07c160; /* 绿色文字 */
  font-weight: 600; /* 加粗字体 */
  border: 2rpx solid #07c160; /* 选中按钮的绿色边框 */
  box-shadow: none;
  z-index: 3; /* 确保选中按钮在最上层 */
}

/* 悬停效果（如果需要） */
.mode-switch-item:active {
  transform: scale(0.98);
  opacity: 0.8;
}
.scroll-area {
  padding: 20rpx 15rpx;
  padding-top: 30rpx; /* 减少顶部内边距，使副标题向上移动 */
  padding-bottom: 0; /* 移除底部内边距 */
  flex: 1;
  box-sizing: border-box;
  overflow: hidden; /* 改为hidden，防止滚动 */
  display: flex;
  flex-direction: column;
}

.page-subtitle {
  font-size: 26rpx;
  color: #999999;
  text-align: center;
  margin-bottom: 30rpx; /* 改为rpx单位 */
  flex-shrink: 0; /* 不被压缩 */
}

.templates-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-top: 10rpx; /* 添加少量顶部边距 */
  padding: 0 10rpx;
  flex: 1; /* 占用剩余空间 */
  overflow-y: auto; /* 只有图片模板需要滚动 */
}

.template-item {
  width: 46%;
  margin: 0 0 30rpx;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.04);
  padding: 12rpx;
  transition: all 0.2s ease;
  border: 1rpx solid rgba(0, 0, 0, 0.03);
  box-sizing: border-box;
  background-color: #ffffff;
}

.template-item:active {
  transform: scale(0.98);
}

.template-item.selected {
  border-color: #07c160;
  border-width: 2rpx;
  background-color: #ffffff;
  box-shadow: 0 4rpx 16rpx rgba(7, 193, 96, 0.15);
}

.template-item.selected .template-name {
  color: #07c160;
  font-weight: 600;
}

.template-image-container {
  width: 100%;
  /* 设置高度为宽度的4/3，保持3:4的宽高比 */
  height: 0;
  padding-bottom: 133.33%; /* 4/3 = 1.3333... */
  position: relative;
  border-radius: 8rpx;
  overflow: hidden;
  background-color: #f8f8f8;
}

.template-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8rpx;
}

.template-name {
  text-align: center;
  margin-top: 20rpx;
  font-size: 26rpx;
  color: #444444;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 即将推出的模板样式 */
.template-item.coming-soon {
  opacity: 0.9;
  position: relative;
}

.coming-soon-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8rpx;
}

.coming-soon-text {
  color: white;
  font-size: 28rpx;
  font-weight: bold;
  background-color: rgba(0, 0, 0, 0.6);
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
}

.btn-container {
  padding: 20rpx 30rpx 50rpx; /* 增加底部内边距，确保白色背景延伸到屏幕底部 */
  width: 100%;
  box-sizing: border-box;
  position: fixed;
  bottom: 0; /* 改回0，让容器紧贴屏幕底部 */
  left: 0;
  background-color: white;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.next-btn {
  background-color: #07c160;
  color: white;
  border-radius: 8rpx;
  font-size: 30rpx;
  padding: 18rpx 0;
  width: 100%;
  box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.15);
  font-weight: 500;
  transition: all 0.2s ease;
}

.next-btn:active {
  opacity: 0.9;
  transform: scale(0.98);
}

/* 自定义模板样式 */
.custom-template {
  background-color: #fef7f7;
  border: 2rpx solid #ffe6eb;
}

.custom-template-preview {
  padding: 30rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  justify-content: space-between;
}

.custom-template-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #ff6b9d;
  margin-bottom: 20rpx;
  text-align: center;
}

.custom-color-blocks {
  display: flex;
  gap: 8rpx;
  margin-bottom: 20rpx;
  justify-content: center;
  flex-wrap: wrap;
}

.custom-color-block {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  border: 2rpx solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.custom-color-codes {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  align-items: center;
}

.custom-color-code {
  font-size: 20rpx;
  color: #ff6b9d;
  font-weight: 500;
  font-family: 'Courier New', monospace;
}

/* 自定义模板滑动展示样式 */
.custom-templates-swiper-container {
  flex: 1; /* 占用剩余空间 */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止内容溢出 */
  min-height: 0; /* 允许flex收缩 */
}

.custom-templates-swiper {
  height: 900rpx; /* 进一步增加高度，让模板显示更大 */
  width: 100%;
  overflow: hidden; /* 防止滑动组件溢出 */
}

.custom-template-slide {
  padding: 40rpx 30rpx 20rpx; /* 调整内边距 */
  display: flex;
  align-items: flex-start; /* 顶部对齐 */
  justify-content: center;
  min-height: 100%; /* 最小高度占满 */
  box-sizing: border-box;
}

.custom-template-card {
  width: 100%;
  max-width: 600rpx; /* 增加最大宽度 */
  background: transparent; /* 移除背景，让模板内容的背景显示 */
  border-radius: 0; /* 移除圆角，由内容控制 */
  padding: 0; /* 移除内边距，由内容控制 */
  box-shadow: none; /* 移除阴影，由内容控制 */
  text-align: center;
  border: none; /* 移除边框 */
  display: flex;
  flex-direction: column;
  justify-content: center; /* 居中显示 */
  align-items: center;
  height: 100%; /* 占满slide高度 */
}

/* 蜜桃汽水模板预览样式 - 完全按照P02生成样式 */
.custom-template-preview-large {
  width: 100%;
  max-width: 550rpx; /* 进一步增大宽度 */
  height: 700rpx; /* 设置固定高度，按比例 800:1066 */
  border-radius: 0; /* 移除圆角，与生成样式一致 */
  overflow: hidden;
  box-shadow: 0 6rpx 30rpx rgba(0, 0, 0, 0.15); /* 进一步增强阴影 */
  background-color: white;
  margin: 0 auto; /* 居中显示 */
  position: relative;
}

/* 上半部分背景 - 按照P02生成比例 */
.custom-template-top-bg {
  background-color: #FFF2EB;
  height: 350rpx; /* 固定高度，按比例 1052/2132 * 700 ≈ 350 */
  padding: 0; /* 移除padding，使用绝对定位 */
  text-align: center;
  position: relative;
}

.custom-template-title-large {
  position: absolute;
  top: 90rpx; /* 再下移45rpx，从45rpx调整为90rpx */
  left: 0;
  right: 0;
  font-size: 50rpx; /* 进一步增大字体 */
  font-weight: bold;
  color: #EB898E;
  margin: 0;
  text-align: center;
}

/* 圆形色块区域 - 按照P02生成位置 */
.custom-circles-area {
  position: absolute;
  top: 280rpx; /* 按比例定位，837/2132 * 700 ≈ 280 */
  left: 0;
  right: 0;
  height: 140rpx; /* 给圆形色块足够空间 */
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.custom-color-blocks-large {
  display: flex;
  justify-content: center;
  gap: 0; /* 移除间距，使用负边距创建交叉效果 */
  flex-wrap: wrap;
  position: relative;
}

.custom-color-block-large {
  width: 120rpx; /* 增大尺寸，让圆形更明显 */
  height: 120rpx;
  border-radius: 50%;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2); /* 增强阴影 */
  flex-shrink: 0;
  border: none; /* 移除边框，与生成样式一致 */
  margin-left: -20rpx; /* 负边距创建交叉效果 */
}

.custom-color-block-large:first-child {
  margin-left: 0; /* 第一个色块不需要负边距 */
}

/* 下半部分背景 - 按照P02生成样式 */
.custom-template-bottom-bg {
  position: absolute;
  top: 350rpx; /* 从上半部分结束位置开始 */
  left: 0;
  right: 0;
  bottom: 0;
  background-color: white;
  padding: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 颜色代码区域 - 与圆形色块垂直对齐 */
.custom-color-codes-large {
  position: absolute;
  top: 110rpx; /* 调整位置，与圆形色块垂直对齐 */
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 0; /* 移除间距 */
  flex-wrap: wrap;
}

/* 颜色代码项 - 添加负边距匹配圆形色块的交叉布局 */
.custom-color-codes-large .custom-color-code-large {
  margin-left: -20rpx; /* 与圆形色块相同的负边距 */
  width: 120rpx; /* 与圆形色块相同宽度，确保对齐 */
}

.custom-color-codes-large .custom-color-code-large:first-child {
  margin-left: 0; /* 第一个代码不需要负边距 */
}

.custom-color-code-large {
  font-size: 20rpx; /* 减小字体大小 */
  font-weight: bold;
  font-family: 'PingFang SC', Arial, sans-serif; /* 与生成样式一致 */
  color: #EB898E;
  text-align: center;
  background: none; /* 移除背景色，与生成样式一致 */
  padding: 0; /* 移除内边距 */
  border-radius: 0; /* 移除圆角 */
}

/* 小矩形色块预览 - 下移一点 */
.custom-small-blocks {
  position: absolute;
  top: 180rpx; /* 下移30rpx，让布局更协调 */
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 0; /* 紧挨着贴合，无间距 */
  flex-wrap: wrap;
}

.custom-small-block {
  width: 35rpx; /* 按比例 146/1600 * 550 ≈ 50，缩放到35 */
  height: 35rpx;
  border-radius: 0; /* 矩形，无圆角 */
  flex-shrink: 0;
}

/* 海盐气泡模板预览样式 - 统一高度为700rpx */
.custom-template-preview-p02 {
  width: 100%;
  max-width: 550rpx; /* 与蜜桃汽水模板相同宽度 */
  height: 700rpx; /* 统一高度为700rpx，与蜜桃汽水模板一致 */
  border-radius: 0; /* 移除圆角，与生成样式一致 */
  overflow: hidden;
  box-shadow: 0 6rpx 30rpx rgba(0, 0, 0, 0.15); /* 进一步增强阴影 */
  background-color: white;
  padding: 0; /* 移除内边距，使用绝对定位 */
  text-align: center;
  margin: 0 auto; /* 居中显示 */
  position: relative;
}

.custom-template-title-p02 {
  position: absolute;
  top: 90rpx; /* 调整为与蜜桃汽水模板一致的位置 */
  left: 0;
  right: 0;
  font-size: 50rpx; /* 保持字体大小 */
  font-weight: bold;
  color: #5865B1;
  text-align: center;
}

/* 长条形色块预览 - 调整位置适应700rpx高度 */
.custom-long-blocks {
  position: absolute;
  top: 280rpx; /* 调整位置，适应700rpx高度 */
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 35rpx; /* 稍微减少间距 */
  flex-wrap: wrap;
}

.custom-long-block {
  width: 65rpx; /* 稍微减小宽度 */
  height: 220rpx; /* 调整高度适应新布局 */
  border-radius: 32rpx; /* 按比例调整圆角 */
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2); /* 增强阴影 */
  flex-shrink: 0;
}

/* 颜色代码预览 - 调整位置和字体大小 */
.custom-color-codes-p02 {
  position: absolute;
  top: 540rpx; /* 调整位置适应700rpx高度 */
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 35rpx; /* 与长条形色块相同间距 */
  flex-wrap: wrap;
}

.custom-color-code-p02 {
  width: 65rpx; /* 与条形色块宽度一致，确保对齐 */
  font-size: 16rpx; /* 进一步减小字体大小，确保对齐 */
  font-weight: normal;
  font-family: Arial, sans-serif; /* 与生成样式一致 */
  text-align: center;
  background: none; /* 移除背景色，与生成样式一致 */
  padding: 0; /* 移除内边距 */
  margin: 0; /* 确保没有外边距 */
  border-radius: 0; /* 移除圆角 */
  line-height: 1; /* 设置行高为1，避免文字偏移 */
  display: flex; /* 使用flex布局 */
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
}

/* 圆形色块预览 - 调整位置 */
.custom-circles-p02 {
  position: absolute;
  top: 580rpx; /* 调整位置适应700rpx高度 */
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 35rpx; /* 与长条形色块相同间距 */
  flex-wrap: wrap;
}

.custom-circle-p02 {
  width: 65rpx; /* 与长条形色块相同宽度 */
  height: 65rpx;
  border-radius: 50%;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2); /* 增强阴影 */
  flex-shrink: 0;
}

/* 移除不再使用的样式 */

/* 滑动提示样式 - 相对定位 */
.swipe-hint-relative {
  padding: 30rpx 0; /* 使用padding控制高度 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  flex-shrink: 0; /* 防止被压缩 */
  background-color: transparent; /* 确保背景透明 */
}

.swipe-hint-text {
  font-size: 26rpx; /* 稍微增大字体 */
  color: #666666; /* 加深颜色，提高可见性 */
  margin-bottom: 12rpx; /* 增加间距 */
  font-weight: 400;
}

.swipe-hint-dots {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12rpx;
}

.hint-dot {
  width: 16rpx; /* 增大尺寸 */
  height: 16rpx;
  border-radius: 50%;
  background-color: #d0d0d0; /* 稍微加深颜色 */
  transition: all 0.3s ease;
  border: 1rpx solid #f0f0f0; /* 添加边框增强可见性 */
}

.hint-dot.active {
  background-color: #07c160;
  transform: scale(1.3); /* 增大缩放比例 */
  border-color: #07c160;
}

/* 落日漫旅模板预览样式 - 按照P03设计1:1复刻 */
.custom-template-preview-p03 {
  width: 100%;
  max-width: 550rpx; /* 与其他模板相同宽度 */
  height: 700rpx; /* 统一高度 */
  border-radius: 0; /* 移除圆角，与生成样式一致 */
  overflow: hidden;
  box-shadow: 0 6rpx 30rpx rgba(0, 0, 0, 0.15);
  background-color: white;
  margin: 0 auto; /* 居中显示 */
  position: relative;
}

/* 上半部分背景 - 按照P03比例 1052/2132 */
.custom-template-top-bg-p03 {
  background-color: #F7E8E4; /* P03的上半部分背景色 */
  height: 350rpx; /* 统一高度为350rpx，与其他模板保持一致 */
  position: relative;
  padding: 0;
}

/* 标题样式 - 按照P03设计 */
.custom-template-title-p03 {
  position: absolute;
  top: 90rpx; /* 与其他模板保持一致的位置 */
  left: 0;
  right: 0;
  font-size: 50rpx; /* 按比例缩放 168px -> 50rpx */
  font-weight: bold;
  color: #C66767; /* P03的标题颜色 */
  text-align: center;
  font-family: 'PingFang SC', Arial, sans-serif;
}

/* 旋转方形色块容器 - 按照P03位置 */
.custom-diamond-blocks {
  position: absolute;
  top: 305rpx; /* 调整到上半部分内的居中位置 */
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 25rpx; /* 方形间距 */
  flex-wrap: wrap;
  width: 100%;
  z-index: 10; /* 确保在上层显示，不被下半部分遮住 */
}

/* 旋转方形色块 - 按照P03设计 */
.custom-diamond-block {
  width: 90rpx; /* 按比例缩放 350px -> 90rpx */
  height: 90rpx;
  border-radius: 0; /* 方形 */
  transform: rotate(45deg); /* 45度旋转 */
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  flex-shrink: 0;
}

/* 下半部分背景 - 按照P03设计 */
.custom-template-bottom-bg-p03 {
  position: absolute;
  top: 350rpx; /* 从上半部分结束位置开始，与其他模板保持一致 */
  left: 0;
  right: 0;
  bottom: 0;
  background-color: white;
  padding: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 颜色代码区域 - 按照P03位置 */
.custom-color-codes-p03 {
  position: absolute;
  top: 80rpx; /* 按比例定位，1337/2132 * 350 ≈ 220，调整到80 */
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 25rpx; /* 与方形色块相同间距 */
  flex-wrap: wrap;
}

/* 颜色代码样式 - 按照P03设计 */
.custom-color-code-p03 {
  width: 90rpx; /* 与菱形色块宽度一致，确保对齐 */
  font-size: 18rpx; /* 按比例缩放 42px -> 18rpx */
  font-weight: bold;
  font-family: 'PingFang SC', Arial, sans-serif;
  color: #7D7D7D; /* P03的颜色代码颜色 */
  text-align: center;
  background: none;
  padding: 0;
  border-radius: 0;
}

/* 圆形色块区域 - 按照P03位置 */
.custom-circles-p03 {
  position: absolute;
  top: 140rpx; /* 按比例定位，1568/2132 * 350 ≈ 260，调整到140 */
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 25rpx; /* 与其他元素相同间距 */
  flex-wrap: wrap;
}

/* 圆形色块样式 - 按照P03设计 */
.custom-circle-p03 {
  width: 50rpx; /* 按比例缩放 158px -> 50rpx */
  height: 50rpx;
  border-radius: 50%;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  flex-shrink: 0;
}

/* ==================== P04 春日樱语模板样式 ==================== */

/* P04模板预览容器 */
.custom-template-preview-p04 {
  width: 100%;
  height: 700rpx;
  position: relative;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

/* 上半部分背景 - 按照P04比例 1052/2132 */
.custom-template-top-bg-p04 {
  background-color: #FFF1F1; /* P04的上半部分背景色 */
  height: 350rpx; /* 按比例 1052/2132 * 700 ≈ 350 */
  position: relative;
  padding: 0;
}

/* 标题样式 - 按照P04设计 */
.custom-template-title-p04 {
  position: absolute;
  top: 80rpx; /* 按比例定位 */
  left: 50%;
  transform: translateX(-50%);
  font-size: 60rpx; /* 按比例缩放 */
  font-weight: 650;
  color: #E89EC3; /* P04标题颜色 */
  text-align: center;
  white-space: nowrap;
}

/* 花瓣形状色块容器 - 在整个模板高度垂直居中 */
.custom-petal-shapes {
  position: absolute;
  top: 295rpx; /* 适应110rpx花瓣色块尺寸 */
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 23rpx; /* 适当调整花瓣间距 */
  flex-wrap: wrap;
  width: 100%;
  z-index: 10;
}

/* 花瓣形状SVG容器 - P04样式 */
.custom-petal-svg-container {
  width: 110rpx; /* 调整花瓣色块尺寸为110rpx */
  height: 110rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  /* 去掉阴影效果 */
  border-radius: 50%;
  overflow: hidden;
}

.custom-petal-svg {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 花瓣加载状态 */
.custom-petal-loading {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 50%;
  font-size: 20rpx;
  color: #999;
}

/* 花瓣加载状态 */
.custom-petal-loading {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 50%;
  font-size: 20rpx;
  color: #999;
}

/* 下半部分背景 - 按照P04设计 */
.custom-template-bottom-bg-p04 {
  position: absolute;
  top: 350rpx; /* 从上半部分结束位置开始 */
  left: 0;
  right: 0;
  bottom: 0;
  background-color: white;
  padding: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 颜色代码容器 - P04样式 */
.custom-color-codes-p04 {
  display: flex;
  justify-content: center;
  gap: 23rpx; /* 与花瓣色块间距对齐 */
  margin-top: 70rpx; /* 下移位置 */
  flex-wrap: wrap;
}

/* 单个颜色代码 - P04样式 */
.custom-color-code-p04 {
  width: 110rpx; /* 与花瓣色块宽度一致，确保对齐 */
  font-size: 20rpx; /* 按比例缩放 */
  color: #7D7D7D;
  font-weight: 650;
  text-align: center;
}

/* 圆形色块容器 - P04样式 */
.custom-circles-p04 {
  display: flex;
  justify-content: center;
  gap: 25rpx;
  margin-top: 30rpx;
  flex-wrap: wrap;
}

/* 星形圆形色块容器 - P04样式 */
.custom-star-circles {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 25rpx; /* 适应60rpx星形色块尺寸 */
  margin-top: 60rpx; /* 进一步下移星形色块位置 */
  flex-wrap: wrap;
}

/* 星形SVG容器 - P04样式 */
.custom-star-svg-container {
  width: 60rpx; /* 调整星形色块尺寸为60rpx */
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  /* 去掉阴影效果 */
  border-radius: 50%;
  overflow: hidden;
}

.custom-star-svg {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 圆形加载状态 */
.custom-circle-loading {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 50%;
  font-size: 18rpx;
  color: #999;
}
