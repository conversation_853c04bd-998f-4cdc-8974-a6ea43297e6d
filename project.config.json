{"appid": "wxe656049fe615db69", "compileType": "miniprogram", "libVersion": "3.0.2", "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "compileWorklet": true, "uglifyFileName": true, "uploadWithSourceMap": true, "packNpmManually": false, "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": false, "condition": false, "swc": true, "disableSWC": false, "ignoreUploadUnusedFiles": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "packNpmRelationList": []}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "projectArchitecture": "miniProgram", "packOptions": {"ignore": [{"value": "PP_DISABLED", "type": "folder"}, {"value": "*.html", "type": "file"}, {"value": "*.htm", "type": "file"}], "include": []}, "condition": {}, "simulatorPluginLibVersion": {}}