/**
 * 微信小程序错误拦截器
 * 专门处理微信内部系统错误，如 updateTextView、reportKeyValue 等
 */

/**
 * 检查是否是微信内部系统错误或开发工具错误
 * @param {string|Error} error - 错误信息
 * @returns {boolean} 是否是微信内部错误或开发工具错误
 */
function isWxInternalError(error) {
  const errorMsg = typeof error === 'string' ? error : (error.message || error.toString());

  const internalErrorPatterns = [
    // 微信内部错误
    'updateTextView',
    'reportKeyValue',
    'removeTextView',
    'removeBaseView',
    'createTextView',
    'createBaseView',
    'too early',
    'too eayly', // 微信的拼写错误
    'jsbridge',
    'WAServiceMainContext',
    'fail no root view',
    'invoke.*fail.*too.*early',
    'invoke.*fail.*too.*eayly',
    'routeDone',
    'webviewId.*not.*current.*page',
    'Page route.*system error',
    // 开发工具错误
    'aiad error',
    'benchmark',
    'unexpected page',
    '\\.html',
    '_getData',
    'webviewScriptError',
    'SystemError'
  ];

  return internalErrorPatterns.some(pattern => {
    try {
      return new RegExp(pattern, 'i').test(errorMsg);
    } catch (e) {
      return errorMsg.toLowerCase().includes(pattern.toLowerCase());
    }
  });
}

/**
 * 安全的控制台输出
 * 避免在某些环境下控制台输出也可能触发错误
 */
function safeConsoleWarn(message, ...args) {
  try {
    if (console && console.warn) {
      console.warn(message, ...args);
    }
  } catch (e) {
    // 静默处理控制台错误
  }
}

function safeConsoleError(message, ...args) {
  try {
    if (console && console.error) {
      console.error(message, ...args);
    }
  } catch (e) {
    // 静默处理控制台错误
  }
}

/**
 * 全局错误拦截器
 * 应该在 app.js 中调用
 */
function setupGlobalErrorInterceptor() {
  // 拦截全局错误
  if (typeof wx !== 'undefined' && wx.onError) {
    wx.onError((error) => {
      if (isWxInternalError(error)) {
        // 静默忽略微信内部错误
        return;
      }
      
      // 其他错误可以选择上报或处理
      safeConsoleError('全局错误:', error);
    });
  }
  
  // 拦截未处理的 Promise rejection - 增强版本
  if (typeof wx !== 'undefined' && wx.onUnhandledRejection) {
    wx.onUnhandledRejection((res) => {
      if (res && res.reason) {
        let errorMsg = '';
        let errorType = 'unknown';

        if (typeof res.reason === 'string') {
          errorMsg = res.reason;
        } else if (typeof res.reason === 'object') {
          errorMsg = res.reason.errMsg || res.reason.message || res.reason.toString();
        }

        if (isWxInternalError(errorMsg)) {
          // 静默忽略微信内部错误
          return;
        }

        // 分类处理不同类型的错误
        if (errorMsg.includes('Canvas')) {
          errorType = 'canvas';
        } else if (errorMsg.includes('广告') || errorMsg.includes('ad')) {
          errorType = 'advertisement';
        } else if (errorMsg.includes('网络') || errorMsg.includes('network')) {
          errorType = 'network';
        } else if (errorMsg.includes('权限') || errorMsg.includes('permission')) {
          errorType = 'permission';
        } else if (errorMsg.includes('超时') || errorMsg.includes('timeout')) {
          errorType = 'timeout';
        }

        // 记录错误信息
        safeConsoleError('未处理的Promise rejection:', {
          type: errorType,
          message: errorMsg,
          reason: res.reason,
          stack: res.reason && res.reason.stack
        });

        // 根据错误类型进行特殊处理
        handleUnhandledRejection(errorType, errorMsg, res.reason);
      }
    });
  }
  
  // 拦截页面错误（如果支持）
  if (typeof window !== 'undefined' && window.addEventListener) {
    window.addEventListener('error', (event) => {
      if (isWxInternalError(event.message || event.error)) {
        event.preventDefault();
        return false;
      }
    }, true);
    
    window.addEventListener('unhandledrejection', (event) => {
      if (isWxInternalError(event.reason)) {
        event.preventDefault();
        return false;
      }
    });
  }
}

/**
 * 处理未捕获的Promise rejection
 * @param {string} errorType - 错误类型
 * @param {string} errorMsg - 错误消息
 * @param {*} reason - 错误原因
 */
function handleUnhandledRejection(errorType, errorMsg, reason) {
  switch (errorType) {
    case 'canvas':
      // Canvas错误的特殊处理
      safeConsoleWarn('Canvas操作出现未处理错误，可能影响色卡生成功能');
      break;

    case 'advertisement':
      // 广告错误的特殊处理
      safeConsoleWarn('广告操作出现未处理错误，可能影响激励广告功能');
      break;

    case 'network':
      // 网络错误的特殊处理
      safeConsoleWarn('网络操作出现未处理错误，可能影响数据加载');
      break;

    case 'permission':
      // 权限错误的特殊处理
      safeConsoleWarn('权限操作出现未处理错误，可能影响相机等功能');
      break;

    case 'timeout':
      // 超时错误的特殊处理
      safeConsoleWarn('操作超时出现未处理错误，可能影响功能响应');
      break;

    default:
      // 其他未知错误
      safeConsoleWarn('出现未分类的Promise rejection错误');
      break;
  }

  // 可以在这里添加错误上报逻辑
  // reportError(errorType, errorMsg, reason);
}

/**
 * 包装函数，自动处理微信内部错误
 * @param {Function} fn - 要包装的函数
 * @returns {Function} 包装后的函数
 */
function wrapWithErrorHandler(fn) {
  return function(...args) {
    try {
      return fn.apply(this, args);
    } catch (error) {
      if (isWxInternalError(error)) {
        safeConsoleWarn('捕获并忽略微信内部错误:', error.message || error);
        return;
      }
      throw error;
    }
  };
}

/**
 * 安全的 setData 包装器
 * @param {Object} context - 页面或组件上下文
 * @returns {Function} 包装后的 setData 函数
 */
function createSafeSetDataWrapper(context) {
  const originalSetData = context.setData;
  
  return function(data, callback) {
    try {
      return originalSetData.call(this, data, callback);
    } catch (error) {
      if (isWxInternalError(error)) {
        safeConsoleWarn('setData 遇到微信内部错误，延迟重试:', error.message || error);
        
        // 延迟重试
        setTimeout(() => {
          try {
            originalSetData.call(this, data, callback);
          } catch (retryError) {
            if (!isWxInternalError(retryError)) {
              safeConsoleError('setData 重试失败:', retryError);
            }
          }
        }, 100);
        
        return;
      }
      throw error;
    }
  };
}

/**
 * 为页面或组件添加错误处理
 * @param {Object} context - 页面或组件上下文
 */
function addErrorHandling(context) {
  if (context && context.setData) {
    context.setData = createSafeSetDataWrapper(context);
  }
}

module.exports = {
  isWxInternalError,
  setupGlobalErrorInterceptor,
  handleUnhandledRejection,
  wrapWithErrorHandler,
  createSafeSetDataWrapper,
  addErrorHandling,
  safeConsoleWarn,
  safeConsoleError
};
