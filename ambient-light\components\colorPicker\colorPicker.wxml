<!--components/colorPicker/colorPicker.wxml-->
<view class="color-picker">
  <!-- 颜色预览 -->
  <view class="color-preview-container">
    <view class="color-preview" style="background-color: {{hexValue}};"></view>
  </view>

  <!-- 选择器模式切换 -->
  <view class="mode-tabs">
    <view
      class="mode-tab {{pickerMode === 'grid' ? 'active' : ''}}"
      bindtap="switchPickerMode"
      data-mode="grid"
    >网格</view>
    <view
      class="mode-tab {{pickerMode === 'spectrum' ? 'active' : ''}}"
      bindtap="switchPickerMode"
      data-mode="spectrum"
    >光谱</view>
    <view
      class="mode-tab {{pickerMode === 'slider' ? 'active' : ''}}"
      bindtap="switchPickerMode"
      data-mode="slider"
    >滑块</view>
    <view
      class="mode-tab {{pickerMode === 'image' ? 'active' : ''}}"
      bindtap="switchPickerMode"
      data-mode="image"
    >图片取色</view>
  </view>

  <!-- 网格模式 -->
  <view class="picker-content" hidden="{{pickerMode !== 'grid'}}">
    <view class="color-grid">
      <view
        class="color-cell"
        wx:for="{{colorGrid}}"
        wx:key="index"
        style="background-color: {{item}};"
        bindtap="onGridColorSelect"
        data-color="{{item}}"
      ></view>
    </view>
  </view>

  <!-- 光谱模式 -->
  <view class="picker-content" hidden="{{pickerMode !== 'spectrum'}}">
    <view class="color-spectrum-container">
      <!-- 主色谱区域 - 从白色到选定色相 -->
      <view class="color-spectrum"
            style="background: linear-gradient(to right, #FFFFFF 0%, {{spectrumBackground}} 100%);"
            bindtouchstart="onSpectrumTouchStart"
            bindtouchmove="onSpectrumTouchMove">
        <view class="spectrum-selector"
              style="left: {{spectrumSelector.x}}px; top: {{spectrumSelector.y}}px; visibility: {{spectrumSelectorReady ? 'visible' : 'hidden'}};">
        </view>
      </view>

      <!-- 色相条 -->
      <view class="hue-slider-container">
        <view class="hue-slider" bindtouchstart="onHueSliderTouchStart" bindtouchmove="onHueSliderTouchMove">
          <view class="hue-slider-thumb" style="left: {{hueSliderPosition}}px;"></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 滑块模式 -->
  <view class="picker-content" hidden="{{pickerMode !== 'slider'}}">
    <!-- RGB滑块 -->
    <view class="slider-group">
      <view class="slider-label">红色</view>
      <view class="slider-bar-container">
        <slider
          class="slider-bar red-slider"
          min="0"
          max="255"
          value="{{r}}"
          activeColor="#ff0000"
          backgroundColor="#f0f0f0"
          block-color="#ffffff"
          block-size="24"
          bindchange="onRgbChange"
          data-type="r"
        ></slider>
      </view>
      <view class="slider-value">{{r}}</view>
    </view>

    <view class="slider-group">
      <view class="slider-label">绿色</view>
      <view class="slider-bar-container">
        <slider
          class="slider-bar green-slider"
          min="0"
          max="255"
          value="{{g}}"
          activeColor="#00ff00"
          backgroundColor="#f0f0f0"
          block-color="#ffffff"
          block-size="24"
          bindchange="onRgbChange"
          data-type="g"
        ></slider>
      </view>
      <view class="slider-value">{{g}}</view>
    </view>

    <view class="slider-group">
      <view class="slider-label">蓝色</view>
      <view class="slider-bar-container">
        <slider
          class="slider-bar blue-slider"
          min="0"
          max="255"
          value="{{b}}"
          activeColor="#00ffff"
          backgroundColor="#f0f0f0"
          block-color="#ffffff"
          block-size="24"
          bindchange="onRgbChange"
          data-type="b"
        ></slider>
      </view>
      <view class="slider-value">{{b}}</view>
    </view>

    <!-- HEX输入框 -->
    <view class="hex-input-group" wx:if="{{isComponentReady}}">
      <view class="hex-input-label">#</view>
      <input
        class="hex-input-field"
        type="text"
        value="{{hexInput || 'C83C23'}}"
        bindinput="onHexInput"
        bindblur="onHexBlur"
        maxlength="6"
        placeholder="输入HEX色值"
      />
      <view class="hex-button copy-button" bindtap="copyHexValue">复制</view>
      <view class="hex-button paste-button" bindtap="pasteHexValue">粘贴</view>
    </view>
  </view>



  <!-- 按钮 -->
  <view class="buttons">
    <button class="btn cancel-btn" bindtap="cancelColor">取消</button>
    <button class="btn confirm-btn" bindtap="confirmColor">确定</button>
  </view>


</view>
