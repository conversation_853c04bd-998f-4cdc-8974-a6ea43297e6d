/* card-package/pages/customImageColorPicker/customImageColorPicker.wxss */
/* 自定义色卡编辑的独立一键图片取色页面样式 */
/* 参考图片主题色提取页面的设计风格 */

.page-wrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  box-sizing: border-box;
  background-color: transparent;
  padding-bottom: 180rpx;
  touch-action: pan-y; /* 允许垂直滚动，但限制其他触摸手势 */
}

/* 颜色选择区域 */
.colors-section {
  margin-bottom: 0rpx; /* 减少底部间距，让颜色区域更靠近提示文字 */
  padding-bottom: 0;
}

.colors-title {
  font-size: 24rpx;
  color: #333;
  margin: 0 auto 8rpx; /* 进一步减少边距，顶部边距为0 */
  padding: 10rpx 20rpx; /* 进一步减少内边距 */
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f7f7f7;
  max-width: 94%;
  border-left: 2rpx solid #07c160;
  min-height: 45rpx; /* 进一步减少最小高度 */
}

.title-left {
  display: flex;
  align-items: flex-start;
  flex: 1;
}

.title-text {
  display: flex;
  flex-direction: column;
  line-height: 1.4;
}

.title-text text:last-child {
  padding-left: 0;
}



/* 信息点 */
.info-dot {
  width: 6rpx;
  height: 6rpx;
  margin-right: 8rpx;
  margin-top: 10rpx;
  background-color: #07c160;
  border-radius: 50%;
  flex-shrink: 0;
}

.colors-row {
  padding: 0 20rpx;
  position: relative;
  z-index: 5;
  margin-top: -5rpx; /* 负边距，让颜色区域更靠近提示文字 */
  min-height: 135rpx; /* 进一步减少最小高度，适应紧凑布局 */
}

/* 颜色容器 */
.colors-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: nowrap;
  width: 100%;
  transition: opacity 0.3s ease;
}

.colors-container.transitioning {
  opacity: 0.7;
}

/* 颜色项容器 */
.color-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  max-width: 130rpx;
  min-width: 100rpx;
  margin: 0 5rpx;
  height: 135rpx; /* 减少高度，适应紧凑布局 */
  position: relative;
  transition: transform 0.1s ease, opacity 0.3s ease;
  touch-action: manipulation; /* 允许触摸操作但防止双击缩放 */
}

.color-item.color-transitioning {
  opacity: 0.8;
  transform: scale(0.98);
}

.color-item.dragging {
  opacity: 0.95;
  transform: scale(1.05);
  z-index: 100;
  transition: none; /* 拖拽时禁用过渡动画 */
}

/* 拖拽时的阴影效果已在JavaScript中动态设置 */

.color-block {
  width: 100%;
  max-width: 120rpx;
  height: 80rpx;
  border-radius: 12rpx;
  position: relative;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease, background-color 0.4s ease;
  overflow: hidden;
  cursor: pointer;
  margin-top: 0rpx; /* 完全移除顶部间距，让色块更靠近提示文字 */
}

/* 棋盘格背景 */
.color-block::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(45deg, #eee 25%, transparent 25%),
    linear-gradient(-45deg, #eee 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, #eee 75%),
    linear-gradient(-45deg, transparent 75%, #eee 75%);
  background-size: 16rpx 16rpx;
  background-position: 0 0, 0 8rpx, 8rpx -8rpx, -8rpx 0px;
  z-index: -1;
  opacity: 0.5;
}

.color-block:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.color-block.selected {
  transform: scale(1.08);
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.2);
  border: 2rpx solid white;
}

.color-block.selected:active {
  transform: scale(1.04);
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.15);
}

.color-number {
  position: absolute;
  right: 8rpx;
  bottom: 8rpx;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.9);
  color: #333;
  font-size: 22rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
}

/* 图片预览区域 */
.image-section {
  margin-top: 60rpx; /* 增加上边距，确保图片与复选框有足够间距，避免复选框遮住图片 */
  margin-bottom: 30rpx; /* 减少底部间距 */
  background-color: transparent;
  box-shadow: none;
  border: none;
}

.image-container {
  width: 100%;
  display: flex;
  justify-content: center;
  position: relative;
  min-height: 200rpx;
  touch-action: none; /* 完全禁用默认触摸行为，防止页面滚动和缩放 */
  background-color: transparent;
  box-shadow: none;
  border: none;
  overflow: hidden; /* 防止内容溢出导致的滚动 */
}

.preview-image {
  width: 690rpx;
  height: calc(690rpx * 4 / 3);
  border-radius: 0;
  object-fit: contain;
  box-shadow: none;
  border: none;
  background-color: transparent;
  touch-action: none; /* 禁用图片的默认触摸行为 */
  pointer-events: none; /* 禁用图片的指针事件，让容器处理触摸 */
  user-select: none; /* 禁用文本选择 */
}

/* 平移提示 */
.panning-tip {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  z-index: 9998;
  animation: fadeInOut 3s ease-in-out;
}

@keyframes fadeInOut {
  0% { opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { opacity: 0; }
}

/* 放大镜容器 */
.magnifier-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  pointer-events: none;
}

/* 连接线样式已移除 */

/* 操作提示 */
.operation-tip {
  padding: 20rpx 30rpx 20rpx 50rpx;
  margin: 0 30rpx 8rpx; /* 减少底部间距，让颜色区域更靠近提示文字 */
  background-color: #f8f9fa;
  border-radius: 12rpx;
  position: relative;
}

.operation-tip::before {
  content: '';
  position: absolute;
  left: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 12rpx;
  height: 12rpx;
  background-color: #07c160;
  border-radius: 50%;
}

.tip-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.tip-text.selected {
  color: #007aff;
  font-weight: 500;
}

.tip-text.dragging {
  color: #ff9500;
  font-weight: 500;
}

/* 目标点 */
.target-point {
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #07c160;
  border: 2px solid white;
  transform: translate(-50%, -50%);
  z-index: 9997;
  box-shadow: 0 0 4px rgba(7, 193, 96, 0.6), 0 0 8px rgba(7, 193, 96, 0.3);
}

/* 放大镜效果 */
.magnifier {
  position: absolute;
  width: 90px;
  height: 90px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 0 0 1px black, 0 0 10px rgba(0, 0, 0, 0.6);
  overflow: hidden;
  transform: translate(-50%, -50%);
  background-color: rgba(255, 255, 255, 0.1);
  z-index: 9999;
}

.magnifier-content {
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 50%;
  position: relative;
}

.magnifier-image {
  position: absolute;
  transform-origin: 0 0;
  object-fit: cover;
  z-index: 1000;
}

/* 水平十字线 */
.magnifier-crosshair-h {
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: rgba(255, 255, 255, 0.8);
  transform: translateY(-50%);
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.8);
  z-index: 1002;
  pointer-events: none;
}

/* 垂直十字线 */
.magnifier-crosshair-v {
  position: absolute;
  top: 0;
  left: 50%;
  width: 2px;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  transform: translateX(-50%);
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.8);
  z-index: 1002;
  pointer-events: none;
}

/* 颜色指示器 */
.color-indicator {
  position: absolute;
  bottom: 10px;
  right: 10px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.5);
  z-index: 1003;
}

/* 图片加载状态 */
.image-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: transparent;
  border-radius: 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #07c160;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 颜色信息样式 */
.color-info {
  width: 100%;
  position: absolute;
  top: 85rpx; /* 减少顶部距离，让色值代码更靠近色块 */
  left: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 5;
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

.color-hex {
  font-size: 22rpx;
  font-weight: bold;
  color: #333;
  padding: 4rpx 8rpx;
  text-align: center;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-decoration: none; /* 去掉下划线 */
}

.color-hex:active {
  opacity: 0.7;
}

/* 复选框样式 */
.color-checkbox {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 4rpx; /* 减少顶部间距，让复选框更靠近色值代码 */
  padding: 6rpx; /* 减少内边距 */
}

.checkbox {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #ddd;
  border-radius: 6rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  transition: all 0.2s ease;
  cursor: pointer;
}

.checkbox.checked {
  background-color: #07c160;
  border-color: #07c160;
}

.checkbox:active {
  transform: scale(0.95);
}

.checkmark {
  color: #fff;
  font-size: 20rpx;
  font-weight: bold;
  line-height: 1;
}

.btn-container {
  padding: 20rpx 30rpx 50rpx;
  width: 100%;
  box-sizing: border-box;
  position: fixed;
  bottom: 0;
  left: 0;
  background-color: white;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.next-btn {
  background-color: #07c160;
  color: white;
  border-radius: 8rpx;
  font-size: 30rpx;
  padding: 18rpx 0;
  width: 100%;
  box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.15);
  transition: all 0.2s ease;
  font-weight: 500;
}

.next-btn:active {
  opacity: 0.9;
  transform: scale(0.98);
}

/* 按钮内文字样式 */
.btn-main-text {
  font-size: 30rpx;
  font-weight: 500;
}

.btn-sub-text {
  font-size: 24rpx;
  font-weight: 400;
  opacity: 0.9;
}




