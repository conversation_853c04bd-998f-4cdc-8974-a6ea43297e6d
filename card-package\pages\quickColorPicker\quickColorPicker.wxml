<!--pages/quickColorPicker/quickColorPicker.wxml-->
<view class="page-wrapper">
  <!-- 颜色选择区域 -->
  <view class="colors-section">
    <view class="colors-title" style="margin-top: {{titleMarginTop || '0'}};">
      <view class="title-left">
        <view class="info-dot"></view>
        <view class="title-text">
          <text>选中要编辑的颜色，点击图片进行取色</text>
        </view>
      </view>
    </view>
    <view class="colors-row">
      <!-- 颜色加载状态 -->
      <view wx:if="{{colorsLoading}}" class="colors-loading" style="display: flex; justify-content: center; align-items: center; height: 200rpx; flex-direction: column;">
        <view class="loading-spinner" style="width: 60rpx; height: 60rpx; border: 4rpx solid #f3f3f3; border-top: 4rpx solid #3498db; border-radius: 50%; animation: spin 1s linear infinite; margin-bottom: 20rpx;"></view>
        <view style="color: #666; font-size: 28rpx;">正在提取颜色...</view>
      </view>

      <!-- 颜色块显示容器 -->
      <view wx:elif="{{colors && colors.length > 0}}" class="colors-container">
        <!-- 颜色块显示 - 水平排列 -->
        <view
          wx:for="{{colors}}"
          wx:key="index"
          class="color-item {{pickingIndex === index ? 'picking' : ''}}"
        >
          <!-- 颜色块 -->
          <view
            class="color-block {{pickingIndex === index ? 'selected' : ''}}"
            style="background-color: {{item.color}};"
            bindtap="selectColorForPicking"
            data-index="{{index}}"
          >
            <view class="color-number">{{index + 1}}</view>
          </view>

          <!-- 颜色信息 -->
          <view class="color-info">
            <view class="color-hex" bindtap="copyColorCode" data-color="{{item.color}}">{{item.color}}</view>
            <view class="edit-btn" bindtap="startColorPicking" data-index="{{index}}">
              <text class="edit-text">编辑吸色</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 没有图片时的占位 -->
      <view wx:else class="no-image-placeholder">
        <view class="placeholder-icon">📷</view>
        <view class="placeholder-text">请选择图片进行颜色提取</view>
      </view>
    </view>
  </view>

  <!-- 图片预览区域 -->
  <view class="image-section">
    <view class="image-container" wx:if="{{imagePath}}"
      catch:touchstart="handleContainerTouchStart"
      catch:touchmove="handleContainerTouchMove"
      catch:touchend="handleContainerTouchEnd"
      catch:touchcancel="handleContainerTouchCancel">
      <image
        class="preview-image"
        src="{{imagePath}}"
        mode="aspectFit"
        binderror="handleImageError"
        bindload="handleImageLoad"
        style="transform: scale({{scale}}) translate({{translateX}}px, {{translateY}}px);"
      ></image>
      <view class="image-loading" wx:if="{{imageLoading}}">
        <view class="loading-spinner"></view>
      </view>

      <!-- 放大镜效果 -->
      <view class="magnifier-container" wx:if="{{magnifierVisible}}">
        <!-- 连接线 -->
        <view class="connector-line" style="left: {{targetX}}px; top: {{targetY}}px; width: {{Math.sqrt(Math.pow(magnifierX - targetX, 2) + Math.pow(magnifierY - targetY, 2))}}px; transform: rotate({{Math.atan2(magnifierY - targetY, magnifierX - targetX) * 180 / Math.PI}}deg); transform-origin: 0 50%;"></view>

        <!-- 目标点 -->
        <view class="target-point" style="left: {{targetX}}px; top: {{targetY}}px;"></view>

        <!-- 放大镜 -->
        <view class="magnifier" style="left: {{magnifierX}}px; top: {{magnifierY}}px;">
          <view class="magnifier-content">
            <canvas
              type="2d"
              id="magnifierCanvas"
              class="magnifier-canvas"
              style="width: 120rpx; height: 120rpx;"
            ></canvas>
            <!-- 十字线 -->
            <view class="magnifier-crosshair-h"></view>
            <view class="magnifier-crosshair-v"></view>
            <!-- 当前颜色指示器 -->
            <view class="color-indicator" style="background-color: {{currentPickedColor || '#ffffff'}};"></view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 取色模式下的颜色信息显示 -->
  <view class="color-info-panel" wx:if="{{pickingMode}}">
    <view class="color-info-card">
      <!-- 颜色预览块 -->
      <view class="color-preview-block" style="background-color: {{currentPickedColor}}"></view>

      <!-- 颜色值信息 -->
      <view class="color-values">
        <view class="color-value-item">
          <view class="color-value-hex" bindtap="copyPickedColor">{{currentPickedColor || '#ffffff'}}</view>
          <view class="color-value-label">HEX</view>
        </view>
        <view class="color-value-item" wx:if="{{currentPickedRgb}}">
          <view class="color-value-rgb" bindtap="copyPickedRgb">{{currentPickedRgb}}</view>
          <view class="color-value-label">RGB</view>
        </view>
      </view>

      <!-- 取色操作按钮 -->
      <view class="picking-actions">
        <view class="picking-cancel-btn" bindtap="cancelColorPick">取消</view>
        <view class="picking-confirm-btn" bindtap="confirmColorPick">确认</view>
      </view>
    </view>
  </view>

  <!-- 底部按钮区域 -->
  <view class="btn-container" wx:if="{{!pickingMode}}">
    <button class="next-btn" bindtap="confirmSelection">确认选择并返回</button>
  </view>

  <!-- 隐藏的canvas用于图片处理 -->
  <canvas type="2d" id="colorAnalysisCanvas" style="width: 200px; height: 200px; position: absolute; left: -9999px;"></canvas>
  <canvas type="2d" id="offscreenCanvas" style="width: 200px; height: 200px; position: absolute; left: -9999px;"></canvas>
</view>
