// pages/skinToneReport/skinToneReport.js
const colorUtils = require('../../utils/colorUtils');
const { storageCache } = require('../../utils/storageCache');

// 肤色色卡一数据
const SKIN_TONE_COLORS_ONE = [
  { name: '偏红冷一白', color: '#F5EBE2' },
  { name: '偏红冷二白', color: '#ECD9C9' },
  { name: '偏红暖一白', color: '#F4D6BC' },
  { name: '偏红暖二白', color: '#DBAF92' },
  { name: '中性冷一白', color: '#F1E0D6' },
  { name: '中性冷二白', color: '#E8BBA8' },
  { name: '中性暖一白', color: '#F2E1C3' },
  { name: '中性暖二白', color: '#EAC7A7' },
  { name: '偏绿冷一白', color: '#F2E5D5' },
  { name: '偏绿冷二白', color: '#E9C9B0' },
  { name: '偏绿暖一白', color: '#EAD9BB' },
  { name: '偏绿暖二白', color: '#DECAAF' }
];

// 肤色色卡二数据
const SKIN_TONE_COLORS_TWO = [
  { name: '粉一白', color: '#FAE7EA' },
  { name: '粉二白', color: '#F4D1CB' },
  { name: '粉三白', color: '#E0B1A1' },
  { name: '黄一白', color: '#FCE6CE' },
  { name: '黄二白', color: '#FBD8B2' },
  { name: '黄三白', color: '#F8C58C' },
  { name: '中一白', color: '#FCDFCD' },
  { name: '中二白', color: '#F2C7B4' },
  { name: '中三白', color: '#E8B59A' },
  { name: '橄榄一白', color: '#FEF6E1' },
  { name: '橄榄二白', color: '#F4E6C9' },
  { name: '橄榄三白', color: '#DEC99E' }
];

Page({
  data: {
    userColor: '', // 用户选择的颜色（单色模式）或平均颜色（多色模式）
    userTextColor: '#000000', // 用户颜色的文字颜色
    selectedColors: [], // 用户选择的所有颜色（多色模式）
    isMultiColor: false, // 是否为多色模式
    closestMatchOne: null, // 肤色色卡一最相似的匹配
    allSimilaritiesOne: [], // 肤色色卡一所有相似度数据
    closestMatchTwo: null, // 肤色色卡二最相似的匹配
    allSimilaritiesTwo: [], // 肤色色卡二所有相似度数据
    reportGenerated: false, // 报告是否已生成
    expandedOne: false, // 肤色色卡一相似度列表是否展开
    expandedTwo: false, // 肤色色卡二相似度列表是否展开
    isSharedEntry: false, // 是否为分享进入
    // 动态加载的色卡数据
    skinToneColorsOne: SKIN_TONE_COLORS_ONE,
    skinToneColorsTwo: SKIN_TONE_COLORS_TWO
  },

  onLoad: function (options) {
    console.log('skinToneReport页面加载，参数:', options);

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '肤色测试报告'
    });

    // 初始化色卡数据
    this.initSkinToneCards();

    // 检查是否为分享进入
    if (options.shared === '1') {
      // 分享进入，从URL参数重建报告
      this.handleSharedEntry(options);
    } else if (options.colorData) {
      // 新的多肤色数据格式
      try {
        const colorData = JSON.parse(decodeURIComponent(options.colorData));
        console.log('从URL参数解析到的颜色数据:', colorData);
        this.generateReportFromColorData(colorData);
      } catch (error) {
        console.error('解析颜色数据失败:', error);
        // 尝试从本地存储获取数据
        this.tryLoadFromStorage();
      }
    } else if (options.color) {
      // 兼容旧的单色格式
      const userColor = decodeURIComponent(options.color);
      console.log('使用单色格式:', userColor);
      this.generateReport(userColor);
    } else {
      // 如果没有颜色参数，尝试从本地存储获取数据
      console.log('没有URL参数，尝试从本地存储获取数据');
      this.tryLoadFromStorage();
    }
  },

  /**
   * 尝试从本地存储加载数据（使用缓存）
   */
  tryLoadFromStorage: function() {
    try {
      const storedData = storageCache.getStorageSync('skinToneTestData');
      console.log('从本地存储获取到的数据:', storedData);

      if (storedData && storedData.averageColor) {
        console.log('使用本地存储的数据生成报告');
        this.generateReportFromColorData(storedData);

        // 清除本地存储的数据，避免重复使用
        storageCache.removeStorageSync('skinToneTestData');
      } else {
        console.log('本地存储中没有有效数据');
        this.showErrorAndGoBack('缺少颜色参数');
      }
    } catch (error) {
      console.error('从本地存储加载数据失败:', error);
      this.showErrorAndGoBack('数据加载失败');
    }
  },

  /**
   * 初始化色卡数据（使用缓存）
   */
  initSkinToneCards: function() {
    try {
      // 从本地存储加载自定义色卡数据（使用缓存）
      const customColorsOne = storageCache.getStorageSync('skinToneColorsOne');
      const customColorsTwo = storageCache.getStorageSync('skinToneColorsTwo');

      this.setData({
        skinToneColorsOne: customColorsOne && customColorsOne.length > 0 ? customColorsOne : SKIN_TONE_COLORS_ONE,
        skinToneColorsTwo: customColorsTwo && customColorsTwo.length > 0 ? customColorsTwo : SKIN_TONE_COLORS_TWO
      });

      console.log('[SkinToneReport] 色卡数据初始化完成');
    } catch (error) {
      console.error('初始化色卡数据失败', error);
      // 出错时使用默认数据
      this.setData({
        skinToneColorsOne: SKIN_TONE_COLORS_ONE,
        skinToneColorsTwo: SKIN_TONE_COLORS_TWO
      });
    }
  },

  // 处理分享进入
  handleSharedEntry: function(options) {
    try {
      // 检查是否有分享ID
      if (options.shareId) {
        // 基于分享ID获取完整数据
        const shareData = this.getShareData(options.shareId);

        if (!shareData || !shareData.userColor) {
          this.showErrorAndGoBack('分享链接已过期或无效');
          return;
        }

        // 设置完整的分享数据，包括色卡数据
        this.setData({
          isMultiColor: shareData.isMultiColor,
          labValues: shareData.labValues,
          rgbValues: shareData.rgbValues,
          isSharedEntry: true, // 标记为分享进入
          // 使用分享者的色卡数据
          skinToneColorsOne: shareData.skinToneColorsOne || SKIN_TONE_COLORS_ONE,
          skinToneColorsTwo: shareData.skinToneColorsTwo || SKIN_TONE_COLORS_TWO
        });

        // 重新生成报告
        this.generateReport(shareData.userColor);

        // 显示分享进入提示
        wx.showToast({
          title: '查看朋友的肤色测试报告',
          icon: 'none',
          duration: 2000
        });
      } else if (options.shareData) {
        // 新的完整分享数据格式（URL参数）
        const shareData = JSON.parse(decodeURIComponent(options.shareData));

        if (!shareData.userColor) {
          this.showErrorAndGoBack('分享数据不完整');
          return;
        }

        // 设置完整的分享数据，包括色卡数据
        this.setData({
          isMultiColor: shareData.isMultiColor,
          labValues: shareData.labValues,
          rgbValues: shareData.rgbValues,
          isSharedEntry: true, // 标记为分享进入
          // 使用分享者的色卡数据
          skinToneColorsOne: shareData.skinToneColorsOne || SKIN_TONE_COLORS_ONE,
          skinToneColorsTwo: shareData.skinToneColorsTwo || SKIN_TONE_COLORS_TWO
        });

        // 重新生成报告
        this.generateReport(shareData.userColor);

        // 显示分享进入提示
        wx.showToast({
          title: '查看朋友的肤色测试报告',
          icon: 'none',
          duration: 2000
        });
      } else {
        // 兼容旧的分享数据格式
        const userColor = decodeURIComponent(options.userColor || '');
        const labValues = decodeURIComponent(options.labValues || '');
        const rgbValues = decodeURIComponent(options.rgbValues || '');
        const isMultiColor = options.isMultiColor === '1';

        if (!userColor) {
          this.showErrorAndGoBack('分享数据不完整');
          return;
        }

        // 设置基础数据（使用默认色卡数据）
        this.setData({
          isMultiColor: isMultiColor,
          labValues: labValues,
          rgbValues: rgbValues,
          isSharedEntry: true // 标记为分享进入
        });

        // 重新生成报告
        this.generateReport(userColor);

        // 显示分享进入提示
        wx.showToast({
          title: '查看朋友的肤色测试报告',
          icon: 'none',
          duration: 2000
        });
      }
    } catch (error) {
      console.error('处理分享数据失败:', error);
      this.showErrorAndGoBack('分享数据解析失败');
    }
  },

  // 显示错误信息并返回上一页
  showErrorAndGoBack: function(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });

    // 2秒后返回上一页
    setTimeout(() => {
      wx.navigateBack();
    }, 2000);
  },

  // 从多肤色数据生成报告
  generateReportFromColorData: function(colorData) {
    const { selectedColors, averageColor, isMultiColor } = colorData;

    this.setData({
      selectedColors: selectedColors,
      isMultiColor: isMultiColor
    });

    // 使用平均颜色进行匹配分析
    this.generateReport(averageColor);
  },

  // 生成测试报告
  generateReport: function(userColor) {
    // 计算用户颜色的文字颜色
    const userTextColor = colorUtils.getTextColorForBackground(userColor);

    // 计算RGB和LAB值
    const rgb = colorUtils.hexToRgb(userColor);
    const lab = colorUtils.rgbToLab(rgb);

    const rgbValues = `${rgb.r}, ${rgb.g}, ${rgb.b}`;
    const labValues = `${lab.l.toFixed(1)}, ${lab.a.toFixed(1)}, ${lab.b.toFixed(1)}`;

    // 使用动态加载的色卡数据
    const { skinToneColorsOne, skinToneColorsTwo } = this.data;

    // 找到肤色色卡一最相似的匹配
    const closestMatchOne = colorUtils.findClosestColor(userColor, skinToneColorsOne);
    if (closestMatchOne) {
      closestMatchOne.level = this.getSimilarityLevel(closestMatchOne.similarity);
    }

    // 计算与肤色色卡一所有颜色的相似度
    const allSimilaritiesOne = colorUtils.calculateAllSimilarities(userColor, skinToneColorsOne);
    allSimilaritiesOne.forEach(item => {
      item.similarityColor = this.getSimilarityColor(item.similarity);
      item.similarityLevel = this.getSimilarityLevel(item.similarity);
    });

    // 找到肤色色卡二最相似的匹配
    const closestMatchTwo = colorUtils.findClosestColor(userColor, skinToneColorsTwo);
    if (closestMatchTwo) {
      closestMatchTwo.level = this.getSimilarityLevel(closestMatchTwo.similarity);
    }

    // 计算与肤色色卡二所有颜色的相似度
    const allSimilaritiesTwo = colorUtils.calculateAllSimilarities(userColor, skinToneColorsTwo);
    allSimilaritiesTwo.forEach(item => {
      item.similarityColor = this.getSimilarityColor(item.similarity);
      item.similarityLevel = this.getSimilarityLevel(item.similarity);
    });

    // 生成肤色分析
    const skinAnalysis = this.generateSkinAnalysis(lab);

    // 更新页面数据
    this.setData({
      userColor: userColor,
      userTextColor: userTextColor,
      rgbValues: rgbValues,
      labValues: labValues,
      closestMatchOne: closestMatchOne,
      allSimilaritiesOne: allSimilaritiesOne,
      closestMatchTwo: closestMatchTwo,
      allSimilaritiesTwo: allSimilaritiesTwo,
      skinAnalysis: skinAnalysis,
      reportGenerated: true
    });
  },

  // 复制颜色代码
  copyColorCode: function(e) {
    const { userColor, rgbValues, labValues } = this.data;

    // 构建完整的色值信息
    const colorInfo = `HEX: ${userColor}\nRGB: ${rgbValues}\nLAB: ${labValues}`;

    wx.setClipboardData({
      data: colorInfo,
      success: () => {
        wx.showToast({
          title: '色值信息已复制',
          icon: 'success',
          duration: 1500
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none',
          duration: 1500
        });
      }
    });
  },

  // 重新测试
  retestSkinTone: function() {
    // 检查是否为分享进入
    if (this.data.isSharedEntry) {
      // 分享进入的用户，直接跳转到肤色测试页面
      wx.redirectTo({
        url: '/clothing-package/pages/skinToneTest/skinToneTest',
        success: () => {
          wx.showToast({
            title: '开始新的肤色测试',
            icon: 'none',
            duration: 1500
          });
        },
        fail: () => {
          // 如果redirectTo失败，尝试使用navigateTo
          wx.navigateTo({
            url: '/clothing-package/pages/skinToneTest/skinToneTest',
            fail: () => {
              wx.showToast({
                title: '跳转失败，请重试',
                icon: 'none',
                duration: 1500
              });
            }
          });
        }
      });
    } else {
      // 正常流程进入的用户，返回上一页
      const pages = getCurrentPages();
      if (pages.length > 1) {
        wx.navigateBack();
      } else {
        // 如果没有上一页，也跳转到肤色测试页面
        wx.redirectTo({
          url: '/clothing-package/pages/skinToneTest/skinToneTest'
        });
      }
    }
  },

  /**
   * 生成分享图片 - 使用Canvas 2D API
   */
  generateShareImage: async function() {
    console.log('开始生成分享图片');

    wx.showLoading({
      title: '生成分享图片中...',
      mask: true
    });

    try {
      // 获取Canvas 2D上下文
      const query = wx.createSelectorQuery();
      const canvasRes = await new Promise((resolve) => {
        query.select('#shareCanvas')
          .fields({ node: true, size: true })
          .exec((res) => {
            resolve(res[0]);
          });
      });

      if (!canvasRes || !canvasRes.node) {
        wx.hideLoading();
        wx.showToast({
          title: '分享图片生成失败',
          icon: 'none'
        });
        return;
      }

      const canvas = canvasRes.node;
      const ctx = canvas.getContext('2d');

      // 设置Canvas尺寸（高分辨率，匹配WXML）
      let dpr = 2; // 默认值
      try {
        const deviceInfo = wx.getDeviceInfo();
        dpr = deviceInfo.pixelRatio || 2;
      } catch (error) {
        console.warn('获取设备信息失败，使用默认pixelRatio:', error);
      }
      const canvasWidth = 750; // 2倍分辨率宽度
      const canvasHeight = 3800; // 增加高度，确保底部内容完整显示

      canvas.width = canvasWidth * dpr;
      canvas.height = canvasHeight * dpr;
      ctx.scale(dpr, dpr);

      console.log('Canvas 2D尺寸:', canvasWidth, 'x', canvasHeight, 'DPR:', dpr);

      // 绘制完整的分享图片
      this.drawCompleteShareImage(ctx, canvas, canvasWidth, canvasHeight);

    } catch (error) {
      wx.hideLoading();
      console.error('生成分享图片失败', error);
      wx.showToast({
        title: '生成图片失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  /**
   * 绘制完整的分享图片 - 使用Canvas 2D API
   */
  drawCompleteShareImage: function(ctx, canvas, width, canvasHeight) {
    console.log('开始绘制完整分享图片');

    // 获取数据，确保使用当前页面的实际数据
    let { userColor, labValues, rgbValues, allSimilaritiesOne, allSimilaritiesTwo, skinAnalysis, skinToneColorsOne, skinToneColorsTwo } = this.data;

    console.log('分享图片生成 - 色卡数据检查:');
    console.log('skinToneColorsOne长度:', skinToneColorsOne ? skinToneColorsOne.length : 0);
    console.log('skinToneColorsTwo长度:', skinToneColorsTwo ? skinToneColorsTwo.length : 0);
    console.log('allSimilaritiesOne长度:', allSimilaritiesOne ? allSimilaritiesOne.length : 0);
    console.log('allSimilaritiesTwo长度:', allSimilaritiesTwo ? allSimilaritiesTwo.length : 0);

    // 如果没有基础数据，使用测试数据
    if (!userColor) {
      userColor = '#F2C7B4';
      labValues = '70.5, 8.2, 15.3';
      rgbValues = '242, 199, 180';
    }

    if (!skinAnalysis) {
      skinAnalysis = {
        lValue: '70.5',
        aValue: '8.2',
        bValue: '15.3',
        lCategory: '标准肤色',
        aCategory: '轻微红调',
        bCategory: '暖黄调',
        skincareSuggestions: [
          '使用温和的清洁产品',
          '注重保湿和防晒',
          '选择适合的美白产品'
        ],
        makeupSuggestions: {
          foundation: '选择暖调粉底液，如象牙白或自然色',
          concealer: '使用比肤色浅一号的遮瑕膏',
          lip: '适合暖调唇釉，如珊瑚色、蜜桃色'
        }
      };
    }

    // 如果没有相似度数据，使用当前色卡数据重新计算
    if (!allSimilaritiesOne || allSimilaritiesOne.length === 0) {
      if (skinToneColorsOne && skinToneColorsOne.length > 0) {
        allSimilaritiesOne = colorUtils.calculateAllSimilarities(userColor, skinToneColorsOne);
        allSimilaritiesOne.forEach(item => {
          item.similarityColor = this.getSimilarityColor(item.similarity);
          item.similarityLevel = this.getSimilarityLevel(item.similarity);
        });
      } else {
        // 使用默认色卡数据
        allSimilaritiesOne = colorUtils.calculateAllSimilarities(userColor, SKIN_TONE_COLORS_ONE);
        allSimilaritiesOne.forEach(item => {
          item.similarityColor = this.getSimilarityColor(item.similarity);
          item.similarityLevel = this.getSimilarityLevel(item.similarity);
        });
      }
    }

    if (!allSimilaritiesTwo || allSimilaritiesTwo.length === 0) {
      if (skinToneColorsTwo && skinToneColorsTwo.length > 0) {
        allSimilaritiesTwo = colorUtils.calculateAllSimilarities(userColor, skinToneColorsTwo);
        allSimilaritiesTwo.forEach(item => {
          item.similarityColor = this.getSimilarityColor(item.similarity);
          item.similarityLevel = this.getSimilarityLevel(item.similarity);
        });
      } else {
        // 使用默认色卡数据
        allSimilaritiesTwo = colorUtils.calculateAllSimilarities(userColor, SKIN_TONE_COLORS_TWO);
        allSimilaritiesTwo.forEach(item => {
          item.similarityColor = this.getSimilarityColor(item.similarity);
          item.similarityLevel = this.getSimilarityLevel(item.similarity);
        });
      }
    }

    try {
      // 动态计算所需高度
      const totalHeight = this.calculateShareImageHeight(skinAnalysis, allSimilaritiesOne, allSimilaritiesTwo);
      console.log('计算的总高度:', totalHeight, 'Canvas高度:', canvasHeight);

      // 使用计算高度和Canvas高度的较大值，确保内容完整显示
      const actualHeight = Math.max(totalHeight + 100, canvasHeight); // 添加100px安全边距

      // 清除画布并设置背景
      ctx.clearRect(0, 0, width, actualHeight);
      ctx.fillStyle = '#f8f9fa';
      ctx.fillRect(0, 0, width, actualHeight);

      let currentY = 60; // 2倍间距

      // 绘制标题
      currentY = this.drawShareTitle(ctx, width, currentY);

      // 绘制用户肤色
      currentY = this.drawShareUserColor(ctx, width, currentY, userColor, labValues, rgbValues);

      // 绘制LAB分析
      currentY = this.drawShareLabAnalysis(ctx, width, currentY, skinAnalysis);

      // 绘制护肤建议
      if (skinAnalysis.skincareSuggestions && skinAnalysis.skincareSuggestions.length > 0) {
        currentY = this.drawShareSkincareSuggestions(ctx, width, currentY, skinAnalysis.skincareSuggestions);
      }

      // 绘制美妆建议
      if (skinAnalysis.makeupSuggestions) {
        currentY = this.drawShareMakeupSuggestions(ctx, width, currentY, skinAnalysis.makeupSuggestions);
      }

      // 绘制色卡排名
      currentY = this.drawShareColorRanking(ctx, width, currentY, '色卡1相似度排名', allSimilaritiesOne, userColor);
      currentY = this.drawShareColorRanking(ctx, width, currentY, '色卡2相似度排名', allSimilaritiesTwo, userColor);

      // 绘制底部微信搜索小程序
      console.log('准备绘制底部，当前Y坐标:', currentY);
      currentY = this.drawShareQRCode(ctx, width, currentY);
      console.log('底部绘制完成，最终Y坐标:', currentY);

      console.log('完整分享图片绘制完成');

      // Canvas 2D API不需要调用draw，直接转换图片
      console.log('Canvas 2D绘制完成，实际高度:', actualHeight);
      setTimeout(() => {
        this.canvasToImage2D(canvas, actualHeight);
      }, 500);

    } catch (error) {
      console.error('绘制完整分享图片失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '绘制失败: ' + error.message,
        icon: 'none',
        duration: 3000
      });
    }
  },

  /**
   * 计算分享图片所需高度（2倍分辨率）
   */
  calculateShareImageHeight: function(skinAnalysis, allSimilaritiesOne, allSimilaritiesTwo) {
    let height = 200; // 标题（2倍）
    height += 200; // 用户肤色（2倍）
    height += 280; // LAB分析（2倍）

    if (skinAnalysis.skincareSuggestions) {
      height += 80 + Math.min(skinAnalysis.skincareSuggestions.length, 3) * 50 + 40; // 2倍
    }

    if (skinAnalysis.makeupSuggestions) {
      height += 240; // 美妆建议（2倍）
    }

    // 显示完整12个色卡排名
    if (allSimilaritiesOne) {
      height += 80 + 12 * 80 + 40; // 12个色卡，每个80px高度（增加间距）
    }

    if (allSimilaritiesTwo) {
      height += 80 + 12 * 80 + 40; // 12个色卡，每个80px高度（增加间距）
    }

    height += 70; // 底部微信搜索小程序区域（调整适应更大搜索框）

    return Math.max(height, 3500); // 增加最小高度，确保底部内容完整显示
  },

  /**
   * 绘制分享图片标题（统一字体大小）- Canvas 2D API
   */
  drawShareTitle: function(ctx, width, currentY) {
    ctx.fillStyle = '#2c3e50';
    ctx.font = '36px sans-serif'; // 统一主标题字体
    ctx.textAlign = 'center';
    ctx.fillText('肤色测试报告', width / 2, currentY + 50);

    ctx.fillStyle = '#7f8c8d';
    ctx.font = '24px sans-serif'; // 统一副标题字体
    ctx.fillText('KALA配色 · 专业肤色分析', width / 2, currentY + 100);

    return currentY + 160;
  },

  /**
   * 绘制分享图片用户肤色（统一字体大小）- Canvas 2D API
   */
  drawShareUserColor: function(ctx, width, currentY, userColor, labValues, rgbValues) {
    // 绘制卡片背景
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(40, currentY, width - 80, 160);

    // 绘制肤色色块（移除绿点，色块左移）
    ctx.fillStyle = userColor;
    ctx.fillRect(80, currentY + 20, 80, 80);

    // 绘制色块边框
    ctx.strokeStyle = '#e0e0e0';
    ctx.lineWidth = 2;
    ctx.strokeRect(80, currentY + 20, 80, 80);

    // 绘制肤色信息（位置相应调整）
    ctx.fillStyle = '#2c3e50';
    ctx.font = '24px sans-serif'; // 统一标题字体
    ctx.textAlign = 'left';
    ctx.fillText('您的肤色', 190, currentY + 50);

    ctx.fillStyle = '#666666';
    ctx.font = '20px sans-serif'; // 统一正文字体
    ctx.fillText(userColor + ' | LAB: ' + labValues, 190, currentY + 84);
    ctx.fillText('RGB: ' + rgbValues, 190, currentY + 112);

    return currentY + 200;
  },

  /**
   * 绘制分享图片LAB分析（统一字体大小）- Canvas 2D API
   */
  drawShareLabAnalysis: function(ctx, width, currentY, skinAnalysis) {
    // 绘制卡片背景
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(40, currentY, width - 80, 240);

    // 绘制标题
    ctx.fillStyle = '#2c3e50';
    ctx.font = '24px sans-serif'; // 统一标题字体
    ctx.textAlign = 'left';
    ctx.fillText('LAB肤色分析', 80, currentY + 50);

    let textY = currentY + 100;

    // 绘制L值
    ctx.fillStyle = '#07c160';
    ctx.font = '20px sans-serif'; // 统一绿点字体
    ctx.fillText('•', 80, textY);

    ctx.fillStyle = '#34495e';
    ctx.font = '20px sans-serif'; // 统一正文字体
    ctx.fillText('L值 (亮度): ' + skinAnalysis.lValue + ' - ' + skinAnalysis.lCategory, 110, textY);
    textY += 44;

    // 绘制a值
    ctx.fillStyle = '#07c160';
    ctx.font = '20px sans-serif'; // 统一绿点字体
    ctx.fillText('•', 80, textY);

    ctx.fillStyle = '#34495e';
    ctx.font = '20px sans-serif'; // 统一正文字体
    ctx.fillText('a值 (红绿轴): ' + skinAnalysis.aValue + ' - ' + skinAnalysis.aCategory, 110, textY);
    textY += 44;

    // 绘制b值
    ctx.fillStyle = '#07c160';
    ctx.font = '20px sans-serif'; // 统一绿点字体
    ctx.fillText('•', 80, textY);

    ctx.fillStyle = '#34495e';
    ctx.font = '20px sans-serif'; // 统一正文字体
    ctx.fillText('b值 (黄蓝轴): ' + skinAnalysis.bValue + ' - ' + skinAnalysis.bCategory, 110, textY);

    return currentY + 280;
  },

  /**
   * 绘制分享图片护肤建议（统一字体大小）- Canvas 2D API
   */
  drawShareSkincareSuggestions: function(ctx, width, currentY, suggestions) {
    const displayCount = Math.min(suggestions.length, 3);
    const cardHeight = 80 + displayCount * 50 + 40; // 2倍高度

    // 绘制卡片背景
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(40, currentY, width - 80, cardHeight);

    // 绘制标题
    ctx.fillStyle = '#2c3e50';
    ctx.font = '24px sans-serif'; // 统一标题字体
    ctx.textAlign = 'left';
    ctx.fillText('护肤建议', 80, currentY + 50);

    let textY = currentY + 100;

    for (let i = 0; i < displayCount; i++) {
      ctx.fillStyle = '#07c160';
      ctx.font = '20px sans-serif'; // 统一绿点字体
      ctx.fillText('•', 80, textY);

      ctx.fillStyle = '#666666';
      ctx.font = '20px sans-serif'; // 统一正文字体
      const suggestion = suggestions[i].length > 30 ? suggestions[i].substring(0, 30) + '...' : suggestions[i];
      ctx.fillText(suggestion, 110, textY);
      textY += 50; // 2倍间距
    }

    return currentY + cardHeight + 40;
  },

  /**
   * 绘制分享图片美妆建议（统一字体大小）- Canvas 2D API
   */
  drawShareMakeupSuggestions: function(ctx, width, currentY, makeupSuggestions) {
    const categories = [
      { key: 'foundation', label: '粉底液' },
      { key: 'concealer', label: '遮瑕膏' },
      { key: 'lip', label: '唇釉' }
    ];

    const validCategories = categories.filter(cat => makeupSuggestions[cat.key]);
    if (validCategories.length === 0) return currentY;

    const cardHeight = 80 + validCategories.length * 70 + 40; // 2倍高度

    // 绘制卡片背景
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(40, currentY, width - 80, cardHeight);

    // 绘制标题
    ctx.fillStyle = '#2c3e50';
    ctx.font = '24px sans-serif'; // 统一标题字体
    ctx.textAlign = 'left';
    ctx.fillText('美妆建议', 80, currentY + 50);

    let textY = currentY + 100;

    for (let category of validCategories) {
      ctx.fillStyle = '#07c160';
      ctx.font = '20px sans-serif'; // 统一绿点字体
      ctx.fillText('•', 80, textY);

      ctx.fillStyle = '#34495e';
      ctx.font = '20px sans-serif'; // 统一标题字体
      ctx.fillText(category.label + ':', 110, textY);

      ctx.fillStyle = '#666666';
      ctx.font = '18px sans-serif'; // 统一内容字体
      const content = makeupSuggestions[category.key];
      const shortContent = content.length > 25 ? content.substring(0, 25) + '...' : content;
      ctx.fillText(shortContent, 110, textY + 30);
      textY += 70; // 2倍间距
    }

    return currentY + cardHeight + 40;
  },

  /**
   * 绘制分享图片色卡排名（统一字体，增大色块）- Canvas 2D API
   */
  drawShareColorRanking: function(ctx, width, currentY, title, similarities, userColor) {
    const displayItems = similarities; // 显示全部色卡
    const cardHeight = 80 + displayItems.length * 80 + 40; // 增加行间距到80px

    // 绘制卡片背景
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(40, currentY, width - 80, cardHeight);

    // 绘制标题
    ctx.fillStyle = '#2c3e50';
    ctx.font = '24px sans-serif'; // 统一标题字体
    ctx.textAlign = 'left';
    ctx.fillText(title, 80, currentY + 50);

    let itemY = currentY + 100;

    for (let i = 0; i < displayItems.length; i++) {
      const item = displayItems[i];

      // 绘制排名
      let rankColor = '#95a5a6';
      if (i === 0) rankColor = '#f39c12'; // 金色
      else if (i === 1) rankColor = '#c0c0c0'; // 银色
      else if (i === 2) rankColor = '#cd7f32'; // 铜色

      ctx.fillStyle = rankColor;
      ctx.font = '20px sans-serif'; // 统一排名字体
      ctx.textAlign = 'left';
      ctx.fillText((i + 1).toString(), 80, itemY);

      // 绘制色卡颜色块（进一步增大尺寸，无描边）
      ctx.fillStyle = item.color;
      ctx.fillRect(115, itemY - 30, 60, 60); // 进一步增大到60x60

      // 绘制用户肤色对比圆（增大尺寸）
      ctx.fillStyle = userColor;
      ctx.beginPath();
      ctx.arc(145, itemY, 18, 0, 2 * Math.PI); // 增大半径到18
      ctx.fill();

      // 绘制颜色名称（调整位置适应更大的色块）
      ctx.fillStyle = '#34495e';
      ctx.font = '20px sans-serif'; // 统一正文字体
      ctx.textAlign = 'left';
      const displayName = item.name.length > 10 ? item.name.substring(0, 10) + '...' : item.name;
      ctx.fillText(displayName, 195, itemY); // 向右移动5px

      // 绘制相似度（移除右侧等级文字）
      ctx.fillStyle = this.getSimilarityColor(item.similarity);
      ctx.font = '20px sans-serif'; // 统一相似度字体
      ctx.textAlign = 'right';
      ctx.fillText(item.similarity + '%', width - 80, itemY);

      itemY += 80; // 增加行间距到80px
    }

    return currentY + cardHeight + 40;
  },

  /**
   * 绘制分享图片底部（微信搜索小程序样式）- Canvas 2D API
   */
  drawShareQRCode: function(ctx, width, currentY) {
    console.log('开始绘制微信搜索小程序底部，currentY:', currentY, 'width:', width);

    try {
      // 绘制白色背景区域（增加高度适应更大的搜索框）
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(40, currentY + 5, width - 80, 90);
      console.log('白色背景绘制完成');

      // 计算垂直居中位置
      const centerY = currentY + 50; // 背景区域的垂直中心

      // 计算整体内容的宽度和起始位置，实现水平居中
      const textContent = '微信搜索小程序体验完整功能';
      const fontSize = '20px sans-serif';
      ctx.font = fontSize;
      const textWidth = ctx.measureText(textContent).width;

      const searchBoxWidth = 140; // 搜索框宽度
      const searchBoxHeight = 40; // 搜索框高度
      const gap = 15; // 文字和搜索框之间的间距

      // 计算整体宽度和起始X坐标（水平居中）
      const totalWidth = textWidth + gap + searchBoxWidth;
      const startX = (width - totalWidth) / 2;

      // 绘制"微信搜索小程序体验完整功能"文字
      ctx.fillStyle = '#333333';
      ctx.font = fontSize;
      ctx.textAlign = 'left';
      ctx.fillText(textContent, startX, centerY + 6);
      console.log('微信搜索小程序文字绘制完成');

      // 绘制绿色搜索框（紧贴文字右侧）
      const searchBoxX = startX + textWidth + gap;
      const searchBoxY = centerY - searchBoxHeight/2; // 垂直居中

      ctx.fillStyle = '#07C160';
      ctx.fillRect(searchBoxX, searchBoxY, searchBoxWidth, searchBoxHeight);
      console.log('绿色搜索框绘制完成');

      // 绘制"KALA配色"文字（在搜索框中垂直和水平居中，使用相同字体大小）
      ctx.fillStyle = '#FFFFFF';
      ctx.font = fontSize; // 使用相同的字体大小
      ctx.textAlign = 'center';
      ctx.fillText('KALA配色', searchBoxX + searchBoxWidth/2, centerY + 6); // 与左侧文字基线对齐
      console.log('KALA配色文字绘制完成');

      console.log('微信搜索小程序底部全部绘制完成');
      return currentY + 50; // 调整返回高度适应更大的背景区域

    } catch (error) {
      console.error('绘制微信搜索小程序底部失败:', error);
      return currentY + 30;
    }
  },





  /**
   * 绘制测试图片（确保Canvas能正常工作）
   */
  drawTestImage: function(ctx, width, height) {
    console.log('开始绘制测试图片，尺寸:', width, 'x', height);

    try {
      // 验证上下文并清除画布
      if (!ctx || typeof ctx.clearRect !== 'function') {
        console.error('肤色报告Canvas上下文无效');
        return;
      }
      ctx.clearRect(0, 0, width, height);

      // 设置背景
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, width, height);

      // 绘制标题
      ctx.fillStyle = '#333333';
      ctx.font = '24px sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText('肤色测试报告', width / 2, 60);

      // 绘制副标题
      ctx.fillStyle = '#666666';
      ctx.font = '16px sans-serif';
      ctx.fillText('KALA配色 · 专业肤色分析', width / 2, 90);

      // 绘制一个测试色块
      ctx.fillStyle = '#F2C7B4';
      ctx.fillRect(50, 120, 80, 80);

      // 绘制色块边框
      ctx.strokeStyle = '#cccccc';
      ctx.lineWidth = 1;
      ctx.strokeRect(50, 120, 80, 80);

      // 绘制色块标签
      ctx.fillStyle = '#333333';
      ctx.font = '14px sans-serif';
      ctx.textAlign = 'left';
      ctx.fillText('您的肤色: #F2C7B4', 150, 160);

      // 绘制一些测试文本
      ctx.fillStyle = '#666666';
      ctx.font = '12px sans-serif';
      ctx.fillText('LAB值: 70.5, 8.2, 15.3', 50, 240);
      ctx.fillText('RGB值: 242, 199, 180', 50, 260);

      // 绘制测试列表
      const testItems = [
        'L值 (亮度): 70.5 - 标准肤色',
        'a值 (红绿轴): 8.2 - 轻微红调',
        'b值 (黄蓝轴): 15.3 - 暖黄调'
      ];

      let y = 300;
      for (let i = 0; i < testItems.length; i++) {
        ctx.fillText('• ' + testItems[i], 50, y);
        y += 25;
      }

      // 绘制底部信息
      ctx.fillStyle = '#999999';
      ctx.font = '12px sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText('扫码体验完整功能', width / 2, height - 60);

      // 绘制二维码占位符
      ctx.fillStyle = '#eeeeee';
      ctx.fillRect(width / 2 - 25, height - 50, 50, 50);

      ctx.strokeStyle = '#cccccc';
      ctx.strokeRect(width / 2 - 25, height - 50, 50, 50);

      ctx.fillStyle = '#999999';
      ctx.font = '10px sans-serif';
      ctx.fillText('QR', width / 2, height - 22);

      console.log('测试图片绘制完成');

      // Canvas 2D API不需要调用draw，直接转换图片
      console.log('Canvas 2D绘制完成，等待500ms后转换图片');
      setTimeout(() => {
        this.canvasToImage();
      }, 500);

    } catch (error) {
      console.error('绘制测试图片失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '绘制失败: ' + error.message,
        icon: 'none',
        duration: 3000
      });
    }
  },

  /**
   * 计算Canvas所需高度
   */
  calculateCanvasHeight: function() {
    const { allSimilaritiesOne, allSimilaritiesTwo, skinAnalysis } = this.data;

    let height = 100; // 基础高度（标题）

    // 用户肤色
    height += 100; // 用户肤色卡片

    // LAB分析
    if (skinAnalysis) {
      height += 140; // LAB分析卡片
    }

    // 护肤建议
    if (skinAnalysis && skinAnalysis.skincareSuggestions && skinAnalysis.skincareSuggestions.length > 0) {
      const suggestionCount = Math.min(skinAnalysis.skincareSuggestions.length, 3);
      height += 40 + suggestionCount * 25 + 40; // 护肤建议卡片（增加行高）
    }

    // 美妆建议（精确计算换行后的高度）
    if (skinAnalysis && skinAnalysis.makeupSuggestions) {
      const categories = ['foundation', 'concealer', 'lip'];
      const validCategories = categories.filter(cat => skinAnalysis.makeupSuggestions[cat]);
      if (validCategories.length > 0) {
        let makeupHeight = 60; // 标题区域
        for (let category of categories) {
          if (skinAnalysis.makeupSuggestions[category]) {
            const content = skinAnalysis.makeupSuggestions[category];
            // 估算文本行数（每行约30个字符）
            const estimatedLines = Math.ceil(content.length / 30);
            makeupHeight += 15 + estimatedLines * 12 + 10; // 标题 + 内容行数 + 间距
          }
        }
        makeupHeight += 20; // 底部边距
        height += makeupHeight + 20; // 美妆建议卡片 + 间距
      }
    }

    // 色卡1排名（显示全部）
    if (allSimilaritiesOne && allSimilaritiesOne.length > 0) {
      height += 40 + allSimilaritiesOne.length * 30 + 40; // 色卡1排名（全部显示）
    }

    // 色卡2排名（显示全部）
    if (allSimilaritiesTwo && allSimilaritiesTwo.length > 0) {
      height += 40 + allSimilaritiesTwo.length * 30 + 40; // 色卡2排名（全部显示）
    }

    height += 120; // 底部二维码区域（增加空间）

    return height; // 返回精确计算的高度
  },

  /**
   * 绘制分享图片
   */
  drawShareImage: function(ctx, width, height) {
    let { userColor, labValues, rgbValues, allSimilaritiesOne, allSimilaritiesTwo, skinAnalysis } = this.data;

    // 如果没有数据，使用测试数据
    if (!userColor || !skinAnalysis) {
      console.log('使用测试数据生成分享图片');
      userColor = userColor || '#F2C7B4';
      labValues = labValues || '70.5, 8.2, 15.3';
      rgbValues = rgbValues || '242, 199, 180';

      if (!skinAnalysis) {
        skinAnalysis = {
          lValue: '70.5',
          aValue: '8.2',
          bValue: '15.3',
          lCategory: '标准肤色',
          aCategory: '轻微红调',
          bCategory: '暖黄调',
          skincareSuggestions: [
            '使用温和的清洁产品',
            '注重保湿和防晒',
            '选择适合的美白产品'
          ],
          makeupSuggestions: {
            foundation: '选择暖调粉底液，如象牙白或自然色',
            concealer: '使用比肤色浅一号的遮瑕膏',
            lip: '适合暖调唇釉，如珊瑚色、蜜桃色'
          }
        };
      }

      if (!allSimilaritiesOne || allSimilaritiesOne.length === 0) {
        allSimilaritiesOne = [
          { name: '中二白', color: '#F2C7B4', similarity: 95 },
          { name: '中一白', color: '#FCDFCD', similarity: 88 },
          { name: '粉二白', color: '#F4D1CB', similarity: 82 }
        ];
      }

      if (!allSimilaritiesTwo || allSimilaritiesTwo.length === 0) {
        allSimilaritiesTwo = [
          { name: '中二白', color: '#F2C7B4', similarity: 95 },
          { name: '中一白', color: '#FCDFCD', similarity: 88 },
          { name: '粉二白', color: '#F4D1CB', similarity: 82 }
        ];
      }
    }

    // 调试信息
    console.log('分享图片数据:', {
      userColor,
      labValues,
      rgbValues,
      allSimilaritiesOne: allSimilaritiesOne ? allSimilaritiesOne.length : 0,
      allSimilaritiesTwo: allSimilaritiesTwo ? allSimilaritiesTwo.length : 0,
      skinAnalysis: skinAnalysis ? {
        lValue: skinAnalysis.lValue,
        aValue: skinAnalysis.aValue,
        bValue: skinAnalysis.bValue,
        skincareSuggestions: skinAnalysis.skincareSuggestions ? skinAnalysis.skincareSuggestions.length : 0,
        makeupSuggestions: skinAnalysis.makeupSuggestions ? Object.keys(skinAnalysis.makeupSuggestions).length : 0
      } : 'null'
    });

    // 设置背景
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, width, height);

    let currentY = 30;

    // 绘制标题
    ctx.fillStyle = '#2c3e50';
    ctx.font = 'bold 20px sans-serif';
    ctx.textAlign = 'center';
    ctx.fillText('肤色测试报告', width / 2, currentY + 25);

    ctx.fillStyle = '#7f8c8d';
    ctx.font = '12px sans-serif';
    ctx.fillText('KALA配色 · 专业肤色分析', width / 2, currentY + 45);

    currentY += 70;

    // 绘制用户肤色信息
    if (userColor) {
      const displayLabValues = labValues || '未知';
      const displayRgbValues = rgbValues || '未知';
      currentY = this.drawSimpleUserColor(ctx, width, currentY, userColor, displayLabValues, displayRgbValues);
    } else {
      // 如果没有用户肤色数据，显示提示
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(20, currentY, width - 40, 80);

      ctx.fillStyle = '#999999';
      ctx.font = '14px sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText('暂无肤色数据', width / 2, currentY + 40);
      currentY += 100;
    }

    // 绘制LAB分析
    if (skinAnalysis && (skinAnalysis.lValue || skinAnalysis.aValue || skinAnalysis.bValue)) {
      currentY = this.drawSimpleLabAnalysis(ctx, width, currentY, skinAnalysis);
    } else {
      // 如果没有LAB分析数据，显示提示
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(20, currentY, width - 40, 60);

      ctx.fillStyle = '#999999';
      ctx.font = '14px sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText('暂无LAB分析数据', width / 2, currentY + 35);
      currentY += 80;
    }

    // 绘制护肤建议
    if (skinAnalysis && skinAnalysis.skincareSuggestions && skinAnalysis.skincareSuggestions.length > 0) {
      currentY = this.drawSimpleSkincareSuggestions(ctx, width, currentY, skinAnalysis.skincareSuggestions);
    }

    // 绘制美妆建议
    if (skinAnalysis && skinAnalysis.makeupSuggestions) {
      currentY = this.drawSimpleMakeupSuggestions(ctx, width, currentY, skinAnalysis.makeupSuggestions);
    }

    // 绘制肤色色卡1排名
    if (allSimilaritiesOne && allSimilaritiesOne.length > 0) {
      currentY = this.drawSimpleColorCardRanking(ctx, width, currentY, '肤色色卡1相似度排名', allSimilaritiesOne, userColor);
    }

    // 绘制肤色色卡2排名
    if (allSimilaritiesTwo && allSimilaritiesTwo.length > 0) {
      currentY = this.drawSimpleColorCardRanking(ctx, width, currentY, '肤色色卡2相似度排名', allSimilaritiesTwo, userColor);
    }

    // 绘制底部二维码
    this.drawSimpleQRCode(ctx, width, height);

    // Canvas 2D API不需要调用draw，直接转换图片
    setTimeout(() => {
      this.canvasToImage();
    }, 500);
  },

  /**
   * 绘制简单的用户肤色
   */
  drawSimpleUserColor: function(ctx, width, currentY, userColor, labValues, rgbValues) {
    // 绘制卡片背景
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(20, currentY, width - 40, 80);

    // 绘制绿点
    ctx.fillStyle = '#07c160';
    ctx.font = '14px sans-serif';
    ctx.textAlign = 'left';
    ctx.fillText('•', 40, currentY + 25);

    // 绘制肤色色块
    const colorSize = 40;
    const colorX = 60;
    const colorY = currentY + 10;

    ctx.fillStyle = userColor;
    ctx.fillRect(colorX, colorY, colorSize, colorSize);

    // 绘制肤色信息
    const textX = colorX + colorSize + 15;

    ctx.fillStyle = '#2c3e50';
    ctx.font = 'bold 14px sans-serif';
    ctx.fillText('您的肤色', textX, currentY + 25);

    ctx.fillStyle = '#666666';
    ctx.font = '11px sans-serif';
    ctx.fillText(`${userColor} | LAB: ${labValues}`, textX, currentY + 42);
    ctx.fillText(`RGB: ${rgbValues}`, textX, currentY + 56);

    return currentY + 100;
  },

  /**
   * 绘制简单的LAB分析
   */
  drawSimpleLabAnalysis: function(ctx, width, currentY, skinAnalysis) {
    // 绘制卡片背景
    const cardHeight = 120;
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(20, currentY, width - 40, cardHeight);

    // 绘制标题
    ctx.fillStyle = '#2c3e50';
    ctx.font = 'bold 14px sans-serif';
    ctx.textAlign = 'left';
    ctx.fillText('LAB肤色分析', 40, currentY + 25);

    let textY = currentY + 45;
    const leftMargin = 40;

    // 绘制L值
    if (skinAnalysis.lValue) {
      ctx.fillStyle = '#07c160';
      ctx.font = '12px sans-serif';
      ctx.fillText('•', leftMargin, textY);

      ctx.fillStyle = '#34495e';
      ctx.font = '11px sans-serif';
      ctx.fillText(`L值 (亮度): ${skinAnalysis.lValue} - ${skinAnalysis.lCategory}`, leftMargin + 15, textY);
      textY += 20;
    }

    // 绘制a值
    if (skinAnalysis.aValue) {
      ctx.fillStyle = '#07c160';
      ctx.font = '12px sans-serif';
      ctx.fillText('•', leftMargin, textY);

      ctx.fillStyle = '#34495e';
      ctx.font = '11px sans-serif';
      ctx.fillText(`a值 (红绿轴): ${skinAnalysis.aValue} - ${skinAnalysis.aCategory}`, leftMargin + 15, textY);
      textY += 20;
    }

    // 绘制b值
    if (skinAnalysis.bValue) {
      ctx.fillStyle = '#07c160';
      ctx.font = '12px sans-serif';
      ctx.fillText('•', leftMargin, textY);

      ctx.fillStyle = '#34495e';
      ctx.font = '11px sans-serif';
      ctx.fillText(`b值 (黄蓝轴): ${skinAnalysis.bValue} - ${skinAnalysis.bCategory}`, leftMargin + 15, textY);
      textY += 20;
    }

    return currentY + cardHeight + 20;
  },

  /**
   * 绘制简单的护肤建议
   */
  drawSimpleSkincareSuggestions: function(ctx, width, currentY, suggestions) {
    // 绘制卡片背景
    const cardHeight = 40 + Math.min(suggestions.length, 3) * 20 + 20;
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(20, currentY, width - 40, cardHeight);

    // 绘制标题
    ctx.fillStyle = '#2c3e50';
    ctx.font = 'bold 14px sans-serif';
    ctx.textAlign = 'left';
    ctx.fillText('护肤建议', 40, currentY + 25);

    let textY = currentY + 45;
    const leftMargin = 40;

    // 只显示前3条建议
    for (let i = 0; i < Math.min(suggestions.length, 3); i++) {
      const suggestion = suggestions[i];

      ctx.fillStyle = '#07c160';
      ctx.font = '12px sans-serif';
      ctx.fillText('•', leftMargin, textY);

      ctx.fillStyle = '#666666';
      ctx.font = '11px sans-serif';
      const shortSuggestion = suggestion.length > 35 ? suggestion.substring(0, 35) + '...' : suggestion;
      ctx.fillText(shortSuggestion, leftMargin + 15, textY);
      textY += 20;
    }

    return currentY + cardHeight + 20;
  },

  /**
   * 绘制简单的美妆建议
   */
  drawSimpleMakeupSuggestions: function(ctx, width, currentY, makeupSuggestions) {
    const categories = [
      { key: 'foundation', label: '粉底液选择' },
      { key: 'concealer', label: '遮瑕膏选择' },
      { key: 'blush', label: '腮红选择' },
      { key: 'lip', label: '唇妆选择' },
      { key: 'eyeshadow', label: '眼影选择' }
    ];

    const validCategories = categories.filter(cat => makeupSuggestions[cat.key]);
    if (validCategories.length === 0) return currentY;

    // 动态计算卡片高度
    let cardHeight = 60; // 标题区域
    for (let category of validCategories) {
      const content = makeupSuggestions[category.key];
      const lines = this.wrapText(ctx, content, width - 100, '10px sans-serif');
      cardHeight += 15 + lines.length * 12 + 10; // 标题 + 内容行数 + 间距
    }
    cardHeight += 20; // 底部边距

    // 绘制卡片背景
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(20, currentY, width - 40, cardHeight);

    // 绘制标题
    ctx.fillStyle = '#2c3e50';
    ctx.font = 'bold 14px sans-serif';
    ctx.textAlign = 'left';
    ctx.fillText('美妆建议', 40, currentY + 25);

    let textY = currentY + 45;
    const leftMargin = 40;

    for (let category of validCategories) {
      // 绘制绿点
      ctx.fillStyle = '#07c160';
      ctx.font = '12px sans-serif';
      ctx.fillText('•', leftMargin, textY);

      // 绘制分类标题
      ctx.fillStyle = '#34495e';
      ctx.font = 'bold 11px sans-serif';
      ctx.fillText(category.label, leftMargin + 15, textY);
      textY += 15;

      // 绘制内容（支持换行）
      ctx.fillStyle = '#666666';
      ctx.font = '10px sans-serif';
      const content = makeupSuggestions[category.key];
      const contentLines = this.wrapText(ctx, content, width - 100, '10px sans-serif');
      for (let line of contentLines) {
        ctx.fillText(line, leftMargin + 15, textY);
        textY += 12;
      }
      textY += 10; // 项目间距
    }

    return currentY + cardHeight + 20;
  },

  /**
   * 绘制简单的色卡排名
   */
  drawSimpleColorCardRanking: function(ctx, width, currentY, title, similarities, userColor) {
    // 显示全部色块
    const displayItems = similarities;
    const cardHeight = 40 + displayItems.length * 30 + 20;

    // 绘制卡片背景
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(20, currentY, width - 40, cardHeight);

    // 绘制标题
    ctx.fillStyle = '#2c3e50';
    ctx.font = 'bold 14px sans-serif';
    ctx.textAlign = 'left';
    ctx.fillText(title, 40, currentY + 25);

    let itemY = currentY + 50;
    const leftMargin = 40;

    for (let i = 0; i < displayItems.length; i++) {
      const item = displayItems[i];

      // 绘制排名（前三名特殊颜色）
      let rankColor = '#95a5a6';
      if (i === 0) rankColor = '#f39c12'; // 金色
      else if (i === 1) rankColor = '#c0c0c0'; // 银色
      else if (i === 2) rankColor = '#cd7f32'; // 铜色

      ctx.fillStyle = rankColor;
      ctx.font = 'bold 12px sans-serif';
      ctx.textAlign = 'left';
      ctx.fillText(`${i + 1}`, leftMargin, itemY);

      // 绘制色卡颜色块（矩形背景）
      const colorSize = 24;
      const colorX = leftMargin + 25;
      const colorY = itemY - 12;

      ctx.fillStyle = item.color;
      ctx.fillRect(colorX, colorY, colorSize, colorSize);

      // 绘制用户肤色对比圆（圆形前景）
      const circleSize = 16;
      const circleX = colorX + (colorSize - circleSize) / 2;
      const circleY = colorY + (colorSize - circleSize) / 2;

      ctx.fillStyle = userColor;
      ctx.beginPath();
      ctx.arc(circleX + circleSize / 2, circleY + circleSize / 2, circleSize / 2, 0, 2 * Math.PI);
      ctx.fill();

      // 绘制颜色名称
      ctx.fillStyle = '#34495e';
      ctx.font = '11px sans-serif';
      ctx.textAlign = 'left';
      const nameX = colorX + colorSize + 12;
      const displayName = item.name.length > 10 ? item.name.substring(0, 10) + '...' : item.name;
      ctx.fillText(displayName, nameX, itemY);

      // 绘制相似度
      ctx.fillStyle = this.getSimilarityColor(item.similarity);
      ctx.font = 'bold 11px sans-serif';
      ctx.textAlign = 'right';
      ctx.fillText(`${item.similarity}%`, width - 80, itemY);

      // 绘制匹配度等级
      ctx.fillStyle = '#7f8c8d';
      ctx.font = '9px sans-serif';
      ctx.fillText(this.getSimilarityLevel(item.similarity), width - 40, itemY);

      itemY += 30;
    }

    return currentY + cardHeight + 20;
  },

  /**
   * 获取相似度颜色
   */
  getSimilarityColor: function(similarity) {
    if (similarity >= 90) return '#27ae60';
    if (similarity >= 80) return '#2ecc71';
    if (similarity >= 70) return '#f39c12';
    if (similarity >= 60) return '#e67e22';
    if (similarity >= 50) return '#e74c3c';
    return '#95a5a6';
  },

  /**
   * 获取相似度等级
   */
  getSimilarityLevel: function(similarity) {
    if (similarity >= 90) return '极佳';
    if (similarity >= 80) return '很好';
    if (similarity >= 70) return '较好';
    if (similarity >= 60) return '一般';
    if (similarity >= 50) return '较差';
    return '很差';
  },

  /**
   * 绘制简单的二维码
   */
  drawSimpleQRCode: function(ctx, width, height) {
    const bottomY = height - 90;

    ctx.fillStyle = '#95a5a6';
    ctx.font = '12px sans-serif';
    ctx.textAlign = 'center';
    ctx.fillText('微信搜索小程序体验完整功能', width / 2, bottomY);

    // 绘制二维码
    const qrSize = 60;
    const qrX = (width - qrSize) / 2;
    const qrY = bottomY + 15;

    // 绘制白色背景
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(qrX - 3, qrY - 3, qrSize + 6, qrSize + 6);

    // 尝试绘制真实的二维码图片
    const self = this;
    wx.getImageInfo({
      src: '/images/QR/QR.jpg',
      success: function(res) {
        // 图片加载成功，绘制真实二维码
        ctx.drawImage(res.path, qrX, qrY, qrSize, qrSize);
        ctx.draw(true); // 重新绘制
      },
      fail: function() {
        // 图片加载失败，绘制占位符
        self.drawQRPlaceholder(ctx, qrX, qrY, qrSize);
        ctx.draw(true);
      }
    });
  },

  /**
   * 绘制二维码占位符
   */
  drawQRPlaceholder: function(ctx, x, y, size) {
    // 绘制灰色背景
    ctx.fillStyle = '#f0f0f0';
    ctx.fillRect(x, y, size, size);

    // 绘制边框
    ctx.strokeStyle = '#ddd';
    ctx.lineWidth = 1;
    ctx.strokeRect(x, y, size, size);

    // 绘制文字
    ctx.fillStyle = '#666666';
    ctx.font = '10px sans-serif';
    ctx.textAlign = 'center';
    ctx.fillText('小程序码', x + size / 2, y + size / 2 + 3);
  },

  /**
   * 绘制综合分析模块（合并用户肤色、LAB分析、护肤建议、美妆建议）
   */
  drawCombinedAnalysisModule: function(ctx, width, startY, userColor, labValues, rgbValues, skinAnalysis) {
    let currentY = startY;
    let moduleStartY = currentY;

    // 绘制模块背景（先用临时高度，后面会调整）
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, currentY, width, 50); // 临时高度

    // 绘制模块标题
    ctx.fillStyle = '#2c3e50';
    ctx.font = 'bold 16px sans-serif';
    ctx.textAlign = 'left';
    ctx.fillText('肤色分析报告', 40, currentY + 30);
    currentY += 45;

    const leftMargin = 40;

    // 1. 绘制用户肤色
    currentY = this.drawUserColorInModule(ctx, width, currentY, userColor, labValues, rgbValues, leftMargin);
    currentY += 10;

    // 2. 绘制LAB分析
    if (skinAnalysis.lValue) {
      currentY = this.drawLabAnalysisItem(ctx, width, currentY, 'L值 (亮度)', skinAnalysis.lValue, skinAnalysis.lCategory, skinAnalysis.lDescription, leftMargin);
    }

    if (skinAnalysis.aValue) {
      currentY = this.drawLabAnalysisItem(ctx, width, currentY, 'a值 (红绿轴)', skinAnalysis.aValue, skinAnalysis.aCategory, skinAnalysis.aDescription, leftMargin);
    }

    if (skinAnalysis.bValue) {
      currentY = this.drawLabAnalysisItem(ctx, width, currentY, 'b值 (黄蓝轴)', skinAnalysis.bValue, skinAnalysis.bCategory, skinAnalysis.bDescription, leftMargin);
    }

    // 3. 绘制护肤建议
    if (skinAnalysis.skincareSuggestions && skinAnalysis.skincareSuggestions.length > 0) {
      currentY = this.drawSectionTitle(ctx, currentY, '护肤建议', leftMargin);
      for (let suggestion of skinAnalysis.skincareSuggestions) {
        currentY = this.drawSuggestionItem(ctx, width, currentY, suggestion, leftMargin);
      }
      currentY += 5;
    }

    // 4. 绘制美妆建议
    if (skinAnalysis.makeupSuggestions) {
      currentY = this.drawSectionTitle(ctx, currentY, '美妆建议', leftMargin);

      const categories = [
        { key: 'foundation', label: '粉底液选择' },
        { key: 'concealer', label: '遮瑕膏选择' },
        { key: 'blush', label: '腮红选择' },
        { key: 'lip', label: '唇妆选择' },
        { key: 'eyeshadow', label: '眼影选择' }
      ];

      for (let category of categories) {
        if (skinAnalysis.makeupSuggestions[category.key]) {
          currentY = this.drawMakeupItem(ctx, width, currentY, category.label, skinAnalysis.makeupSuggestions[category.key], leftMargin);
        }
      }
    }

    // 计算实际模块高度并重新绘制背景
    const actualHeight = currentY - moduleStartY + 20;
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, moduleStartY, width, actualHeight);

    // 重新绘制标题
    ctx.fillStyle = '#2c3e50';
    ctx.font = 'bold 16px sans-serif';
    ctx.textAlign = 'left';
    ctx.fillText('肤色分析报告', 40, moduleStartY + 30);

    return moduleStartY + actualHeight;
  },

  /**
   * 绘制用户肤色卡片
   */
  drawUserColorCard: function(ctx, width, startY, userColor, labValues, rgbValues) {
    let currentY = startY;

    // 绘制卡片背景（去除右侧白边）
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, currentY, width, 100);

    // 绘制左侧色块
    const colorSize = 60;
    const colorX = 40;
    const colorY = currentY + 20;

    ctx.fillStyle = userColor;
    ctx.fillRect(colorX, colorY, colorSize, colorSize);

    // 绘制色块信息
    const textX = colorX + colorSize + 20;

    ctx.fillStyle = '#2c3e50';
    ctx.font = 'bold 16px sans-serif';
    ctx.textAlign = 'left';
    ctx.fillText('您的肤色', textX, colorY + 20);

    ctx.fillStyle = '#34495e';
    ctx.font = '12px sans-serif';
    ctx.fillText(userColor, textX, colorY + 40);

    ctx.fillStyle = '#7f8c8d';
    ctx.font = '11px sans-serif';
    ctx.fillText(`LAB: ${labValues}`, textX, colorY + 55);
    ctx.fillText(`RGB: ${rgbValues}`, textX, colorY + 70);

    return currentY + 120;
  },

  /**
   * 绘制扁平风格的色卡相似度排名
   */
  drawFlatColorCardRanking: function(ctx, width, startY, title, similarities, userColor) {
    let currentY = startY;

    // 绘制卡片背景（去除右侧白边，调整高度以适应更大的色块）
    const cardHeight = 50 + similarities.length * 40 + 20;
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, currentY, width, cardHeight);

    // 绘制标题
    ctx.fillStyle = '#2c3e50';
    ctx.font = 'bold 16px sans-serif';
    ctx.textAlign = 'left';
    ctx.fillText(title, 40, currentY + 30);
    currentY += 70; // 增大标题与内容之间的间距

    // 绘制排名列表（增加行高以适应32px色块）
    const itemHeight = 40;
    const leftMargin = 40;

    for (let i = 0; i < similarities.length; i++) {
      const item = similarities[i];
      const itemY = currentY + i * itemHeight;

      // 绘制排名标识（调整位置）
      const rankBadgeSize = 18;
      const rankX = leftMargin;
      const rankY = itemY - 9;

      // 前三名使用不同颜色
      let badgeColor = '#ecf0f1';
      let textColor = '#7f8c8d';
      if (i === 0) {
        badgeColor = '#f39c12';
        textColor = '#ffffff';
      } else if (i === 1) {
        badgeColor = '#95a5a6';
        textColor = '#ffffff';
      } else if (i === 2) {
        badgeColor = '#d35400';
        textColor = '#ffffff';
      }

      ctx.fillStyle = badgeColor;
      ctx.beginPath();
      ctx.arc(rankX + rankBadgeSize / 2, rankY + rankBadgeSize / 2, rankBadgeSize / 2, 0, 2 * Math.PI);
      ctx.fill();

      ctx.fillStyle = textColor;
      ctx.font = 'bold 10px sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText(`${i + 1}`, rankX + rankBadgeSize / 2, rankY + rankBadgeSize / 2 + 3);

      // 绘制色卡颜色块（与测试报告样式一致，增大尺寸）
      const colorSize = 32;
      const colorX = leftMargin + 30;
      const colorY = itemY - 16;

      // 绘制矩形色卡背景
      ctx.fillStyle = item.color;
      ctx.fillRect(colorX, colorY, colorSize, colorSize);

      // 绘制用户肤色圆形前景（与测试报告样式一致）
      const circleSize = 20;
      const circleX = colorX + (colorSize - circleSize) / 2;
      const circleY = colorY + (colorSize - circleSize) / 2;

      ctx.fillStyle = userColor;
      ctx.beginPath();
      ctx.arc(circleX + circleSize / 2, circleY + circleSize / 2, circleSize / 2, 0, 2 * Math.PI);
      ctx.fill();

      // 绘制颜色名称
      ctx.fillStyle = '#34495e';
      ctx.font = '12px sans-serif';
      ctx.textAlign = 'left';
      const nameX = colorX + colorSize + 18;

      // 截断过长的名称
      let displayName = item.name;
      if (displayName.length > 10) {
        displayName = displayName.substring(0, 10) + '...';
      }
      ctx.fillText(displayName, nameX, itemY + 4);

      // 绘制相似度
      const similarityX = width - 80;
      ctx.fillStyle = this.getFlatSimilarityColor(item.similarity);
      ctx.font = 'bold 11px sans-serif';
      ctx.textAlign = 'right';
      ctx.fillText(`${item.similarity}%`, similarityX, itemY + 4);

      // 绘制匹配度标签
      const levelX = width - 20;
      ctx.fillStyle = '#7f8c8d';
      ctx.font = '10px sans-serif';
      ctx.textAlign = 'right';
      ctx.fillText(this.getShortSimilarityLevel(item.similarity), levelX, itemY + 4);
    }

    return currentY + similarities.length * itemHeight + 20;
  },

  /**
   * 获取扁平风格的相似度颜色
   */
  getFlatSimilarityColor: function(similarity) {
    if (similarity >= 90) return '#27ae60'; // 绿色
    if (similarity >= 80) return '#2ecc71'; // 浅绿
    if (similarity >= 70) return '#f39c12'; // 橙色
    if (similarity >= 60) return '#e67e22'; // 深橙
    if (similarity >= 50) return '#e74c3c'; // 红色
    return '#95a5a6'; // 灰色
  },

  /**
   * 获取简短的相似度等级
   */
  getShortSimilarityLevel: function(similarity) {
    if (similarity >= 90) return '极佳';
    if (similarity >= 80) return '很好';
    if (similarity >= 70) return '较好';
    if (similarity >= 60) return '一般';
    if (similarity >= 50) return '较差';
    return '很差';
  },

  /**
   * 绘制扁平风格的肤色分析
   */
  drawFlatSkinAnalysis: function(ctx, width, startY, skinAnalysis) {
    let currentY = startY;

    // 计算卡片高度（动态计算以适应完整内容）
    const cardHeight = 300;

    // 绘制卡片背景（去除右侧白边）
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, currentY, width, cardHeight);

    // 绘制标题
    ctx.fillStyle = '#2c3e50';
    ctx.font = 'bold 16px sans-serif';
    ctx.textAlign = 'left';
    ctx.fillText('LAB肤色分析', 40, currentY + 30);
    currentY += 50;

    const leftMargin = 40;
    const lineHeight = 16;

    // 绘制L值分析
    ctx.fillStyle = '#34495e';
    ctx.font = 'bold 12px sans-serif';
    ctx.fillText(`L值 (亮度): ${skinAnalysis.lValue} - ${skinAnalysis.lCategory}`, leftMargin, currentY);
    currentY += lineHeight;

    // 绘制L值描述（完整内容）
    ctx.fillStyle = '#7f8c8d';
    ctx.font = '11px sans-serif';
    const lDescLines = this.wrapText(ctx, skinAnalysis.lDescription, width - 80, '11px sans-serif');
    for (let line of lDescLines) {
      ctx.fillText(line, leftMargin, currentY);
      currentY += 14;
    }
    currentY += 8;

    // 绘制a值分析
    ctx.fillStyle = '#34495e';
    ctx.font = 'bold 12px sans-serif';
    ctx.fillText(`a值 (红绿轴): ${skinAnalysis.aValue} - ${skinAnalysis.aCategory}`, leftMargin, currentY);
    currentY += lineHeight;

    // 绘制a值描述（完整内容）
    ctx.fillStyle = '#7f8c8d';
    ctx.font = '11px sans-serif';
    const aDescLines = this.wrapText(ctx, skinAnalysis.aDescription, width - 80, '11px sans-serif');
    for (let line of aDescLines) {
      ctx.fillText(line, leftMargin, currentY);
      currentY += 14;
    }
    currentY += 8;

    // 绘制b值分析
    ctx.fillStyle = '#34495e';
    ctx.font = 'bold 12px sans-serif';
    ctx.fillText(`b值 (黄蓝轴): ${skinAnalysis.bValue} - ${skinAnalysis.bCategory}`, leftMargin, currentY);
    currentY += lineHeight;

    // 绘制b值描述（完整内容）
    ctx.fillStyle = '#7f8c8d';
    ctx.font = '11px sans-serif';
    const bDescLines = this.wrapText(ctx, skinAnalysis.bDescription, width - 80, '11px sans-serif');
    for (let line of bDescLines) {
      ctx.fillText(line, leftMargin, currentY);
      currentY += 14;
    }

    return startY + cardHeight;
  },

  /**
   * 绘制扁平风格的护肤建议
   */
  drawFlatSkincareSuggestions: function(ctx, width, startY, suggestions) {
    let currentY = startY;

    // 计算卡片高度
    const cardHeight = 40 + suggestions.length * 25 + 20;

    // 绘制卡片背景（去除右侧白边）
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, currentY, width, cardHeight);

    // 绘制标题
    ctx.fillStyle = '#2c3e50';
    ctx.font = 'bold 16px sans-serif';
    ctx.textAlign = 'left';
    ctx.fillText('护肤建议', 40, currentY + 30);
    currentY += 50;

    const leftMargin = 40;

    // 绘制建议列表（与测试报告页面一致）
    for (let i = 0; i < suggestions.length; i++) {
      const suggestion = suggestions[i];

      // 绘制圆点（与测试报告样式一致）
      ctx.fillStyle = '#07c160';
      ctx.font = '12px sans-serif';
      ctx.textAlign = 'left';
      ctx.fillText('•', leftMargin, currentY);

      // 绘制建议内容（不截断，显示完整内容）
      ctx.fillStyle = '#666666';
      ctx.font = '11px sans-serif';

      const suggestionLines = this.wrapText(ctx, suggestion, width - 80, '11px sans-serif');
      for (let j = 0; j < suggestionLines.length; j++) {
        const line = suggestionLines[j];
        ctx.fillText(line, leftMargin + 15, currentY);
        currentY += 14;
      }
      currentY += 8;
    }

    return startY + cardHeight;
  },

  /**
   * 绘制扁平风格的美妆建议
   */
  drawFlatMakeupSuggestions: function(ctx, width, startY, makeupSuggestions) {
    let currentY = startY;

    // 计算卡片高度（动态计算）
    const categories = [
      { key: 'foundation', label: '粉底液选择' },
      { key: 'concealer', label: '遮瑕膏选择' },
      { key: 'lip', label: '唇釉选择' }
    ];

    let validCategories = categories.filter(cat => makeupSuggestions[cat.key]);
    const cardHeight = 40 + validCategories.length * 35 + 20;

    // 绘制卡片背景（去除右侧白边）
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, currentY, width, cardHeight);

    // 绘制标题
    ctx.fillStyle = '#2c3e50';
    ctx.font = 'bold 16px sans-serif';
    ctx.textAlign = 'left';
    ctx.fillText('美妆建议', 40, currentY + 30);
    currentY += 50;

    const leftMargin = 40;

    // 绘制美妆建议（与测试报告页面一致）
    for (let category of validCategories) {
      if (makeupSuggestions[category.key]) {
        // 绘制分类标题
        ctx.fillStyle = '#333333';
        ctx.font = 'bold 12px sans-serif';
        ctx.textAlign = 'left';
        ctx.fillText(category.label, leftMargin, currentY);
        currentY += 16;

        // 绘制建议内容（完整内容，不截断）
        ctx.fillStyle = '#666666';
        ctx.font = '11px sans-serif';
        const suggestionLines = this.wrapText(ctx, makeupSuggestions[category.key], width - 80, '11px sans-serif');
        for (let j = 0; j < suggestionLines.length; j++) {
          const line = suggestionLines[j];
          ctx.fillText(line, leftMargin, currentY);
          currentY += 14;
        }
        currentY += 8;
      }
    }

    return startY + cardHeight;
  },



  /**
   * 绘制模块内的用户肤色
   */
  drawUserColorInModule: function(ctx, width, currentY, userColor, labValues, rgbValues, leftMargin) {
    // 绘制绿点
    ctx.fillStyle = '#07c160';
    ctx.font = '12px sans-serif';
    ctx.textAlign = 'left';
    ctx.fillText('•', leftMargin, currentY + 15);

    // 绘制肤色色块
    const colorSize = 35;
    const colorX = leftMargin + 20;
    const colorY = currentY;

    ctx.fillStyle = userColor;
    ctx.fillRect(colorX, colorY, colorSize, colorSize);

    // 绘制肤色信息
    const textX = colorX + colorSize + 15;

    ctx.fillStyle = '#34495e';
    ctx.font = 'bold 12px sans-serif';
    ctx.textAlign = 'left';
    ctx.fillText('您的肤色', textX, currentY + 12);

    ctx.fillStyle = '#666666';
    ctx.font = '10px sans-serif';
    ctx.fillText(`${userColor} | LAB: ${labValues} | RGB: ${rgbValues}`, textX, currentY + 28);

    return currentY + 45;
  },

  /**
   * 绘制LAB分析项
   */
  drawLabAnalysisItem: function(ctx, width, currentY, label, value, category, description, leftMargin) {
    // 绘制绿点
    ctx.fillStyle = '#07c160';
    ctx.font = '12px sans-serif';
    ctx.textAlign = 'left';
    ctx.fillText('•', leftMargin, currentY + 12);

    const contentX = leftMargin + 20;

    // 绘制标题
    ctx.fillStyle = '#34495e';
    ctx.font = 'bold 11px sans-serif';
    ctx.fillText(`${label}: ${value} - ${category}`, contentX, currentY + 12);

    // 绘制描述（限制长度）
    ctx.fillStyle = '#666666';
    ctx.font = '10px sans-serif';
    const shortDesc = description.length > 60 ? description.substring(0, 60) + '...' : description;
    const descLines = this.wrapText(ctx, shortDesc, width - 80, '10px sans-serif');
    let textY = currentY + 26;
    for (let line of descLines) {
      ctx.fillText(line, contentX, textY);
      textY += 12;
    }

    return textY + 8;
  },

  /**
   * 绘制分节标题
   */
  drawSectionTitle: function(ctx, currentY, title, leftMargin) {
    ctx.fillStyle = '#34495e';
    ctx.font = 'bold 13px sans-serif';
    ctx.textAlign = 'left';
    ctx.fillText(title, leftMargin, currentY + 15);
    return currentY + 25;
  },

  /**
   * 绘制建议项
   */
  drawSuggestionItem: function(ctx, width, currentY, suggestion, leftMargin) {
    // 绘制绿点
    ctx.fillStyle = '#07c160';
    ctx.font = '12px sans-serif';
    ctx.textAlign = 'left';
    ctx.fillText('•', leftMargin, currentY + 12);

    // 绘制建议内容（限制长度）
    ctx.fillStyle = '#666666';
    ctx.font = '10px sans-serif';
    const shortSuggestion = suggestion.length > 50 ? suggestion.substring(0, 50) + '...' : suggestion;
    const suggestionLines = this.wrapText(ctx, shortSuggestion, width - 80, '10px sans-serif');
    let textY = currentY + 12;
    for (let line of suggestionLines) {
      ctx.fillText(line, leftMargin + 20, textY);
      textY += 12;
    }

    return textY + 6;
  },

  /**
   * 绘制美妆项
   */
  drawMakeupItem: function(ctx, width, currentY, label, content, leftMargin) {
    // 绘制绿点
    ctx.fillStyle = '#07c160';
    ctx.font = '12px sans-serif';
    ctx.textAlign = 'left';
    ctx.fillText('•', leftMargin, currentY + 12);

    const contentX = leftMargin + 20;

    // 绘制分类标题
    ctx.fillStyle = '#34495e';
    ctx.font = 'bold 10px sans-serif';
    ctx.fillText(label, contentX, currentY + 12);

    // 绘制内容（限制长度）
    ctx.fillStyle = '#666666';
    ctx.font = '10px sans-serif';
    const shortContent = content.length > 45 ? content.substring(0, 45) + '...' : content;
    const contentLines = this.wrapText(ctx, shortContent, width - 80, '10px sans-serif');
    let textY = currentY + 24;
    for (let line of contentLines) {
      ctx.fillText(line, contentX, textY);
      textY += 12;
    }

    return textY + 6;
  },



  /**
   * 绘制小程序二维码
   */
  drawQRCode: function(ctx, width, startY) {
    const qrSize = 60;
    const qrX = (width - qrSize) / 2;
    const qrY = startY;

    // 先绘制白色背景
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(qrX - 5, qrY - 5, qrSize + 10, qrSize + 10);

    // 绘制二维码边框
    ctx.strokeStyle = '#e0e0e0';
    ctx.lineWidth = 1;
    ctx.strokeRect(qrX, qrY, qrSize, qrSize);

    // 尝试绘制二维码图片
    try {
      ctx.drawImage('/images/QR/QR.jpg', qrX, qrY, qrSize, qrSize);
    } catch (error) {
      // 如果图片加载失败，绘制占位符
      ctx.fillStyle = '#f5f5f5';
      ctx.fillRect(qrX, qrY, qrSize, qrSize);

      ctx.fillStyle = '#999999';
      ctx.font = '10px sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText('小程序码', qrX + qrSize / 2, qrY + qrSize / 2 + 3);
    }
  },

  /**
   * 文本换行处理
   */
  wrapText: function(ctx, text, maxWidth, font) {
    ctx.font = font;
    const words = text.split('');
    const lines = [];
    let currentLine = '';

    for (let i = 0; i < words.length; i++) {
      const testLine = currentLine + words[i];
      const metrics = ctx.measureText(testLine);
      const testWidth = metrics.width;

      if (testWidth > maxWidth && currentLine !== '') {
        lines.push(currentLine);
        currentLine = words[i];
      } else {
        currentLine = testLine;
      }
    }
    lines.push(currentLine);

    return lines;
  },



  /**
   * 将Canvas 2D转换为图片并保存
   */
  canvasToImage2D: function(canvas, dynamicHeight) {
    console.log('开始转换Canvas 2D为图片，动态高度:', dynamicHeight);

    // 使用传入的动态高度，如果没有则使用默认值
    const actualHeight = dynamicHeight || 3800;

    wx.canvasToTempFilePath({
      canvas: canvas,
      width: 750,
      height: actualHeight,
      destWidth: 750,
      destHeight: actualHeight,
      fileType: 'png', // 使用PNG格式保证质量
      quality: 1,
      success: (res) => {
        console.log('Canvas 2D转换成功:', res.tempFilePath);
        wx.hideLoading();

        // 显示操作选项
        wx.showActionSheet({
          itemList: ['保存到相册', '查看图片'],
          success: (actionRes) => {
            if (actionRes.tapIndex === 0) {
              // 保存到相册
              this.saveImageToAlbum(res.tempFilePath);
            } else if (actionRes.tapIndex === 1) {
              // 查看图片
              this.previewImage(res.tempFilePath);
            }
          }
        });
      },
      fail: (err) => {
        console.error('Canvas 2D转换失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '生成图片失败: ' + (err.errMsg || '未知错误'),
          icon: 'none',
          duration: 3000
        });
      }
    }, this);
  },

  /**
   * 将Canvas转换为图片并保存（旧版API兼容）
   */
  canvasToImage: function(dynamicHeight) {
    console.log('开始转换Canvas为图片，动态高度:', dynamicHeight);

    // 使用传入的动态高度，如果没有则使用默认值
    const actualHeight = dynamicHeight || 3800;

    wx.canvasToTempFilePath({
      canvasId: 'shareCanvas',
      width: 750,
      height: actualHeight,
      destWidth: 750,
      destHeight: actualHeight,
      fileType: 'png', // 使用PNG格式保证质量
      quality: 1,
      success: (res) => {
        console.log('Canvas转换成功:', res.tempFilePath);
        wx.hideLoading();

        // 显示操作选项
        wx.showActionSheet({
          itemList: ['保存到相册', '查看图片'],
          success: (actionRes) => {
            if (actionRes.tapIndex === 0) {
              // 保存到相册
              this.saveImageToAlbum(res.tempFilePath);
            } else if (actionRes.tapIndex === 1) {
              // 查看图片
              this.previewImage(res.tempFilePath);
            }
          }
        });
      },
      fail: (err) => {
        console.error('Canvas转换失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '生成图片失败: ' + (err.errMsg || '未知错误'),
          icon: 'none',
          duration: 3000
        });
      }
    }, this);
  },

  /**
   * 保存图片到相册
   */
  saveImageToAlbum: function(imagePath) {
    wx.saveImageToPhotosAlbum({
      filePath: imagePath,
      success: () => {
        wx.showToast({
          title: '已保存到相册',
          icon: 'success',
          duration: 2000
        });
      },
      fail: (err) => {
        if (err.errMsg.includes('auth deny')) {
          wx.showModal({
            title: '提示',
            content: '需要您授权保存图片到相册',
            showCancel: false,
            confirmText: '去设置',
            success: () => {
              wx.openSetting();
            }
          });
        } else {
          wx.showToast({
            title: '保存失败',
            icon: 'none',
            duration: 2000
          });
        }
      }
    });
  },

  /**
   * 预览图片
   */
  previewImage: function(imagePath) {
    wx.previewImage({
      urls: [imagePath],
      current: imagePath
    });
  },

  // 分享报告
  shareReport: function() {
    const { userColor, closestMatchOne, closestMatchTwo } = this.data;

    if (!closestMatchOne || !closestMatchTwo) {
      wx.showToast({
        title: '报告未生成',
        icon: 'none',
        duration: 1500
      });
      return;
    }

    // 构建分享内容
    const shareContent = `我的肤色测试结果：\n选择颜色：${userColor}\n\n肤色色卡一最匹配：${closestMatchOne.name}\n相似度：${closestMatchOne.similarity}%\n\n肤色色卡二最匹配：${closestMatchTwo.name}\n相似度：${closestMatchTwo.similarity}%`;

    wx.setClipboardData({
      data: shareContent,
      success: () => {
        wx.showToast({
          title: '报告内容已复制',
          icon: 'success',
          duration: 1500
        });
      },
      fail: () => {
        wx.showToast({
          title: '分享失败',
          icon: 'none',
          duration: 1500
        });
      }
    });
  },

  // 查看颜色详情
  viewColorDetail: function(e) {
    const color = e.currentTarget.dataset.color;
    const name = e.currentTarget.dataset.name;

    wx.showModal({
      title: name,
      content: `颜色代码：${color}\n点击确定复制颜色代码`,
      confirmText: '复制',
      cancelText: '关闭',
      success: (res) => {
        if (res.confirm) {
          this.copyColorCode(e);
        }
      }
    });
  },

  // 获取相似度等级描述
  getSimilarityLevel: function(similarity) {
    if (similarity >= 90) return '极高相似';
    if (similarity >= 80) return '高度相似';
    if (similarity >= 70) return '较为相似';
    if (similarity >= 60) return '中等相似';
    if (similarity >= 50) return '略有相似';
    return '相似度较低';
  },

  // 获取相似度等级颜色
  getSimilarityColor: function(similarity) {
    if (similarity >= 90) return '#4CAF50'; // 绿色
    if (similarity >= 80) return '#8BC34A'; // 浅绿
    if (similarity >= 70) return '#CDDC39'; // 黄绿
    if (similarity >= 60) return '#FFC107'; // 黄色
    if (similarity >= 50) return '#FF9800'; // 橙色
    return '#F44336'; // 红色
  },

  // 切换肤色色卡一相似度列表展开状态
  toggleExpandOne: function() {
    this.setData({
      expandedOne: !this.data.expandedOne
    });
  },

  // 切换肤色色卡二相似度列表展开状态
  toggleExpandTwo: function() {
    this.setData({
      expandedTwo: !this.data.expandedTwo
    });
  },

  // 生成肤色分析（针对亚洲人优化）
  generateSkinAnalysis: function(lab) {
    const { l, a, b } = lab;

    // L值（亮度）分析 - 针对亚洲人肤色范围优化
    let lCategory, lDescription, lSkincareSuggestions = [];
    if (l >= 80) {
      lCategory = "极白肤色（L值：80-100）";
      lDescription = "肤色极为白皙，在亚洲人群中属于最白的1-2%，皮肤薄透，血管清晰可见，容易晒红晒伤。";
      lSkincareSuggestions = [
        "选择SPF50+的物理防晒霜，含氧化锌、二氧化钛成分",
        "使用温和修复产品，如含有积雪草苷、神经酰胺的精华",
        "避免含酒精、香精的刺激性产品，选择敏感肌专用护肤品"
      ];
    } else if (l >= 68) {
      lCategory = "白皙肤色（L值：68-79）";
      lDescription = "典型的亚洲白皮，肤色明亮有光泽，约占亚洲人群的15-20%，容易显现红血丝和色斑。";
      lSkincareSuggestions = [
        "使用温和美白产品，如含有熊果苷、4-MSK的精华液",
        "选择含有烟酰胺的产品改善毛孔和提亮肤色",
        "注重抗氧化护理，使用含维生素C、E的产品"
      ];
    } else if (l >= 58) {
      lCategory = "标准肤色（L值：58-67）";
      lDescription = "亚洲人最常见的肤色范围，约占人群的40-50%，肤色自然健康，适应性强。";
      lSkincareSuggestions = [
        "根据肤质选择合适的清洁产品，混合性肌肤可分区护理",
        "使用含有透明质酸、胶原蛋白的保湿产品",
        "定期使用温和去角质产品，如含有乳酸、PHA的产品"
      ];
    } else if (l >= 48) {
      lCategory = "偏深肤色（L值：48-57）";
      lDescription = "肤色偏深，在亚洲人群中约占25-30%，多见于南方地区，肤色稳定不易过敏。";
      lSkincareSuggestions = [
        "使用含有传明酸、曲酸的美白产品改善肤色不均",
        "选择质地丰润的保湿产品，如含有角鲨烷、乳木果油的面霜",
        "注重防晒，使用SPF30以上的防晒产品防止色素沉着"
      ];
    } else {
      lCategory = "深色肤色（L值：30-47）";
      lDescription = "肤色较深，在亚洲人群中相对少见，多为长期日晒或遗传因素，肌肤较为厚实。";
      lSkincareSuggestions = [
        "使用含有果酸、水杨酸的产品促进角质更新",
        "选择高浓度美白精华，如含有氢醌、曲酸的产品",
        "加强深层保湿，使用含有神经酰胺、透明质酸的厚重面霜"
      ];
    }

    // a值（红绿轴）分析 - 针对亚洲人色调特点优化
    let aCategory, aDescription, aSkincareSuggestions = [];
    if (a >= 15) {
      aCategory = "明显红调（a值：15以上）";
      aDescription = "肤色明显偏红，可能是敏感肌、酒糟鼻或长期炎症导致，在亚洲人中多见于敏感体质。";
      aSkincareSuggestions = [
        "使用舒缓抗炎产品，如含有洋甘菊、马齿苋提取物的精华",
        "选择无香精、无酒精的温和护肤品",
        "避免过度清洁和摩擦，建议就医检查是否有皮肤疾病"
      ];
    } else if (a >= 6) {
      aCategory = "轻微红调（a值：6-14）";
      aDescription = "肤色带有健康的红润感，是亚洲人理想的肤色状态之一，显得气色好有活力。";
      aSkincareSuggestions = [
        "维持现有护肤习惯，使用温和的日常护肤品",
        "注意防晒避免红调加深，选择含有抗氧化成分的产品",
        "可适当使用含有烟酰胺的产品平衡肤色"
      ];
    } else if (a >= -2) {
      aCategory = "中性色调（a值：-2-5）";
      aDescription = "肤色非常平衡，既不偏红也不偏绿，是亚洲人最理想的肤色状态，约占人群30%。";
      aSkincareSuggestions = [
        "保持均衡的护肤方案，根据季节调整产品质地",
        "夏季注重控油和防晒，冬季加强保湿和修复",
        "可尝试功效性产品，如美白、抗老等"
      ];
    } else if (a >= -8) {
      aCategory = "轻微绿调（a值：-8--3）";
      aDescription = "肤色略带绿调，在亚洲人中较为常见，可能显得肤色偏冷或略显苍白。";
      aSkincareSuggestions = [
        "使用提亮肤色的产品，如含有维生素C、烟酰胺的精华",
        "选择带有轻微暖调的护肤品，避免过于清爽的产品",
        "注重血液循环，可使用含有咖啡因的眼霜和面霜"
      ];
    } else {
      aCategory = "明显绿调（a值：-8以下）";
      aDescription = "肤色明显偏绿，可能显得苍白无血色，在亚洲人中相对少见，多见于贫血或循环不良。";
      aSkincareSuggestions = [
        "使用促进血液循环的产品，如含有人参、红景天提取物的精华",
        "选择滋养型护肤品，加强肌肤营养补充",
        "建议检查身体健康状况，注意补血养气"
      ];
    }

    // b值（黄蓝轴）分析 - 针对亚洲人黄调特点优化
    let bCategory, bDescription, bSkincareSuggestions = [];
    if (b >= 25) {
      bCategory = "重度黄调（b值：25以上）";
      bDescription = "肤色明显偏黄，可能显得暗沉蜡黄，在亚洲人中多见于肝功能异常或长期熬夜人群。";
      bSkincareSuggestions = [
        "使用强效美白产品，如含有曲酸、熊果苷的高浓度精华",
        "加强抗氧化护理，选择含有维生素C、谷胱甘肽的产品",
        "建议调整作息，检查肝功能，从内调理改善肤色"
      ];
    } else if (b >= 15) {
      bCategory = "中度黄调（b值：15-24）";
      bDescription = "典型的亚洲暖黄皮，约占亚洲人群60-70%，肤色温暖自然，但可能显得不够透亮。";
      bSkincareSuggestions = [
        "使用温和美白产品，如含有传明酸、4-MSK的精华液",
        "选择提亮肤色的产品，含有烟酰胺、维生素C成分",
        "注重去角质护理，使用含有果酸、酵素的产品"
      ];
    } else if (b >= 8) {
      bCategory = "轻度黄调（b值：8-14）";
      bDescription = "肤色带有自然的暖调，是亚洲人非常理想的肤色状态，显得健康有光泽。";
      bSkincareSuggestions = [
        "维持现有肤色状态，使用日常保湿和防晒产品",
        "可适当使用提亮产品增加肌肤光泽感",
        "选择适合暖调肌肤的彩妆产品"
      ];
    } else if (b >= 0) {
      bCategory = "中性色调（b值：0-7）";
      bDescription = "肤色非常平衡，既不偏黄也不偏蓝，在亚洲人中相对少见，约占人群10-15%。";
      bSkincareSuggestions = [
        "保持均衡护肤，根据个人肤质选择合适产品",
        "可尝试各种功效性护肤品，肌肤适应性较强",
        "注重基础护理，做好清洁、保湿、防晒"
      ];
    } else {
      bCategory = "冷调肤色（b值：0以下）";
      bDescription = "肤色偏冷调，带有蓝紫底色，在亚洲人中较为少见，多见于混血或特殊体质人群。";
      bSkincareSuggestions = [
        "选择适合冷调肌肤的护肤品，避免过于温暖的产品",
        "使用温和保湿产品，如含有透明质酸、胶原蛋白的精华",
        "注意防晒和抗氧化，选择适合冷调肌肤的彩妆"
      ];
    }

    // 综合护肤建议（智能筛选最重要的3条）
    const allSuggestions = [...lSkincareSuggestions, ...aSkincareSuggestions, ...bSkincareSuggestions];

    // 先去重，避免相同建议
    const uniqueSuggestions = [...new Set(allSuggestions)];

    // 按重要性排序：防晒>美白>保湿>修复>其他
    const priorityKeywords = ['防晒', '美白', '保湿', '修复', '温和', '抗氧化'];
    const prioritizedSuggestions = uniqueSuggestions.sort((a, b) => {
      const aScore = priorityKeywords.findIndex(keyword => a.includes(keyword));
      const bScore = priorityKeywords.findIndex(keyword => b.includes(keyword));
      return (aScore === -1 ? 999 : aScore) - (bScore === -1 ? 999 : bScore);
    });

    // 确保有足够的建议，如果去重后不足3条，则补充其他建议
    let skincareSuggestions = prioritizedSuggestions.slice(0, 3);

    // 如果建议不足3条，添加通用建议
    if (skincareSuggestions.length < 3) {
      const generalSuggestions = [
        "保持良好的清洁、保湿和防晒习惯",
        "根据季节变化调整护肤方案",
        "选择适合自己肤质的护肤产品"
      ];

      for (let suggestion of generalSuggestions) {
        if (skincareSuggestions.length >= 3) break;
        if (!skincareSuggestions.includes(suggestion)) {
          skincareSuggestions.push(suggestion);
        }
      }
    }

    // 生成美妆建议
    const makeupSuggestions = this.generateMakeupSuggestions(l, a, b);

    return {
      lValue: l.toFixed(1),
      aValue: a.toFixed(1),
      bValue: b.toFixed(1),
      lCategory: lCategory,
      lDescription: lDescription,
      aCategory: aCategory,
      aDescription: aDescription,
      bCategory: bCategory,
      bDescription: bDescription,
      skincareSuggestions: skincareSuggestions,
      makeupSuggestions: makeupSuggestions
    };
  },

  /**
   * 生成分享ID
   */
  generateShareId: function() {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9);
    return `share_${timestamp}_${random}`;
  },

  /**
   * 保存分享数据到本地存储
   */
  saveShareData: function(shareId, shareData) {
    try {
      // 设置过期时间（7天）
      const expireTime = Date.now() + 7 * 24 * 60 * 60 * 1000;
      const dataWithExpire = {
        data: shareData,
        expireTime: expireTime
      };

      wx.setStorageSync(`shareData_${shareId}`, dataWithExpire);
      return true;
    } catch (error) {
      console.error('保存分享数据失败', error);
      return false;
    }
  },

  /**
   * 获取分享数据
   */
  getShareData: function(shareId) {
    try {
      const storedData = wx.getStorageSync(`shareData_${shareId}`);
      if (!storedData) {
        return null;
      }

      // 检查是否过期
      if (Date.now() > storedData.expireTime) {
        // 数据已过期，删除
        wx.removeStorageSync(`shareData_${shareId}`);
        return null;
      }

      return storedData.data;
    } catch (error) {
      console.error('获取分享数据失败', error);
      return null;
    }
  },

  // 小程序分享功能
  onShareAppMessage: function(res) {
    const { userColor, closestMatchOne } = this.data;

    // 构建分享标题 - 突出个人测试结果
    let shareTitle = '【KALA配色】我刚完成了肤色测试';
    if (userColor && closestMatchOne) {
      shareTitle = `我的肤色是${closestMatchOne.name}，你也来测测看！`;
    } else if (userColor) {
      shareTitle = `我测出的肤色是${userColor}，你也来试试！`;
    }

    // 使用正确的子包路径分享到肤色测试页面
    const sharePath = '/clothing-package/pages/skinToneTest/skinToneTest';

    console.log('分享KALA配色小程序:', { title: shareTitle, path: sharePath });

    return {
      title: shareTitle,
      path: sharePath,
      imageUrl: '', // 可以设置自定义分享图片
    };
  },

  // 生成美妆建议（针对亚洲人优化）
  generateMakeupSuggestions: function(l, a, b) {
    let foundationSuggestion, concealerSuggestion, lipSuggestion;

    // 粉底液建议（基于L值和色调）
    if (l >= 80) {
      // 极白肤色
      if (a >= 6) {
        foundationSuggestion = "选择粉调或桃调的粉底液，如象牙白、瓷白色号，避免黄调过重的产品";
      } else if (b >= 8) {
        foundationSuggestion = "选择暖调象牙白或米白色粉底液，带有轻微黄调平衡肤色";
      } else {
        foundationSuggestion = "选择冷调粉底液，如粉白、象牙白色号，避免暖调产品";
      }
    } else if (l >= 68) {
      // 白皙肤色
      if (a >= 6 && b >= 15) {
        foundationSuggestion = "选择暖调粉底液，如自然白、暖白色号，平衡红黄调";
      } else if (a >= 6) {
        foundationSuggestion = "选择中性偏粉调的粉底液，如自然白、粉白色号";
      } else if (b >= 15) {
        foundationSuggestion = "选择暖调粉底液，如暖白、米白色号，与肤色黄调匹配";
      } else {
        foundationSuggestion = "选择中性色调粉底液，如自然白、标准白色号";
      }
    } else if (l >= 58) {
      // 标准肤色
      if (b >= 15) {
        foundationSuggestion = "选择暖调粉底液，如自然色、暖米色号，与亚洲人常见黄调匹配";
      } else {
        foundationSuggestion = "选择中性色调粉底液，如自然色、标准色号";
      }
    } else if (l >= 48) {
      // 偏深肤色
      foundationSuggestion = "选择暖调深色粉底液，如小麦色、蜜糖色号，注意与颈部肤色匹配";
    } else {
      // 深色肤色
      foundationSuggestion = "选择深色暖调粉底液，如古铜色、深蜜糖色号，建议试色确认";
    }

    // 遮瑕膏建议
    if (l >= 68) {
      if (a >= 6) {
        concealerSuggestion = "选择绿色调遮瑕膏中和红血丝，再用肤色遮瑕膏提亮";
      } else {
        concealerSuggestion = "选择比粉底浅1-2个色号的遮瑕膏，用于提亮和遮盖瑕疵";
      }
    } else {
      concealerSuggestion = "选择与肤色相近的遮瑕膏，重点遮盖色斑和暗沉区域";
    }



    // 唇釉/口红建议
    if (l >= 68) {
      if (a >= 6) {
        lipSuggestion = "适合粉色系唇釉，如樱花粉、玫瑰粉，避免橘调过重的颜色";
      } else if (b >= 15) {
        lipSuggestion = "适合暖调唇釉，如珊瑚色、蜜桃色，与肤色黄调呼应";
      } else {
        lipSuggestion = "适合冷调唇釉，如莓果色、紫调红色，提升整体气质";
      }
    } else if (l >= 58) {
      if (b >= 15) {
        lipSuggestion = "适合暖调唇釉，如橘红色、砖红色，显得温暖有活力";
      } else {
        lipSuggestion = "适合经典红色系唇釉，如正红色、樱桃红";
      }
    } else {
      lipSuggestion = "适合饱和度高的唇釉，如深红色、酒红色，增强气场";
    }



    return {
      foundation: foundationSuggestion,
      concealer: concealerSuggestion,
      lip: lipSuggestion
    };
  }
});
