/**
 * Canvas工具函数
 */
const { safeGetPixelRatio } = require('./systemInfoUtils.js');

/**
 * 验证Canvas上下文是否有效
 * @param {CanvasRenderingContext2D} ctx - Canvas上下文
 * @param {string} contextName - 上下文名称（用于错误日志）
 * @returns {boolean} 是否有效
 */
function validateCanvasContext(ctx, contextName = 'Canvas') {
  if (!ctx) {
    console.error(`${contextName}上下文为null或undefined`);
    return false;
  }
  
  if (typeof ctx.clearRect !== 'function') {
    console.error(`${contextName}上下文缺少必要的方法`);
    return false;
  }
  
  return true;
}

/**
 * 安全地清空Canvas
 * @param {CanvasRenderingContext2D} ctx - Canvas上下文
 * @param {number} width - Canvas宽度
 * @param {number} height - Canvas高度
 * @param {string} contextName - 上下文名称（用于错误日志）
 * @returns {boolean} 是否成功清空
 */
function safeClearCanvas(ctx, width, height, contextName = 'Canvas') {
  if (!validateCanvasContext(ctx, contextName)) {
    return false;
  }
  
  try {
    ctx.clearRect(0, 0, width, height);
    return true;
  } catch (error) {
    console.error(`${contextName}清空失败:`, error);
    return false;
  }
}

/**
 * 安全地获取Canvas上下文
 * @param {HTMLCanvasElement} canvas - Canvas元素
 * @param {string} contextType - 上下文类型，默认'2d'
 * @param {string} contextName - 上下文名称（用于错误日志）
 * @returns {CanvasRenderingContext2D|null} Canvas上下文或null
 */
function safeGetCanvasContext(canvas, contextType = '2d', contextName = 'Canvas') {
  if (!canvas) {
    console.error(`${contextName}节点为null或undefined`);
    return null;
  }
  
  try {
    const ctx = canvas.getContext(contextType);
    if (!validateCanvasContext(ctx, contextName)) {
      return null;
    }
    return ctx;
  } catch (error) {
    console.error(`${contextName}上下文获取失败:`, error);
    return null;
  }
}

/**
 * 安全地获取设备像素比
 * @returns {number} 设备像素比
 */
function getDevicePixelRatio() {
  return safeGetPixelRatio();
}

/**
 * 安全地设置Canvas尺寸和缩放
 * @param {HTMLCanvasElement} canvas - Canvas元素
 * @param {CanvasRenderingContext2D} ctx - Canvas上下文
 * @param {number} width - 逻辑宽度
 * @param {number} height - 逻辑高度
 * @param {number} dpr - 设备像素比，可选
 * @param {string} contextName - 上下文名称（用于错误日志）
 * @returns {boolean} 是否成功设置
 */
function safeSetCanvasSize(canvas, ctx, width, height, dpr = null, contextName = 'Canvas') {
  if (!canvas || !validateCanvasContext(ctx, contextName)) {
    return false;
  }
  
  try {
    const devicePixelRatio = dpr || getDevicePixelRatio();
    
    canvas.width = width * devicePixelRatio;
    canvas.height = height * devicePixelRatio;
    
    ctx.scale(devicePixelRatio, devicePixelRatio);
    
    return true;
  } catch (error) {
    console.error(`${contextName}尺寸设置失败:`, error);
    return false;
  }
}

/**
 * 安全地重置Canvas变换矩阵
 * @param {CanvasRenderingContext2D} ctx - Canvas上下文
 * @param {string} contextName - 上下文名称（用于错误日志）
 * @returns {boolean} 是否成功重置
 */
function safeResetTransform(ctx, contextName = 'Canvas') {
  if (!validateCanvasContext(ctx, contextName)) {
    return false;
  }
  
  try {
    if (typeof ctx.setTransform === 'function') {
      ctx.setTransform(1, 0, 0, 1, 0, 0);
      return true;
    } else {
      console.warn(`${contextName}不支持setTransform方法`);
      return false;
    }
  } catch (error) {
    console.error(`${contextName}变换矩阵重置失败:`, error);
    return false;
  }
}

module.exports = {
  validateCanvasContext,
  safeClearCanvas,
  safeGetCanvasContext,
  getDevicePixelRatio,
  safeSetCanvasSize,
  safeResetTransform
};
