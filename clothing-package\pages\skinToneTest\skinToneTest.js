// pages/skinToneTest/skinToneTest.js
const colorUtils = require('../../utils/colorUtils');
const adFreeUtils = require('../../utils/adFreeUtils.js');
const { storageCache } = require('../../utils/storageCache');
const logUtils = require('../../../utils/logUtils');

// 肤色色卡一数据
const SKIN_TONE_COLORS_ONE = [
  { name: '偏红冷一白', color: '#F5EBE2' },
  { name: '偏红冷二白', color: '#ECD9C9' },
  { name: '偏红暖一白', color: '#F4D6BC' },
  { name: '偏红暖二白', color: '#DBAF92' },
  { name: '中性冷一白', color: '#F1E0D6' },
  { name: '中性冷二白', color: '#E8BBA8' },
  { name: '中性暖一白', color: '#F2E1C3' },
  { name: '中性暖二白', color: '#EAC7A7' },
  { name: '偏绿冷一白', color: '#F2E5D5' },
  { name: '偏绿冷二白', color: '#E9C9B0' },
  { name: '偏绿暖一白', color: '#EAD9BB' },
  { name: '偏绿暖二白', color: '#DECAAF' }
];

// 肤色色卡二数据
const SKIN_TONE_COLORS_TWO = [
  { name: '粉一白', color: '#FAE7EA' },
  { name: '黄一白', color: '#FCE6CE' },
  { name: '中一白', color: '#FCDFCD' },
  { name: '橄榄一白', color: '#FEF6E1' },
  { name: '粉二白', color: '#F4D1CB' },
  { name: '黄二白', color: '#FBD8B2' },
  { name: '中二白', color: '#F2C7B4' },
  { name: '橄榄二白', color: '#F4E6C9' },
  { name: '粉三白', color: '#E0B1A1' },
  { name: '黄三白', color: '#F8C58C' },
  { name: '中三白', color: '#E8B59A' },
  { name: '橄榄三白', color: '#DEC99E' }
];

Page({
  data: {
    selectedColor: '#EACEB8', // 当前选择器中的颜色
    selectedColors: [], // 选择的多个肤色
    colorTextColors: [], // 每个颜色对应的文字颜色
    averageColor: '', // 平均颜色
    averageTextColor: '#000000', // 平均颜色的文字颜色
    showColorPicker: false, // 是否显示颜色选择器
    editingIndex: -1, // 正在编辑的颜色索引，-1表示添加新颜色
    skinToneColorsOne: SKIN_TONE_COLORS_ONE, // 肤色色卡一数据
    skinToneColorsTwo: SKIN_TONE_COLORS_TWO, // 肤色色卡二数据
    // 激励广告相关
    rewardedVideoAd: null,
    adInitialized: false,
    dailyClickCount: 0, // 当日点击次数
    lastClickDate: '', // 上次点击日期
    // 色卡编辑相关
    showCardEditModal: false, // 是否显示色卡编辑弹窗
    editingCardType: '', // 正在编辑的色卡类型 ('one' 或 'two')
    editingCardIndex: -1, // 正在编辑的色卡项索引
    editingCardColor: '', // 正在编辑的颜色
    editingCardName: '' // 正在编辑的名称
  },

  onLoad: function (options) {
    try {
      // 设置页面标题
      wx.setNavigationBarTitle({
        title: '肤色测试'
      });

      // 异步初始化色卡数据和每日点击计数，避免阻塞首屏渲染
      wx.nextTick(() => {
        this.initSkinToneCardsAsync();
        this.initDailyClickCountAsync();
      });

      // 延迟初始化激励广告，避免阻塞页面加载
      wx.nextTick(() => {
        setTimeout(() => {
          this.initRewardedVideoAd();
        }, 100);
      });

      // 初始化颜色数据
      this.updateColorData();
    } catch (error) {
      console.error('页面初始化失败:', error);
    }
  },

  // 更新颜色相关数据
  updateColorData: function() {
    const selectedColors = this.data.selectedColors;

    // 如果没有选择颜色，清空相关数据
    if (selectedColors.length === 0) {
      this.setData({
        colorTextColors: [],
        averageColor: '',
        averageTextColor: '#000000'
      });
      return;
    }

    // 计算每个颜色的文字颜色
    const colorTextColors = selectedColors.map(color =>
      colorUtils.getTextColorForBackground(color)
    );

    // 计算平均颜色
    const averageColor = this.calculateAverageColor(selectedColors);
    const averageTextColor = colorUtils.getTextColorForBackground(averageColor);

    this.setData({
      colorTextColors: colorTextColors,
      averageColor: averageColor,
      averageTextColor: averageTextColor
    });
  },

  // 计算平均颜色
  calculateAverageColor: function(colors) {
    if (colors.length === 0) {
      return '';
    }

    if (colors.length === 1) {
      return colors[0];
    }

    let totalR = 0, totalG = 0, totalB = 0;

    colors.forEach(color => {
      const rgb = colorUtils.hexToRgb(color);
      totalR += rgb.r;
      totalG += rgb.g;
      totalB += rgb.b;
    });

    const avgR = Math.round(totalR / colors.length);
    const avgG = Math.round(totalG / colors.length);
    const avgB = Math.round(totalB / colors.length);

    return colorUtils.rgbToHex(avgR, avgG, avgB);
  },

  // 添加肤色 - 直接打开图片选择
  addColor: function() {
    // 直接打开图片选择器
    wx.chooseImage({
      count: 1,
      sizeType: ['original', 'compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];

        // 直接跳转到图片取色页面
        wx.navigateTo({
          url: '/pages/imageColorPicker/imageColorPicker?color=' + encodeURIComponent('#EACEB8') + '&imagePath=' + encodeURIComponent(tempFilePath),
          events: {
            // 监听从图片取色页面返回的颜色
            acceptColorFromImagePicker: (result) => {
              // 检查结果是对象还是字符串
              let colorValue;
              if (typeof result === 'object' && result !== null) {
                // 如果是对象，提取color属性
                colorValue = result.color;
              } else {
                // 如果是字符串，直接使用
                colorValue = result;
              }

              if (colorValue) {
                // 添加新颜色到选择列表
                const selectedColors = [...this.data.selectedColors];
                if (selectedColors.length < 3) {
                  selectedColors.push(colorValue);
                  this.setData({
                    selectedColors: selectedColors
                  });
                  this.updateColorData();

                  wx.showToast({
                    title: '肤色已添加',
                    icon: 'success',
                    duration: 1000
                  });
                } else {
                  wx.showToast({
                    title: '最多只能选择3种肤色',
                    icon: 'none',
                    duration: 1500
                  });
                }
              }
            }
          },
          fail: (err) => {
            wx.showToast({
              title: '打开取色页面失败',
              icon: 'none',
              duration: 1500
            });
          }
        });
      },
      fail: (err) => {
        // 用户取消选择图片不显示错误
        if (err.errMsg !== 'chooseImage:fail cancel') {
          wx.showToast({
            title: '选择图片失败',
            icon: 'none',
            duration: 1500
          });
        }
      }
    });
  },

  // 编辑肤色 - 直接打开图片选择
  editColor: function(e) {
    const index = e.currentTarget.dataset.index;
    const currentColor = this.data.selectedColors[index];

    // 直接打开图片选择器
    wx.chooseImage({
      count: 1,
      sizeType: ['original', 'compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];

        // 直接跳转到图片取色页面
        wx.navigateTo({
          url: '/pages/imageColorPicker/imageColorPicker?color=' + encodeURIComponent(currentColor) + '&imagePath=' + encodeURIComponent(tempFilePath),
          events: {
            // 监听从图片取色页面返回的颜色
            acceptColorFromImagePicker: (result) => {
              // 检查结果是对象还是字符串
              let colorValue;
              if (typeof result === 'object' && result !== null) {
                // 如果是对象，提取color属性
                colorValue = result.color;
              } else {
                // 如果是字符串，直接使用
                colorValue = result;
              }

              if (colorValue) {
                // 更新指定索引的颜色
                const selectedColors = [...this.data.selectedColors];
                selectedColors[index] = colorValue;
                this.setData({
                  selectedColors: selectedColors
                });
                this.updateColorData();

                wx.showToast({
                  title: '肤色已更新',
                  icon: 'success',
                  duration: 1000
                });
              }
            }
          },
          fail: (err) => {
            wx.showToast({
              title: '打开取色页面失败',
              icon: 'none',
              duration: 1500
            });
          }
        });
      },
      fail: (err) => {
        // 用户取消选择图片不显示错误
        if (err.errMsg !== 'chooseImage:fail cancel') {
          wx.showToast({
            title: '选择图片失败',
            icon: 'none',
            duration: 1500
          });
        }
      }
    });
  },

  // 删除肤色
  removeColor: function(e) {
    const index = e.currentTarget.dataset.index;
    const selectedColors = [...this.data.selectedColors];

    selectedColors.splice(index, 1);
    this.setData({
      selectedColors: selectedColors
    });
    this.updateColorData();

    wx.showToast({
      title: '肤色已删除',
      icon: 'success',
      duration: 1000
    });
  },

  // 显示颜色选择器
  showColorPicker: function() {
    this.setData({
      editingIndex: -1,
      selectedColor: '#EACEB8',
      showColorPicker: true
    });
  },

  // 隐藏颜色选择器
  hideColorPicker: function() {
    this.setData({
      showColorPicker: false,
      editingIndex: -1
    });
  },

  // 颜色选择器变化
  onColorPickerChange: function(e) {
    const color = e.detail.color;
    this.setData({
      selectedColor: color
    });
  },

  // 颜色选择器确认
  onColorPickerConfirm: function(e) {
    const color = e.detail.color;
    const editingIndex = this.data.editingIndex;
    const selectedColors = [...this.data.selectedColors];

    if (editingIndex >= 0) {
      // 编辑现有颜色
      selectedColors[editingIndex] = color;
      wx.showToast({
        title: '肤色已更新',
        icon: 'success',
        duration: 1000
      });
    } else {
      // 添加新颜色
      if (selectedColors.length < 3) {
        selectedColors.push(color);
        wx.showToast({
          title: '肤色已添加',
          icon: 'success',
          duration: 1000
        });
      }
    }

    this.setData({
      selectedColors: selectedColors,
      showColorPicker: false,
      editingIndex: -1
    });

    this.updateColorData();
  },

  /**
   * 初始化每日点击计数（同步版本，保留兼容性）
   */
  initDailyClickCount() {
    try {
      const today = new Date().toDateString();
      const storedData = wx.getStorageSync('skinToneTest_dailyClick');

      if (storedData && storedData.date === today) {
        // 今天已有记录，使用存储的点击次数
        this.setData({
          dailyClickCount: storedData.count || 0,
          lastClickDate: storedData.date
        });
      } else {
        // 新的一天，重置计数
        this.setData({
          dailyClickCount: 0,
          lastClickDate: today
        });
        // 保存到本地存储
        wx.setStorageSync('skinToneTest_dailyClick', {
          date: today,
          count: 0
        });
      }
    } catch (error) {
      console.error('初始化每日点击计数失败', error);
      // 出错时使用默认值
      this.setData({
        dailyClickCount: 0,
        lastClickDate: new Date().toDateString()
      });
    }
  },

  /**
   * 异步初始化每日点击计数（优化版本）
   */
  async initDailyClickCountAsync() {
    try {
      const today = new Date().toDateString();

      // 使用异步方式获取存储数据
      const storedData = await new Promise((resolve) => {
        wx.getStorage({
          key: 'skinToneTest_dailyClick',
          success: (res) => resolve(res.data),
          fail: () => resolve(null)
        });
      });

      if (storedData && storedData.date === today) {
        // 今天已有记录，使用存储的点击次数
        this.setData({
          dailyClickCount: storedData.count || 0,
          lastClickDate: storedData.date
        });
      } else {
        // 新的一天，重置计数
        this.setData({
          dailyClickCount: 0,
          lastClickDate: today
        });

        // 异步保存到本地存储
        wx.setStorage({
          key: 'skinToneTest_dailyClick',
          data: {
            date: today,
            count: 0
          }
        });
      }

      console.log('[SkinToneTest] 每日点击计数初始化完成');
    } catch (error) {
      console.error('异步初始化每日点击计数失败', error);
      // 出错时使用默认值
      this.setData({
        dailyClickCount: 0,
        lastClickDate: new Date().toDateString()
      });
    }
  },

  /**
   * 初始化激励广告
   */
  initRewardedVideoAd() {
    console.log('开始初始化激励广告');

    // 防止重复初始化
    if (this.data.adInitialized) {
      console.log('广告已初始化，跳过');
      return;
    }

    // 检查是否支持激励广告
    if (wx.createRewardedVideoAd) {
      let rewardedVideoAd = null;

      try {
        console.log('创建激励广告实例');
        // 创建激励广告实例
        rewardedVideoAd = wx.createRewardedVideoAd({
          adUnitId: 'adunit-5e9ed732d45874f5' // 您的激励广告位ID
        });

        console.log('激励广告实例创建成功，立即设置错误处理函数');
      } catch (createError) {
        console.error('创建激励广告实例失败:', createError);
        this.setData({
          adInitialized: false,
          rewardedVideoAd: null
        });
        return;
      }

      // 确保广告实例存在后再设置错误处理函数
      if (rewardedVideoAd) {
        try {
          // 立即设置错误处理函数，防止创建时就出错
          rewardedVideoAd.onError(err => {
            console.error('肤色测试激励广告加载失败', err);

            // 处理两种主要错误情况
            if (err.errCode === 1004) {
              // 情况1: 调用正常，但没有合适的广告返回给用户
              console.log('没有合适的广告返回，隐藏广告入口');
              this.setData({
                adInitialized: false,
                rewardedVideoAd: null
              });
              // 无广告返回时，建议不设置激励视频入口，直接跳转
              this.navigateToReport();
            } else if (err.errCode === 1000 || err.errCode === 1001 || err.errCode === 1002) {
              // 情况2: 错误的调用导致的异常返回
              console.log('广告调用异常，错误码:', err.errCode);
              this.setData({
                adInitialized: false,
                rewardedVideoAd: null
              });
              // 调用异常时也直接跳转，不阻塞用户流程
              this.navigateToReport();
            } else {
              // 其他未知错误
              console.log('广告未知错误，错误码:', err.errCode);
              this.setData({
                adInitialized: false,
                rewardedVideoAd: null
              });
              // 保险起见，也直接跳转
              this.navigateToReport();
            }
          });

          console.log('错误处理函数设置成功');
        } catch (errorHandlerError) {
          console.error('设置错误处理函数失败:', errorHandlerError);
          this.setData({
            adInitialized: false,
            rewardedVideoAd: null
          });
          return;
        }

        try {
          // 设置其他监听器
          rewardedVideoAd.onLoad(() => {
            console.log('肤色测试激励广告加载成功');
          });

          rewardedVideoAd.onClose(res => {
            console.log('广告关闭事件:', res);
            if (res && res.isEnded) {
              // 用户完整观看了广告，继续跳转
              console.log('用户完整观看广告，跳转到报告页面');
              this.navigateToReport();
            } else {
              // 用户中途退出了广告
              console.log('用户中途退出广告');
              wx.showToast({
                title: '请观看完整广告',
                icon: 'none',
                duration: 1500
              });
            }
          });

          console.log('广告事件监听器设置成功');
        } catch (listenerError) {
          console.error('设置广告事件监听器失败:', listenerError);
          this.setData({
            adInitialized: false,
            rewardedVideoAd: null
          });
          return;
        }

        // 最后设置数据，表示初始化完成
        try {
          this.setData({
            rewardedVideoAd: rewardedVideoAd,
            adInitialized: true
          });
          console.log('激励广告初始化成功');
        } catch (setDataError) {
          console.error('设置广告数据失败:', setDataError);
          this.setData({
            adInitialized: false,
            rewardedVideoAd: null
          });
        }
      } else {
        console.error('激励广告实例创建失败，实例为空');
        this.setData({
          adInitialized: false,
          rewardedVideoAd: null
        });
      }
    } else {
      console.log('当前版本不支持激励广告，直接跳转');
      // 不支持广告时，可以直接跳转
    }
  },

  /**
   * 显示激励广告 - 修复Promise链错误处理
   */
  async showRewardedVideoAd() {
    console.log('尝试显示激励广告');
    const rewardedVideoAd = this.data.rewardedVideoAd;

    // 如果广告还未初始化，先初始化
    if (!rewardedVideoAd) {
      console.log('广告未初始化，开始初始化');
      this.initRewardedVideoAd();
      // 给一点时间让广告初始化，但设置最大重试次数
      if (!this.adRetryCount) {
        this.adRetryCount = 0;
      }

      if (this.adRetryCount < 3) {
        this.adRetryCount++;
        setTimeout(() => {
          this.showRewardedVideoAd();
        }, 1000);
      } else {
        console.log('广告初始化重试次数超限，直接跳转');
        this.navigateToReport();
      }
      return;
    }

    try {
      // 第一次尝试显示广告
      console.log('开始显示广告');
      await rewardedVideoAd.show();
      console.log('广告显示成功');
    } catch (firstErr) {
      console.log('广告显示失败，尝试重新加载:', firstErr);

      try {
        // 重新加载广告
        await rewardedVideoAd.load();
        console.log('广告重新加载成功，再次显示');

        // 第二次尝试显示广告
        await rewardedVideoAd.show();
        console.log('广告重新显示成功');
      } catch (secondErr) {
        // 最终错误处理
        console.error('激励广告最终失败', secondErr);
        wx.showToast({
          title: '广告加载失败，直接进入报告',
          icon: 'none',
          duration: 1500
        });
        // 广告失败时直接跳转
        this.navigateToReport();
      }
    }
  },

  /**
   * 更新每日点击计数
   */
  updateDailyClickCount() {
    const today = new Date().toDateString();
    let newCount = this.data.dailyClickCount + 1;

    this.setData({
      dailyClickCount: newCount,
      lastClickDate: today
    });

    // 保存到本地存储
    try {
      wx.setStorageSync('skinToneTest_dailyClick', {
        date: today,
        count: newCount
      });
    } catch (error) {
      console.error('保存每日点击计数失败', error);
    }
  },

  /**
   * 跳转到测试报告页面
   * 注意：此处必须使用 JavaScript API 而非 navigator 组件，原因：
   * 1. 需要动态构建复杂的 URL 参数（包含 JSON 数据）
   * 2. 需要错误处理和重试逻辑（URL 过长时的降级处理）
   * 3. 需要本地存储备用方案
   */
  navigateToReport() {
    const selectedColors = this.data.selectedColors;
    const averageColor = this.data.averageColor;

    logUtils.log('准备跳转到测试报告页面', { selectedColors, averageColor });

    // 跳转到测试报告页面，传递选择的颜色数据
    const colorData = {
      selectedColors: selectedColors,
      averageColor: averageColor,
      isMultiColor: selectedColors.length > 1
    };

    // 先尝试将数据存储到本地，作为备用方案
    try {
      wx.setStorageSync('skinToneTestData', colorData);
      logUtils.log('数据已存储到本地作为备用');
    } catch (error) {
      logUtils.error('存储数据失败:', error);
    }

    // 使用正确的子包路径
    const targetUrl = `/clothing-package/pages/skinToneReport/skinToneReport?colorData=${encodeURIComponent(JSON.stringify(colorData))}`;

    logUtils.log('跳转URL长度:', targetUrl.length);
    logUtils.log('跳转URL:', targetUrl);

    wx.navigateTo({
      url: targetUrl,
      success: () => {
        logUtils.log('跳转成功');
      },
      fail: (err) => {
        logUtils.error('跳转失败:', err);

        // 如果URL太长导致跳转失败，尝试不带参数跳转
        if (err.errMsg && err.errMsg.includes('url length')) {
          logUtils.log('URL过长，尝试不带参数跳转');
          wx.navigateTo({
            url: '/clothing-package/pages/skinToneReport/skinToneReport',
            success: () => {
              logUtils.log('备用跳转成功');
            },
            fail: (err2) => {
              logUtils.error('备用跳转也失败:', err2);
              wx.showToast({
                title: '跳转失败，请重试',
                icon: 'none',
                duration: 2000
              });
            }
          });
        } else {
          wx.showToast({
            title: '跳转失败，请重试',
            icon: 'none',
            duration: 2000
          });
        }
      }
    });
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {
    // 清理可能存在的定时器
    if (this.uploadTimer) {
      clearTimeout(this.uploadTimer);
      this.uploadTimer = null;
    }
    if (this.analysisTimer) {
      clearTimeout(this.analysisTimer);
      this.analysisTimer = null;
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {
    logUtils.log('肤色测试页面卸载，开始清理资源');

    // 清理激励广告事件监听器
    if (this.data.rewardedVideoAd) {
      try {
        // 移除所有事件监听器
        this.data.rewardedVideoAd.offLoad();
        this.data.rewardedVideoAd.offError();
        this.data.rewardedVideoAd.offClose();
        logUtils.log('肤色测试激励广告事件监听器已清理');
      } catch (error) {
        logUtils.error('清理肤色测试激励广告事件监听器失败:', error);
      }

      // 清空广告实例引用
      this.setData({
        rewardedVideoAd: null,
        adInitialized: false
      });
    }

    // 重置广告重试计数器
    this.adRetryCount = 0;

    // 清理可能存在的定时器
    if (this.uploadTimer) {
      clearTimeout(this.uploadTimer);
      this.uploadTimer = null;
    }
    if (this.analysisTimer) {
      clearTimeout(this.analysisTimer);
      this.analysisTimer = null;
    }

    logUtils.log('肤色测试页面资源清理完成');
  },

  // 生成测试报告
  generateReport: function() {
    logUtils.log('开始生成测试报告');

    const selectedColors = this.data.selectedColors;
    logUtils.log('当前选择的肤色:', selectedColors);

    // 检查是否已选择肤色
    if (selectedColors.length === 0) {
      logUtils.log('未选择肤色，显示提示');
      wx.showToast({
        title: '请先选择肤色',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    try {
      // 更新每日点击计数
      this.updateDailyClickCount();

      // 检查去广告状态
      const isAdFree = adFreeUtils.isAdFreeActivated();
      logUtils.log('去广告状态:', isAdFree);
      logUtils.log('每日点击次数:', this.data.dailyClickCount);

      // 判断是否需要显示激励广告（去广告激活时不显示广告）
      const shouldShowAd = !isAdFree && this.data.dailyClickCount > 1; // 第二次开始显示广告
      logUtils.log('是否需要显示广告:', shouldShowAd);

      if (shouldShowAd) {
        // 显示激励广告
        logUtils.log('显示激励广告');
        this.showRewardedVideoAd();
      } else {
        // 直接跳转
        logUtils.log('直接跳转到报告页面');
        this.navigateToReport();
      }
    } catch (error) {
      logUtils.error('生成测试报告过程中出错:', error);
      wx.showToast({
        title: '生成报告失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 复制颜色代码
  copyColorCode: function(e) {
    const color = e.currentTarget.dataset.color || this.data.selectedColor;

    wx.setClipboardData({
      data: color,
      success: () => {
        wx.showToast({
          title: '颜色代码已复制',
          icon: 'success',
          duration: 1500
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none',
          duration: 1500
        });
      }
    });
  },

  /**
   * 初始化色卡数据（同步版本，保留兼容性）
   */
  initSkinToneCards: function() {
    try {
      // 从本地存储加载自定义色卡数据（使用缓存）
      const customColorsOne = storageCache.getStorageSync('skinToneColorsOne');
      const customColorsTwo = storageCache.getStorageSync('skinToneColorsTwo');

      this.setData({
        skinToneColorsOne: customColorsOne && customColorsOne.length > 0 ? customColorsOne : SKIN_TONE_COLORS_ONE,
        skinToneColorsTwo: customColorsTwo && customColorsTwo.length > 0 ? customColorsTwo : SKIN_TONE_COLORS_TWO
      });

      console.log('[SkinToneTest] 色卡数据初始化完成（使用缓存）');
    } catch (error) {
      console.error('初始化色卡数据失败', error);
      // 出错时使用默认数据
      this.setData({
        skinToneColorsOne: SKIN_TONE_COLORS_ONE,
        skinToneColorsTwo: SKIN_TONE_COLORS_TWO
      });
    }
  },

  /**
   * 异步初始化色卡数据（优化版本）
   */
  async initSkinToneCardsAsync() {
    try {
      // 使用缓存的异步方式获取存储数据
      const [customColorsOne, customColorsTwo] = await Promise.all([
        storageCache.getStorage('skinToneColorsOne'),
        storageCache.getStorage('skinToneColorsTwo')
      ]);

      this.setData({
        skinToneColorsOne: customColorsOne && customColorsOne.length > 0 ? customColorsOne : SKIN_TONE_COLORS_ONE,
        skinToneColorsTwo: customColorsTwo && customColorsTwo.length > 0 ? customColorsTwo : SKIN_TONE_COLORS_TWO
      });

      console.log('[SkinToneTest] 色卡数据异步初始化完成（使用缓存）');
    } catch (error) {
      console.error('异步初始化色卡数据失败', error);
      // 出错时使用默认数据
      this.setData({
        skinToneColorsOne: SKIN_TONE_COLORS_ONE,
        skinToneColorsTwo: SKIN_TONE_COLORS_TWO
      });
    }
  },

  /**
   * 编辑色卡项
   */
  editSkinToneCard: function(e) {
    const { type, index } = e.currentTarget.dataset;
    const cardData = type === 'one' ? this.data.skinToneColorsOne : this.data.skinToneColorsTwo;
    const item = cardData[index];

    this.setData({
      showCardEditModal: true,
      editingCardType: type,
      editingCardIndex: index,
      editingCardColor: item.color,
      editingCardName: item.name
    });
  },

  /**
   * 隐藏色卡编辑弹窗
   */
  hideCardEditModal: function() {
    this.setData({
      showCardEditModal: false,
      editingCardType: '',
      editingCardIndex: -1,
      editingCardColor: '',
      editingCardName: ''
    });
  },

  /**
   * 编辑颜色输入处理
   */
  onEditColorInput: function(e) {
    this.setData({
      editingCardColor: e.detail.value
    });
  },

  /**
   * 编辑名称输入处理
   */
  onEditNameInput: function(e) {
    this.setData({
      editingCardName: e.detail.value
    });
  },

  /**
   * 颜色输入确认处理
   */
  onEditColorConfirm: function(e) {
    const value = e.detail.value.trim();
    if (this.isValidCardColor(value)) {
      const color = value.startsWith('#') ? value : '#' + value;
      this.setData({
        editingCardColor: color.toUpperCase()
      });
    } else {
      wx.showToast({
        title: '颜色格式不正确',
        icon: 'none',
        duration: 1500
      });
    }
  },

  /**
   * 验证色卡颜色值是否有效
   */
  isValidCardColor: function(color) {
    if (!color) return false;

    // 移除#号
    const cleanColor = color.replace('#', '');

    // 检查是否为3位或6位十六进制
    const hexPattern = /^[0-9A-Fa-f]{3}$|^[0-9A-Fa-f]{6}$/;
    return hexPattern.test(cleanColor);
  },

  /**
   * 粘贴色卡颜色值
   */
  pasteCardColor: function() {
    wx.getClipboardData({
      success: (res) => {
        const clipboardData = res.data.trim();
        if (this.isValidCardColor(clipboardData)) {
          let color = clipboardData.startsWith('#') ? clipboardData : '#' + clipboardData;

          // 如果是3位十六进制，转换为6位
          if (color.length === 4) {
            color = '#' + color[1] + color[1] + color[2] + color[2] + color[3] + color[3];
          }

          this.setData({
            editingCardColor: color.toUpperCase()
          });

          wx.showToast({
            title: '粘贴成功',
            icon: 'success',
            duration: 1000
          });
        } else {
          wx.showToast({
            title: '剪贴板中不是有效的颜色值',
            icon: 'none',
            duration: 1500
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '获取剪贴板失败',
          icon: 'none',
          duration: 1500
        });
      }
    });
  },

  /**
   * 从图片取色
   */
  pickCardColorFromImage: function() {
    wx.chooseImage({
      count: 1,
      sizeType: ['original', 'compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];

        // 跳转到图片取色页面
        wx.navigateTo({
          url: '/pages/imageColorPicker/imageColorPicker?color=' + encodeURIComponent(this.data.editingCardColor || '#F5EBE2') + '&imagePath=' + encodeURIComponent(tempFilePath),
          events: {
            // 监听从图片取色页面返回的颜色
            acceptColorFromImagePicker: (result) => {
              let colorValue;
              if (typeof result === 'object' && result !== null) {
                colorValue = result.color;
              } else {
                colorValue = result;
              }

              if (colorValue) {
                this.setData({
                  editingCardColor: colorValue.toUpperCase()
                });
                wx.showToast({
                  title: '颜色已更新',
                  icon: 'success',
                  duration: 1000
                });
              }
            }
          },
          fail: (err) => {
            wx.showToast({
              title: '打开取色页面失败',
              icon: 'none',
              duration: 1500
            });
          }
        });
      },
      fail: (err) => {
        if (err.errMsg !== 'chooseImage:fail cancel') {
          wx.showToast({
            title: '选择图片失败',
            icon: 'none',
            duration: 1500
          });
        }
      }
    });
  },

  /**
   * 保存色卡编辑
   */
  saveCardEdit: function() {
    const { editingCardType, editingCardIndex, editingCardColor, editingCardName } = this.data;

    // 验证输入
    if (!editingCardColor.trim()) {
      wx.showToast({
        title: '请输入颜色值',
        icon: 'none',
        duration: 1500
      });
      return;
    }

    if (!editingCardName.trim()) {
      wx.showToast({
        title: '请输入颜色名称',
        icon: 'none',
        duration: 1500
      });
      return;
    }

    // 验证颜色格式
    const colorRegex = /^#[0-9A-Fa-f]{6}$/;
    if (!colorRegex.test(editingCardColor.trim())) {
      wx.showToast({
        title: '颜色格式错误，请使用#RRGGBB格式',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    try {
      // 更新对应的色卡数据
      const cardKey = editingCardType === 'one' ? 'skinToneColorsOne' : 'skinToneColorsTwo';
      const cardData = [...this.data[cardKey]];

      cardData[editingCardIndex] = {
        name: editingCardName.trim(),
        color: editingCardColor.trim().toUpperCase()
      };

      // 更新页面数据
      this.setData({
        [cardKey]: cardData
      });

      // 保存到本地存储（使用缓存）
      const storageKey = editingCardType === 'one' ? 'skinToneColorsOne' : 'skinToneColorsTwo';
      storageCache.setStorageSync(storageKey, cardData);

      // 关闭弹窗
      this.hideCardEditModal();

      wx.showToast({
        title: '保存成功',
        icon: 'success',
        duration: 1500
      });

    } catch (error) {
      console.error('保存色卡编辑失败', error);
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none',
        duration: 1500
      });
    }
  },

  /**
   * 重置色卡为默认值
   */
  resetSkinToneCards: function() {
    wx.showModal({
      title: '确认重置',
      content: '确定要将所有色卡重置为默认值吗？此操作不可撤销。',
      confirmText: '确认重置',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          try {
            // 重置为默认数据
            this.setData({
              skinToneColorsOne: SKIN_TONE_COLORS_ONE,
              skinToneColorsTwo: SKIN_TONE_COLORS_TWO
            });

            // 清除本地存储（使用缓存）
            storageCache.removeStorageSync('skinToneColorsOne');
            storageCache.removeStorageSync('skinToneColorsTwo');

            wx.showToast({
              title: '重置成功',
              icon: 'success',
              duration: 1500
            });

          } catch (error) {
            console.error('重置色卡失败', error);
            wx.showToast({
              title: '重置失败，请重试',
              icon: 'none',
              duration: 1500
            });
          }
        }
      }
    });
  }
});
