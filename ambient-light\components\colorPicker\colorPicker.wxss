/* components/colorPicker/colorPicker.wxss */
.color-picker {
  padding: 30rpx;
  height: 780rpx; /* 增加高度，确保最后一行色值完整显示 */
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #f8f8f8;
  border-radius: 16rpx;
}

/* 颜色预览和HEX值 */
.color-preview-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
  flex-shrink: 0;
}

.color-preview {
  width: 100%;
  height: 100rpx;
  border-radius: 12rpx;
  border: 1rpx solid #e0e0e0;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

/* 添加棋盘格背景，便于查看半透明颜色 */
.color-preview::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(45deg, #eee 25%, transparent 25%),
    linear-gradient(-45deg, #eee 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, #eee 75%),
    linear-gradient(-45deg, transparent 75%, #eee 75%);
  background-size: 16rpx 16rpx;
  background-position: 0 0, 0 8rpx, 8rpx -8rpx, -8rpx 0px;
  z-index: -1;
  opacity: 0.5;
}

/* 选择器模式切换 */
.mode-tabs {
  display: flex;
  margin-bottom: 20rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 6rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
}

.mode-tab {
  flex: 1;
  text-align: center;
  padding: 12rpx 0;
  font-size: 28rpx;
  color: #666;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.mode-tab.active {
  color: #ffffff;
  background-color: #07c160;
  font-weight: bold;
  box-shadow: 0 2rpx 6rpx rgba(7, 193, 96, 0.3);
}

/* 选择器内容区域 */
.picker-content {
  flex: 1;
  overflow: hidden;
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
}

/* 网格模式 */
.color-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  grid-template-rows: repeat(5, 1fr);
  gap: 8rpx; /* 减小间距 */
  padding: 12rpx;
  height: 100%;
  box-sizing: border-box;
  align-content: space-between; /* 确保行之间均匀分布 */
}

.color-cell {
  width: 100%;
  height: 64rpx; /* 略微增加高度 */
  border-radius: 8rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
  position: relative;
  overflow: hidden;
}

/* 添加棋盘格背景，便于查看半透明颜色 */
.color-cell::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(45deg, #eee 25%, transparent 25%),
    linear-gradient(-45deg, #eee 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, #eee 75%),
    linear-gradient(-45deg, transparent 75%, #eee 75%);
  background-size: 16rpx 16rpx;
  background-position: 0 0, 0 8rpx, 8rpx -8rpx, -8rpx 0px;
  z-index: -1;
  opacity: 0.5;
}

.color-cell:active {
  transform: scale(1.1);
  z-index: 1;
}

/* 光谱模式 */
.color-spectrum-container {
  width: 100%;
  height: 100%;
  padding: 20rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.color-spectrum {
  width: 100%;
  height: 320rpx;
  border-radius: 8rpx;
  position: relative;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 背景色通过内联样式动态设置 */
.color-spectrum {
  background-size: 100% 100%;
}

/* 添加黑色渐变覆盖层 */
.color-spectrum::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 1) 100%);
  pointer-events: none;
}

.spectrum-selector {
  position: absolute;
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  border: 2rpx solid #ffffff;
  box-shadow: 0 0 0 1rpx rgba(0, 0, 0, 0.3);
  transform: translate(-50%, -50%);
  z-index: 2;
  pointer-events: none;
}

/* 色相滑块 */
.hue-slider-container {
  width: 100%;
  height: 40rpx;
  position: relative;
}

.hue-slider {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
  position: relative;
  background: linear-gradient(to right,
    #ff0000 0%, #ffff00 17%, #00ff00 33%,
    #00ffff 50%, #0000ff 67%, #ff00ff 83%, #ff0000 100%);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.hue-slider-thumb {
  position: absolute;
  width: 20rpx;
  height: 40rpx;
  background-color: #ffffff;
  border-radius: 4rpx;
  box-shadow: 0 0 4rpx rgba(0, 0, 0, 0.3);
  transform: translateX(-50%);
  top: 0;
  pointer-events: none;
}

/* 滑块模式 */
.slider-group {
  display: flex;
  align-items: center;
  padding: 16rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.slider-label {
  width: 80rpx;
  font-size: 28rpx;
  color: #333;
  flex-shrink: 0;
}

.slider-bar-container {
  flex: 1;
  padding: 0 20rpx;
}

.slider-bar {
  margin: 0;
}

.slider-value {
  width: 60rpx;
  font-size: 28rpx;
  color: #333;
  text-align: right;
  flex-shrink: 0;
}

/* HEX输入框样式 */
.hex-input-group {
  display: flex;
  align-items: center;
  padding: 16rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  margin: 16rpx 20rpx;
  flex-wrap: nowrap;
}

.hex-input-label {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-right: 10rpx;
}

.hex-input-field {
  flex: 1;
  height: 64rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #ffffff;
  border-radius: 6rpx;
  padding: 0 16rpx;
  border: 1rpx solid #e0e0e0;
  font-family: monospace;
  text-transform: uppercase;
}

/* 复制和粘贴按钮样式 */
.hex-button {
  height: 64rpx;
  line-height: 64rpx;
  padding: 0 12rpx;
  font-size: 24rpx;
  color: #666;
  background-color: #f5f5f5;
  border-radius: 6rpx;
  margin-left: 8rpx;
  text-align: center;
  border: 1rpx solid #e0e0e0;
  box-sizing: border-box;
  white-space: nowrap;
}

.copy-button {
  background-color: #f0f0f0;
}

.paste-button {
  background-color: #f0f0f0;
}

/* 按钮 */
.buttons {
  display: flex;
  gap: 20rpx;
  flex-shrink: 0;
  margin-top: 10rpx; /* 添加固定的上边距 */
}

.btn {
  flex: 1;
  font-size: 28rpx;
  padding: 12rpx 0;
  border-radius: 8rpx;
  border: none;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #333;
}

.confirm-btn {
  background-color: #07c160;
  color: #fff;
}
