<!--pages/colorPalette/colorPalette.wxml-->
<view class="page">
  <view class="container">
    <!-- 基础颜色选择 -->
    <view class="section base-color-section">
      <view class="section-title">基础颜色</view>
      <view class="base-color-container">
        <!-- 左侧颜色色块，在中央显示16进制色值 -->
        <view class="color-preview" style="background-color: {{baseColor}};" bindtap="showColorPicker">
          <view class="color-hex-value" style="color: {{textColor}};">{{baseColor}}</view>
        </view>
        <!-- 右侧上下并列的按钮 -->
        <view class="color-actions-column">
          <view class="btn-wrapper">
            <view class="custom-btn random-btn" bindtap="randomColor">随机颜色</view>
          </view>
          <view class="btn-wrapper">
            <view class="custom-btn picker-btn" bindtap="showColorPicker">选择颜色</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 合并的配色方案和色轮可视化模块 -->
    <view class="section combined-section">
      <view class="section-title">配色方案</view>

      <!-- 布局容器 -->
      <view class="combined-layout">
        <!-- 色轮说明文字已移除 -->

        <!-- 配色方案类型选择 -->
        <view class="palette-type-container">
          <!-- 第一行标签 -->
          <view class="palette-type-row">
            <view
              class="palette-type-item {{paletteType === paletteTypes[0].id ? 'active' : ''}}"
              bindtap="switchPaletteType"
              data-type="{{paletteTypes[0].id}}"
            >
              <text class="palette-type-text">{{paletteTypes[0].name}}</text>
            </view>
            <view
              class="palette-type-item {{paletteType === paletteTypes[1].id ? 'active' : ''}}"
              bindtap="switchPaletteType"
              data-type="{{paletteTypes[1].id}}"
            >
              <text class="palette-type-text">{{paletteTypes[1].name}}</text>
            </view>
            <view
              class="palette-type-item {{paletteType === paletteTypes[2].id ? 'active' : ''}}"
              bindtap="switchPaletteType"
              data-type="{{paletteTypes[2].id}}"
            >
              <text class="palette-type-text">{{paletteTypes[2].name}}</text>
            </view>
          </view>

          <!-- 第二行标签 -->
          <view class="palette-type-row">
            <view
              class="palette-type-item {{paletteType === paletteTypes[3].id ? 'active' : ''}}"
              bindtap="switchPaletteType"
              data-type="{{paletteTypes[3].id}}"
            >
              <text class="palette-type-text">{{paletteTypes[3].name}}</text>
            </view>
            <view
              class="palette-type-item {{paletteType === paletteTypes[4].id ? 'active' : ''}}"
              bindtap="switchPaletteType"
              data-type="{{paletteTypes[4].id}}"
            >
              <text class="palette-type-text">{{paletteTypes[4].name}}</text>
            </view>
            <view
              class="palette-type-item {{paletteType === paletteTypes[5].id ? 'active' : ''}}"
              bindtap="switchPaletteType"
              data-type="{{paletteTypes[5].id}}"
            >
              <text class="palette-type-text">{{paletteTypes[5].name}}</text>
            </view>
          </view>
        </view>

        <!-- 配色方案名称已移除 -->

        <!-- 颜色预览和色轮区域 -->
        <view class="color-preview-wheel-container">
          <!-- 色轮可视化 -->
          <view class="color-wheel-wrapper" id="colorWheelContainer">
            <color-wheel-canvas
              id="colorWheel"
              canvasId="colorWheelCanvas"
              width="240"
              height="240"
              wheelSize="220"
              selectedColor="{{baseColor}}"
              paletteType="{{paletteType}}"
              colorPalette="{{colorPalette}}"
              bind:colorChange="onColorWheelChange"
            ></color-wheel-canvas>
          </view>

          <!-- 颜色预览区域 -->
          <view class="color-preview-area">
            <!-- 相关颜色说明 - 移到配色色块上方 -->
            <view class="related-colors-info">
              <text class="related-colors-text">大圆点为基础颜色，小圆点为当前配色方案生成的颜色。单色调方案不显示额外颜色点。</text>
            </view>

            <!-- 颜色预览条 - 使用核心颜色 -->
            <view class="color-preview-bar" wx:if="{{coreColors.length > 0}}">
              <view
                wx:for="{{coreColors}}"
                wx:key="index"
                class="color-preview-segment"
                style="background-color: {{item}}; flex: 1;"
                bindtap="copyColor"
                data-color="{{item}}"
                data-index="{{index}}"
              >
                <view class="segment-copy-hint {{copiedIndex === index ? 'show' : ''}}">已复制</view>
              </view>
            </view>

            <!-- 颜色值显示 - 使用核心颜色 -->
            <view class="color-preview-values">
              <view
                wx:for="{{coreColors}}"
                wx:key="index"
                class="color-preview-value"
                bindtap="copyColor"
                data-color="{{item}}"
                data-index="{{index}}"
              >
                <text class="color-value-text">{{item}}</text>
                <view class="copy-hint {{copiedIndex === index ? 'show' : ''}}">已复制</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>


  </view>

  <!-- 颜色选择器弹窗 -->
  <view class="color-picker-modal" wx:if="{{showColorPicker}}">
    <view class="color-picker-container">
      <view class="color-picker-header">
        <view class="color-picker-title">选择颜色</view>
        <view class="color-picker-close" bindtap="hideColorPicker">×</view>
      </view>
      <!-- 集成颜色选择器组件 - 用时注入 -->
      <color-picker
        wx:if="{{showColorPicker}}"
        color="{{baseColor}}"
        bindchange="onColorPickerChange"
        bindconfirm="onColorPickerConfirm"
        bindcancel="hideColorPicker"
      ></color-picker>
    </view>
  </view>
</view>
