<!-- P04春日樱语色卡容器 -->
<view class="p04-card-container" id="cardContainer">
  <!-- 上半部分背景 -->
  <view class="p04-top-section" style="background-color: {{topBackgroundColor}};">
    <!-- 标题 -->
    <view class="p04-title" style="color: {{fontColor}};">{{title}}</view>

    <!-- P04花瓣形状SVG色块 -->
    <view class="p04-svg-petals" data-count="{{colors.length}}">
      <view
        wx:for="{{colors}}"
        wx:key="index"
        class="p04-svg-petal"
        bindtap="onPetalTap"
        data-index="{{index}}"
        data-color="{{item}}"
      >
        <image
          wx:if="{{svgDataUrls[index]}}"
          src="{{svgDataUrls[index]}}"
          class="petal-svg"
          mode="aspectFit"
        />
        <view wx:else class="petal-loading">
          <text>加载中...</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 下半部分背景 -->
  <view class="p04-bottom-section" style="background-color: {{bottomBackgroundColor}};">
    <!-- 颜色代码 -->
    <view class="p04-color-codes" data-count="{{colors.length}}">
      <view
        wx:for="{{colors}}"
        wx:key="index"
        class="p04-color-code"
      >{{item}}</view>
    </view>

    <!-- 圆形色块 -->
    <view class="p04-circles" data-count="{{colors.length}}">
      <view
        wx:for="{{colors}}"
        wx:key="index"
        class="p04-circle"
        style="background-color: {{item}};"
      ></view>
    </view>
  </view>
</view>

<!-- 隐藏的Canvas用于生成图片 -->
<canvas
  id="colorCardCanvas"
  type="2d"
  style="width: {{canvasWidth}}rpx; height: {{canvasHeight}}rpx; position: absolute; left: -9999rpx; top: -9999rpx;"
></canvas>
