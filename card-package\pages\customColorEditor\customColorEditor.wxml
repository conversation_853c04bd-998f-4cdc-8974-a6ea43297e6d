<!--pages/customColorEditor/customColorEditor.wxml-->
<view class="page-wrapper">
  <!-- 预览效果区域 - 放在顶部，模拟实际生成的色卡样式 -->
  <view class="preview-section">
    <!-- P02样式预览（蜜桃汽水模板） -->
    <view wx:if="{{templateId === 101}}" class="preview-container">
      <!-- 上半部分背景 -->
      <view class="preview-top-bg" style="background-color: {{topBackgroundColor}};">
        <view class="preview-title" style="color: {{fontColor}};">{{templateName}}</view>
        <view wx:if="{{subTitle}}" class="preview-subtitle" style="color: {{fontColor}};">{{subTitle}}</view>
      </view>

      <!-- 下半部分背景 -->
      <view class="preview-bottom-bg" style="background-color: {{bottomBackgroundColor}};">
        <!-- 下半部分内容容器 - 垂直居中 -->
        <view class="preview-bottom-content-p02">
          <!-- 正方形色块 -->
          <view class="preview-square-blocks">
            <view
              wx:for="{{colors}}"
              wx:key="index"
              class="preview-square-block"
              style="background-color: {{item}};"
            ></view>
          </view>
        </view>
      </view>

      <!-- 圆形色块区域 - 绝对定位，跨越上下背景 -->
      <view class="preview-circles-area" style="--top-bg-color: {{topBackgroundColor}};">
        <view class="preview-circles">
          <view
            wx:for="{{colors}}"
            wx:key="index"
            class="preview-circle-item"
          >
            <view class="preview-circle" style="background-color: {{item}};"></view>
            <view class="preview-color-code" style="color: {{fontColor}};">{{item}}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- P02样式预览（海盐气泡模板） -->
    <view wx:elif="{{templateId === 102}}" class="preview-container-p02" style="background-color: {{backgroundColor}};">
      <view class="preview-title-p02" style="color: {{titleColor}};">{{templateName}}</view>
      <view wx:if="{{subTitle}}" class="preview-subtitle-p02" style="color: {{titleColor}};">{{subTitle}}</view>
      <view class="preview-long-blocks">
        <view
          wx:for="{{colors}}"
          wx:key="index"
          class="preview-long-block"
          style="background-color: {{item}};"
        ></view>
      </view>
      <view class="preview-color-codes-p02">
        <view
          wx:for="{{colors}}"
          wx:key="index"
          class="preview-color-code-p02"
          style="color: {{item}};"
        >{{item}}</view>
      </view>
      <view class="preview-circles-p02">
        <view
          wx:for="{{colors}}"
          wx:key="index"
          class="preview-circle-p02"
          style="background-color: {{item}};"
        ></view>
      </view>
    </view>

    <!-- P03样式预览（落日漫旅模板） -->
    <view wx:elif="{{templateId === 103}}" class="preview-container-p03">
      <!-- 上半部分背景 -->
      <view class="preview-top-bg-p03" style="background-color: {{topBackgroundColor}};">
        <view class="preview-title-p03" style="color: {{fontColor}};">{{templateName}}</view>
        <view wx:if="{{subTitle}}" class="preview-subtitle-p03" style="color: {{fontColor}};">{{subTitle}}</view>

        <!-- 旋转方形色块 -->
        <view class="preview-diamond-blocks">
          <view
            wx:for="{{colors}}"
            wx:key="index"
            class="preview-diamond-block"
            style="background-color: {{item}};"
          ></view>
        </view>
      </view>

      <!-- 下半部分背景 -->
      <view class="preview-bottom-bg-p03" style="background-color: {{bottomBackgroundColor}};">
        <!-- 下半部分内容容器 - 垂直居中 -->
        <view class="preview-bottom-content-p03">
          <!-- 颜色代码 -->
          <view class="preview-color-codes-p03">
            <view
              wx:for="{{colors}}"
              wx:key="index"
              class="preview-color-code-p03"
              style="color: #7D7D7D;"
            >{{item}}</view>
          </view>

          <!-- 圆形色块 -->
          <view class="preview-circles-p03">
            <view
              wx:for="{{colors}}"
              wx:key="index"
              class="preview-circle-p03"
              style="background-color: {{item}};"
            ></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 春日樱语模板预览 (ID: 104) -->
    <view wx:elif="{{templateId === 104}}" class="preview-container">
      <view class="preview-card-p04">
        <!-- 上半部分背景 -->
        <view class="preview-top-bg-p04" style="background-color: {{topBackgroundColor}};">
          <!-- 标题 -->
          <view class="preview-title-p04" style="color: {{fontColor}};">{{templateName}}</view>
          <view wx:if="{{subTitle}}" class="preview-subtitle-p04" style="color: {{fontColor}};">{{subTitle}}</view>

          <!-- 花瓣形状色块 -->
          <view class="preview-petal-shapes">
            <canvas
              wx:for="{{colors}}"
              wx:key="index"
              class="preview-petal-canvas"
              id="petal-{{index}}"
              type="2d"
              data-color="{{item}}"
              data-index="{{index}}"
            ></canvas>
          </view>
        </view>

        <!-- 下半部分背景 -->
        <view class="preview-bottom-bg-p04" style="background-color: {{bottomBackgroundColor}};">
          <!-- 下半部分内容容器 - 垂直居中 -->
          <view class="preview-bottom-content-p04">
            <!-- 颜色代码 -->
            <view class="preview-color-codes-p04">
              <view
                wx:for="{{colors}}"
                wx:key="index"
                class="preview-color-code-p04"
              >{{item}}</view>
            </view>

            <!-- 星形圆形色块 -->
            <view class="preview-star-circles">
              <canvas
                wx:for="{{colors}}"
                wx:key="index"
                class="preview-star-canvas"
                id="star-{{index}}"
                type="2d"
                data-color="{{item}}"
                data-index="{{index}}"
              ></canvas>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 模板设置区域 - 移到颜色编辑上方 -->
  <view class="settings-section">
    <view class="setting-item" bindtap="editTitle">
      <view class="setting-label">
        <view class="setting-icon">📝</view>
        <text>主标题</text>
      </view>
      <view class="setting-value">
        <text>{{templateName}}</text>
        <view class="setting-arrow">›</view>
      </view>
    </view>

    <view class="setting-item" bindtap="editSubTitle">
      <view class="setting-label">
        <view class="setting-icon">📄</view>
        <text>副标题</text>
      </view>
      <view class="setting-value">
        <text wx:if="{{subTitle}}" class="setting-text">{{subTitle}}</text>
        <text wx:else class="setting-placeholder">点击设置副标题</text>
        <view class="setting-arrow">›</view>
      </view>
    </view>

    <!-- P02样式模板（101）的字体颜色设置 -->
    <block wx:if="{{templateId === 101}}">
      <view class="setting-item" bindtap="editFontColor">
        <view class="setting-label">
          <view class="setting-icon">🖋️</view>
          <text>字体颜色</text>
        </view>
        <view class="setting-value">
          <view class="bg-color-preview" style="background-color: {{fontColor}};"></view>
          <text>{{fontColor}}</text>
          <view class="setting-arrow">›</view>
        </view>
      </view>

      <view class="setting-item" bindtap="editTopBackgroundColor">
        <view class="setting-label">
          <view class="setting-icon">🎨</view>
          <text>上半段背景</text>
        </view>
        <view class="setting-value">
          <view class="bg-color-preview" style="background-color: {{topBackgroundColor}};"></view>
          <text>{{topBackgroundColor}}</text>
          <view class="setting-arrow">›</view>
        </view>
      </view>

      <view class="setting-item" bindtap="editBottomBackgroundColor">
        <view class="setting-label">
          <view class="setting-icon">🎨</view>
          <text>下半段背景</text>
        </view>
        <view class="setting-value">
          <view class="bg-color-preview" style="background-color: {{bottomBackgroundColor}};"></view>
          <text>{{bottomBackgroundColor}}</text>
          <view class="setting-arrow">›</view>
        </view>
      </view>
    </block>

    <!-- P01样式模板（102）的背景设置 -->
    <block wx:if="{{templateId === 102}}">
      <view class="setting-item" bindtap="editTitleColor">
        <view class="setting-label">
          <view class="setting-icon">🖋️</view>
          <text>标题颜色</text>
        </view>
        <view class="setting-value">
          <view class="bg-color-preview" style="background-color: {{titleColor}};"></view>
          <text>{{titleColor}}</text>
          <view class="setting-arrow">›</view>
        </view>
      </view>

      <view class="setting-item" bindtap="editBackgroundColor">
        <view class="setting-label">
          <view class="setting-icon">🎨</view>
          <text>背景颜色</text>
        </view>
        <view class="setting-value">
          <view class="bg-color-preview" style="background-color: {{backgroundColor}};"></view>
          <text>{{backgroundColor}}</text>
          <view class="setting-arrow">›</view>
        </view>
      </view>
    </block>

    <!-- P03样式模板（103）的设置 -->
    <block wx:if="{{templateId === 103}}">
      <view class="setting-item" bindtap="editFontColor">
        <view class="setting-label">
          <view class="setting-icon">🖋️</view>
          <text>标题颜色</text>
        </view>
        <view class="setting-value">
          <view class="bg-color-preview" style="background-color: {{fontColor}};"></view>
          <text>{{fontColor}}</text>
          <view class="setting-arrow">›</view>
        </view>
      </view>

      <view class="setting-item" bindtap="editTopBackgroundColor">
        <view class="setting-label">
          <view class="setting-icon">🎨</view>
          <text>上半段背景</text>
        </view>
        <view class="setting-value">
          <view class="bg-color-preview" style="background-color: {{topBackgroundColor}};"></view>
          <text>{{topBackgroundColor}}</text>
          <view class="setting-arrow">›</view>
        </view>
      </view>

      <view class="setting-item" bindtap="editBottomBackgroundColor">
        <view class="setting-label">
          <view class="setting-icon">🎨</view>
          <text>下半段背景</text>
        </view>
        <view class="setting-value">
          <view class="bg-color-preview" style="background-color: {{bottomBackgroundColor}};"></view>
          <text>{{bottomBackgroundColor}}</text>
          <view class="setting-arrow">›</view>
        </view>
      </view>
    </block>

    <!-- P04样式模板（104）的设置 -->
    <block wx:if="{{templateId === 104}}">
      <view class="setting-item" bindtap="editFontColor">
        <view class="setting-label">
          <view class="setting-icon">🖋️</view>
          <text>标题颜色</text>
        </view>
        <view class="setting-value">
          <view class="bg-color-preview" style="background-color: {{fontColor}};"></view>
          <text>{{fontColor}}</text>
          <view class="setting-arrow">›</view>
        </view>
      </view>

      <view class="setting-item" bindtap="editTopBackgroundColor">
        <view class="setting-label">
          <view class="setting-icon">🎨</view>
          <text>上半段背景</text>
        </view>
        <view class="setting-value">
          <view class="bg-color-preview" style="background-color: {{topBackgroundColor}};"></view>
          <text>{{topBackgroundColor}}</text>
          <view class="setting-arrow">›</view>
        </view>
      </view>

      <view class="setting-item" bindtap="editBottomBackgroundColor">
        <view class="setting-label">
          <view class="setting-icon">🎨</view>
          <text>下半段背景</text>
        </view>
        <view class="setting-value">
          <view class="bg-color-preview" style="background-color: {{bottomBackgroundColor}};"></view>
          <text>{{bottomBackgroundColor}}</text>
          <view class="setting-arrow">›</view>
        </view>
      </view>
    </block>
  </view>

  <!-- 颜色选择区域 -->
  <view class="colors-section">
    <view class="colors-title">
      <view class="title-left">
        <view class="info-dot"></view>
        <view class="title-text">
          <text>最多可添加5个颜色</text>
        </view>
      </view>
      <view class="color-count-controls">
        <view class="count-display">{{colors.length}}/5</view>
      </view>
    </view>

    <!-- 颜色列表 - 一行显示2个 -->
    <view class="colors-grid">
      <view
        wx:for="{{colors}}"
        wx:key="index"
        class="color-item"
      >
        <!-- 颜色块 -->
        <view class="color-block" style="background-color: {{item}};" bindtap="editColor" data-index="{{index}}">
          <view class="color-number">{{index + 1}}</view>
        </view>

        <!-- 操作按钮 - 右侧上下并列 -->
        <view class="color-actions">
          <view class="edit-btn" bindtap="editColor" data-index="{{index}}">编辑</view>
          <view class="delete-btn" bindtap="removeColor" data-index="{{index}}" wx:if="{{colors.length > 2}}">删除</view>
        </view>
      </view>
    </view>

    <!-- 添加颜色按钮 - 放在颜色列表下方 -->
    <view class="add-color-section">
      <!-- 添加颜色按钮 - 只在颜色数量小于5时显示 -->
      <view class="add-color-btn gray-btn" bindtap="addColor" wx:if="{{colors.length < 5}}">
        <view class="add-icon">+</view>
        <text>添加颜色</text>
      </view>

      <!-- 一键图片取色按钮 - 始终显示 -->
      <view class="image-color-btn green-btn" bindtap="openImageColorPicker">
        <view class="image-icon">📷</view>
        <text>一键图片取色</text>
      </view>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="btn-container">
    <button class="next-btn" bindtap="generateColorCard">下一步，制作色卡</button>
  </view>
</view>

<!-- 颜色选择器弹窗 -->
<view class="color-picker-modal" wx:if="{{showColorPicker}}">
  <view class="color-picker-container">
    <view class="color-picker-header">
      <view class="color-picker-title">选择颜色 {{editingIndex + 1}}</view>
      <view class="color-picker-close" bindtap="cancelColorPicker">×</view>
    </view>

    <view class="color-picker-content">
      <!-- 用时注入颜色选择器组件 -->
      <color-picker
        wx:if="{{showColorPicker}}"
        color="{{selectedColor}}"
        bindchange="onColorChange"
        bindconfirm="onColorConfirm"
        bindcancel="cancelColorPicker"
      ></color-picker>
    </view>
  </view>
</view>

<!-- 主标题编辑器弹窗 -->
<view class="title-editor-modal" wx:if="{{showTitleEditor}}">
  <view class="title-editor-container">
    <view class="title-editor-header">
      <view class="title-editor-title">编辑主标题</view>
      <view class="title-editor-close" bindtap="cancelTitleEdit">×</view>
    </view>

    <view class="title-editor-content">
      <view class="title-input-section">
        <input
          class="title-input"
          placeholder="请输入主标题"
          value="{{tempTitle}}"
          bindinput="onTitleInput"
          bindconfirm="confirmTitle"
          maxlength="20"
        />
      </view>
    </view>

    <view class="title-editor-actions">
      <button class="picker-btn cancel" bindtap="cancelTitleEdit">取消</button>
      <button class="picker-btn confirm" bindtap="confirmTitle">确定</button>
    </view>
  </view>
</view>

<!-- 副标题编辑器弹窗 -->
<view class="title-editor-modal" wx:if="{{showSubTitleEditor}}">
  <view class="title-editor-container">
    <view class="title-editor-header">
      <view class="title-editor-title">编辑副标题</view>
      <view class="title-editor-close" bindtap="cancelSubTitleEdit">×</view>
    </view>

    <view class="title-editor-content">
      <view class="title-input-section">
        <input
          class="title-input"
          placeholder="请输入副标题"
          value="{{tempSubTitle}}"
          bindinput="onSubTitleInput"
          bindconfirm="confirmSubTitle"
          maxlength="30"
        />
      </view>
    </view>

    <view class="title-editor-actions">
      <button class="picker-btn cancel" bindtap="cancelSubTitleEdit">取消</button>
      <button class="picker-btn confirm" bindtap="confirmSubTitle">确定</button>
    </view>
  </view>
</view>

<!-- 上半段背景颜色选择器弹窗 -->
<view class="color-picker-modal" wx:if="{{showTopBgColorPicker}}">
  <view class="color-picker-container">
    <view class="color-picker-header">
      <view class="color-picker-title">编辑上半段背景颜色</view>
      <view class="color-picker-close" bindtap="cancelTopBgColorEdit">×</view>
    </view>

    <view class="color-picker-content">
      <!-- 用时注入颜色选择器组件 -->
      <color-picker
        wx:if="{{showTopBgColorPicker}}"
        color="{{tempTopBgColor}}"
        bindchange="onTopBgColorChange"
        bindconfirm="onTopBgColorConfirm"
        bindcancel="cancelTopBgColorEdit"
      ></color-picker>
    </view>
  </view>
</view>

<!-- 下半段背景颜色选择器弹窗 -->
<view class="color-picker-modal" wx:if="{{showBottomBgColorPicker}}">
  <view class="color-picker-container">
    <view class="color-picker-header">
      <view class="color-picker-title">编辑下半段背景颜色</view>
      <view class="color-picker-close" bindtap="cancelBottomBgColorEdit">×</view>
    </view>

    <view class="color-picker-content">
      <!-- 用时注入颜色选择器组件 -->
      <color-picker
        wx:if="{{showBottomBgColorPicker}}"
        color="{{tempBottomBgColor}}"
        bindchange="onBottomBgColorChange"
        bindconfirm="onBottomBgColorConfirm"
        bindcancel="cancelBottomBgColorEdit"
      ></color-picker>
    </view>
  </view>
</view>

<!-- 背景颜色选择器弹窗（102模板） -->
<view class="color-picker-modal" wx:if="{{showBgColorPicker}}">
  <view class="color-picker-container">
    <view class="color-picker-header">
      <view class="color-picker-title">编辑背景颜色</view>
      <view class="color-picker-close" bindtap="cancelBgColorEdit">×</view>
    </view>

    <view class="color-picker-content">
      <!-- 用时注入颜色选择器组件 -->
      <color-picker
        wx:if="{{showBgColorPicker}}"
        color="{{tempBgColor}}"
        bindchange="onBgColorChange"
        bindconfirm="onBgColorConfirm"
        bindcancel="cancelBgColorEdit"
      ></color-picker>
    </view>
  </view>
</view>

<!-- 字体颜色选择器弹窗（101模板） -->
<view class="color-picker-modal" wx:if="{{showFontColorPicker}}">
  <view class="color-picker-container">
    <view class="color-picker-header">
      <view class="color-picker-title">编辑字体颜色</view>
      <view class="color-picker-close" bindtap="cancelFontColorEdit">×</view>
    </view>

    <view class="color-picker-content">
      <!-- 用时注入颜色选择器组件 -->
      <color-picker
        wx:if="{{showFontColorPicker}}"
        color="{{tempFontColor}}"
        bindchange="onFontColorChange"
        bindconfirm="onFontColorConfirm"
        bindcancel="cancelFontColorEdit"
      ></color-picker>
    </view>
  </view>
</view>

<!-- 标题颜色选择器弹窗（102模板） -->
<view class="color-picker-modal" wx:if="{{showTitleColorPicker}}">
  <view class="color-picker-container">
    <view class="color-picker-header">
      <view class="color-picker-title">编辑标题颜色</view>
      <view class="color-picker-close" bindtap="cancelTitleColorEdit">×</view>
    </view>

    <view class="color-picker-content">
      <!-- 用时注入颜色选择器组件 -->
      <color-picker
        wx:if="{{showTitleColorPicker}}"
        color="{{tempTitleColor}}"
        bindchange="onTitleColorChange"
        bindconfirm="onTitleColorConfirm"
        bindcancel="cancelTitleColorEdit"
      ></color-picker>
    </view>
  </view>
</view>

