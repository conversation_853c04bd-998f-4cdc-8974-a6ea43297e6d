/**
 * 安全的页面跳转工具
 * 解决 routeDone 错误的简单方案
 * 优先推荐使用 navigator 组件，JavaScript API 作为备选方案
 */
const logUtils = require('./logUtils');

/**
 * 安全的页面跳转
 * @param {Object} options - 跳转选项
 * @param {string} options.url - 跳转URL
 * @param {string} options.method - 跳转方法 (navigateTo|redirectTo|reLaunch|switchTab)
 * @param {Function} options.success - 成功回调
 * @param {Function} options.fail - 失败回调
 * @param {number} options.delay - 延迟时间（毫秒）
 */
function safeNavigate(options) {
  const {
    url,
    method = 'navigateTo',
    success,
    fail,
    delay = 100,
    ...otherOptions
  } = options;

  // 验证参数
  if (!url) {
    const error = new Error('URL is required');
    if (fail) fail(error);
    return;
  }

  // 延迟执行，避免 routeDone 错误
  setTimeout(() => {
    try {
      const wxMethod = wx[method];
      if (!wxMethod) {
        throw new Error(`Invalid navigation method: ${method}`);
      }

      // 构建wx方法的参数，包含所有传入的选项
      const wxOptions = {
        url,
        ...otherOptions,  // 直接展开其他选项
        success: (res) => {
          logUtils.log(`Navigation success: ${method} -> ${url}`);
          if (success) success(res);
        },
        fail: (err) => {
          logUtils.error(`Navigation failed: ${method} -> ${url}`, err);

          // 如果是 redirectTo 失败，尝试 navigateTo
          if (method === 'redirectTo') {
            logUtils.log('Retrying with navigateTo...');
            safeNavigate({
              url,
              method: 'navigateTo',
              success,
              fail,
              delay: 0 // 重试时不再延迟
            });
            return;
          }

          if (fail) fail(err);
        }
      };

      wxMethod(wxOptions);
    } catch (error) {
      logUtils.error('Navigation execution error:', error);
      if (fail) fail(error);
    }
  }, delay);
}

/**
 * 安全的返回上一页
 * @param {number} delta - 返回层数
 * @param {Function} success - 成功回调
 * @param {Function} fail - 失败回调
 */
function safeNavigateBack(delta = 1, success, fail) {
  setTimeout(() => {
    // 检查页面栈
    const pages = getCurrentPages();
    if (pages.length <= delta) {
      // 如果页面栈不够，跳转到首页
      safeNavigate({
        url: '/pages/index/index',
        method: 'reLaunch',
        success,
        fail
      });
      return;
    }

    wx.navigateBack({
      delta,
      success: (res) => {
        logUtils.log(`NavigateBack success: delta=${delta}`);
        if (success) success(res);
      },
      fail: (err) => {
        logUtils.error('NavigateBack failed:', err);
        // 失败时尝试跳转到首页
        safeNavigate({
          url: '/pages/index/index',
          method: 'reLaunch',
          success,
          fail
        });
      }
    });
  }, 100);
}

/**
 * 便捷的跳转方法
 */
const SafeNavigation = {
  /**
   * 跳转到指定页面
   */
  navigateTo(url, options = {}) {
    return safeNavigate({
      url,
      method: 'navigateTo',
      ...options
    });
  },

  /**
   * 重定向到指定页面
   */
  redirectTo(url, options = {}) {
    return safeNavigate({
      url,
      method: 'redirectTo',
      ...options
    });
  },

  /**
   * 重新启动到指定页面
   */
  reLaunch(url, options = {}) {
    return safeNavigate({
      url,
      method: 'reLaunch',
      ...options
    });
  },

  /**
   * 切换到 Tab 页面
   */
  switchTab(url, options = {}) {
    return safeNavigate({
      url,
      method: 'switchTab',
      ...options
    });
  },

  /**
   * 返回上一页
   */
  navigateBack(delta = 1, success, fail) {
    return safeNavigateBack(delta, success, fail);
  },

  /**
   * 返回首页
   */
  goHome(success, fail) {
    return this.reLaunch('/pages/index/index', { success, fail });
  },

  /**
   * 生成 navigator 组件的 URL
   * 用于在 WXML 中使用 navigator 组件时构建 URL
   * @param {string} basePath - 基础路径
   * @param {Object} params - 参数对象
   * @returns {string} 完整的 URL
   */
  buildNavigatorUrl(basePath, params = {}) {
    if (!basePath) {
      logUtils.error('buildNavigatorUrl: basePath is required');
      return '';
    }

    const queryString = Object.keys(params)
      .filter(key => params[key] !== undefined && params[key] !== null)
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&');

    return queryString ? `${basePath}?${queryString}` : basePath;
  },

  /**
   * 检查是否应该使用 navigator 组件
   * @param {Object} options - 跳转选项
   * @returns {boolean} 是否推荐使用 navigator 组件
   */
  shouldUseNavigator(options = {}) {
    const {
      hasComplexLogic = false,
      hasErrorHandling = false,
      hasAsyncOperation = false,
      hasConditionalLogic = false,
      urlLength = 0
    } = options;

    // 如果有复杂逻辑、错误处理、异步操作或条件逻辑，建议使用 JavaScript API
    if (hasComplexLogic || hasErrorHandling || hasAsyncOperation || hasConditionalLogic) {
      return false;
    }

    // 如果 URL 过长（超过 1000 字符），建议使用 JavaScript API 进行处理
    if (urlLength > 1000) {
      return false;
    }

    return true;
  }
};

module.exports = {
  safeNavigate,
  safeNavigateBack,
  SafeNavigation
};
