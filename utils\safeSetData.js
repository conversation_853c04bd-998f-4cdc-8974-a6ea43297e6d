/**
 * 安全的 setData 工具函数
 * 用于避免 "updateTextView fail: too early" 错误
 */
const logUtils = require('./logUtils');

/**
 * 安全的 setData 调用
 * @param {Object} context - 页面或组件的 this 上下文
 * @param {Object} data - 要设置的数据
 * @param {Function} callback - 可选的回调函数
 * @param {Number} delay - 延迟时间，默认为 0
 */
function safeSetData(context, data, callback, delay = 0) {
  if (!context || typeof context.setData !== 'function') {
    logUtils.warn('safeSetData: 无效的上下文对象');
    return;
  }

  const executeSetData = () => {
    try {
      // 检查页面是否已经销毁
      if (context.__destroyed || context.__isDestroyed) {
        logUtils.warn('safeSetData: 页面已销毁，跳过 setData');
        return;
      }

      context.setData(data, callback);
    } catch (error) {
      // 检查是否是微信小程序内部错误
      const errorMsg = error.message || error.toString();
      const isWxInternalError = errorMsg.includes('too early') ||
                               errorMsg.includes('too eayly') ||
                               errorMsg.includes('updateTextView') ||
                               errorMsg.includes('reportKeyValue') ||
                               errorMsg.includes('jsbridge') ||
                               errorMsg.includes('WAServiceMainContext');

      if (isWxInternalError) {
        // 对于微信内部错误，延迟重试
        logUtils.warn('safeSetData: 检测到微信内部错误，延迟重试:', errorMsg);
        setTimeout(() => {
          safeSetData(context, data, callback, 100);
        }, 100);
      } else {
        logUtils.error('safeSetData: setData 调用失败', error);
      }
    }
  };

  if (delay > 0) {
    setTimeout(executeSetData, delay);
  } else {
    // 使用 nextTick 确保在下一个事件循环中执行，并添加最小延迟
    if (wx.nextTick) {
      wx.nextTick(() => {
        setTimeout(executeSetData, 16); // 最小延迟一帧
      });
    } else {
      setTimeout(executeSetData, 16);
    }
  }
}

/**
 * 批量安全的 setData 调用
 * @param {Object} context - 页面或组件的 this 上下文
 * @param {Array} dataArray - 数据数组，每个元素包含 {data, callback, delay}
 */
function batchSafeSetData(context, dataArray) {
  if (!Array.isArray(dataArray)) {
    logUtils.warn('batchSafeSetData: dataArray 必须是数组');
    return;
  }

  dataArray.forEach((item, index) => {
    const { data, callback, delay = index * 10 } = item;
    safeSetData(context, data, callback, delay);
  });
}

/**
 * 节流版本的安全 setData
 * @param {Object} context - 页面或组件的 this 上下文
 * @param {Number} interval - 节流间隔，默认 16ms (约60fps)
 */
function createThrottledSafeSetData(context, interval = 16) {
  let lastTime = 0;
  let timer = null;
  let pendingData = {};

  return function(data, callback) {
    // 合并待处理的数据
    Object.assign(pendingData, data);

    const now = Date.now();
    const remaining = interval - (now - lastTime);

    if (remaining <= 0) {
      // 立即执行
      if (timer) {
        clearTimeout(timer);
        timer = null;
      }
      
      safeSetData(context, pendingData, callback);
      pendingData = {};
      lastTime = now;
    } else {
      // 延迟执行
      if (timer) {
        clearTimeout(timer);
      }
      
      timer = setTimeout(() => {
        safeSetData(context, pendingData, callback);
        pendingData = {};
        lastTime = Date.now();
        timer = null;
      }, remaining);
    }
  };
}

/**
 * 防抖版本的安全 setData
 * @param {Object} context - 页面或组件的 this 上下文
 * @param {Number} delay - 防抖延迟，默认 100ms
 */
function createDebouncedSafeSetData(context, delay = 100) {
  let timer = null;
  let pendingData = {};

  return function(data, callback) {
    // 合并待处理的数据
    Object.assign(pendingData, data);

    if (timer) {
      clearTimeout(timer);
    }

    timer = setTimeout(() => {
      safeSetData(context, pendingData, callback);
      pendingData = {};
      timer = null;
    }, delay);
  };
}

/**
 * 页面生命周期安全检查
 * @param {Object} context - 页面或组件的 this 上下文
 * @returns {Boolean} 是否可以安全执行 setData
 */
function canSafelySetData(context) {
  if (!context) return false;
  
  // 检查页面是否已销毁
  if (context.__destroyed || context.__isDestroyed) return false;
  
  // 检查是否有 setData 方法
  if (typeof context.setData !== 'function') return false;
  
  // 对于组件，检查是否已经 attached
  if (context.is && !context.__attached) return false;
  
  return true;
}

/**
 * 页面初始化安全 setData
 * 专门用于页面 onLoad 和组件 attached 生命周期
 * @param {Object} context - 页面或组件的 this 上下文
 * @param {Object} data - 要设置的数据
 * @param {Function} callback - 可选的回调函数
 */
function initSafeSetData(context, data, callback) {
  // 页面初始化时使用更长的延迟
  const initDelay = 100;

  const executeInit = () => {
    try {
      if (!canSafelySetData(context)) {
        logUtils.warn('initSafeSetData: 上下文不安全，跳过初始化');
        return;
      }

      context.setData(data, callback);
    } catch (error) {
      const errorMsg = error.message || error.toString();
      const isWxInternalError = errorMsg.includes('too early') ||
                               errorMsg.includes('too eayly') ||
                               errorMsg.includes('updateTextView') ||
                               errorMsg.includes('reportKeyValue') ||
                               errorMsg.includes('jsbridge');

      if (isWxInternalError) {
        // 对于初始化错误，使用更长的延迟重试
        logUtils.warn('initSafeSetData: 初始化时检测到微信内部错误，延迟重试');
        setTimeout(() => {
          initSafeSetData(context, data, callback);
        }, 200);
      } else {
        logUtils.error('initSafeSetData: 初始化失败', error);
      }
    }
  };

  // 使用双重延迟确保安全
  setTimeout(() => {
    if (wx.nextTick) {
      wx.nextTick(executeInit);
    } else {
      setTimeout(executeInit, 16);
    }
  }, initDelay);
}

module.exports = {
  safeSetData,
  batchSafeSetData,
  createThrottledSafeSetData,
  createDebouncedSafeSetData,
  canSafelySetData,
  initSafeSetData
};
