// 幸运签卡片组件
const { isWeChatInternalError, safeSetData, safeCanvasOperation } = require('../../utils/luckyCardErrorHandler.js');

Component({
  properties: {
    luckyData: {
      type: Object,
      value: {},
      observer: function(newVal, oldVal) {
        // 检查组件是否已销毁
        if (this._isDestroyed) {
          console.warn('组件已销毁，忽略数据更新');
          return;
        }

        // 数据验证
        if (newVal && typeof newVal === 'object') {
          console.log('🔍 luckyData changed:', {
            colorName: newVal.colorName,
            title: newVal.title,
            fortune: newVal.fortune,
            contentPreview: newVal.content ? newVal.content.substring(0, 50) + '...' : 'null',
            hasInterpretation: !!(newVal.interpretation && newVal.interpretation.content)
          });

          // 确保数据完整性
          if (!newVal.colorName || !newVal.title) {
            console.warn('⚠️ luckyData缺少必要字段:', newVal);
          }

          // 验证签文内容是否正确
          if (newVal.content) {
            if (newVal.content.includes('【解签】') || newVal.content.includes('【姻缘】')) {
              console.error('❌ 检测到错误的数据结构：content字段包含解读内容');
              console.error('错误的content:', newVal.content.substring(0, 100));
              return;
            }
          }

          // 安全地格式化签文内容
          try {
            if (newVal.content) {
              this.formatContent(newVal.content);
            } else {
              console.warn('⚠️ luckyData.content为空');
            }
          } catch (error) {
            console.error('格式化内容失败:', error);
          }

          // 如果是新数据且组件已经准备好，重新初始化显示
          if (oldVal && JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
            console.log('检测到新的luckyData，重新初始化组件');
            // 延迟执行，确保组件状态稳定
            setTimeout(() => {
              if (!this._isDestroyed) {
                this.resetAndShow();
              }
            }, 100);
          }
        } else {
          console.warn('Invalid luckyData:', newVal);
        }
      }
    }
  },

  data: {
    flipped: false,
    currentDate: '',
    isAnimating: false, // 防止动画期间重复点击
    isVisible: false, // 控制整体可见性
    showButtons: false, // 控制按钮显示
    isEntering: false, // 控制入场动画状态
    formattedContent: '', // 格式化后的签文内容
    shareImagePath: '' // 分享图片路径
  },

  lifetimes: {
    attached() {
      console.log('=== luckyCard组件attached ===');
      this._isDestroyed = false;
      this._isReady = false;
      this.setCurrentDate();
      this.initCard();
    },

    ready() {
      console.log('=== luckyCard组件ready ===');
      this._isReady = true;

      // 运行数据诊断
      if (this.properties.luckyData) {
        const isDataValid = this.debugLuckyData();

        if (isDataValid && this.properties.luckyData.content) {
          try {
            this.formatContent(this.properties.luckyData.content);
          } catch (error) {
            console.error('初始化格式化内容失败:', error);
          }
        } else {
          console.warn('⚠️ 数据验证失败，使用默认内容');
          this.safeSetData({ formattedContent: '两眼盯铜板，一心钻钱眼。\n朝朝算收益，夜夜梦财源。' });
        }
      } else {
        console.warn('⚠️ 组件初始化时luckyData为空');
      }

      // 延迟启动动画，确保组件完全渲染
      setTimeout(() => {
        if (!this._isDestroyed && this._isReady) {
          this.startEnterAnimation();
        }
      }, 100);
    },

    detached() {
      console.log('=== luckyCard组件detached ===');
      this._isDestroyed = true;
      this._isReady = false;

      // 清理所有可能的定时器
      const timers = [
        '_animationTimer',
        '_enterAnimationTimer',
        '_buttonTimer',
        '_canvasTimer',
        '_exitTimer',
        '_flipTimer'
      ];

      timers.forEach(timerName => {
        if (this[timerName]) {
          clearTimeout(this[timerName]);
          this[timerName] = null;
        }
      });

      // 清理Canvas相关资源
      this.cleanupCanvas();
    }
  },

  methods: {
    // 安全的setData方法（使用工具函数）
    safeSetData(data, callback) {
      safeSetData(this, data, callback);
    },

    // 调试工具：验证和诊断数据
    debugLuckyData() {
      const luckyData = this.properties.luckyData;
      console.log('🔍 === 幸运签数据诊断 ===');
      console.log('完整数据:', luckyData);

      if (!luckyData) {
        console.error('❌ luckyData为空');
        return false;
      }

      console.log('📋 数据字段检查:');
      console.log('- colorName:', luckyData.colorName);
      console.log('- title:', luckyData.title);
      console.log('- fortune:', luckyData.fortune);
      console.log('- color:', luckyData.color);
      console.log('- content:', luckyData.content);
      console.log('- interpretation:', luckyData.interpretation);

      // 检查content字段
      if (luckyData.content) {
        console.log('📝 签文内容分析:');
        console.log('- 长度:', luckyData.content.length);
        console.log('- 前100字符:', luckyData.content.substring(0, 100));

        if (luckyData.content.includes('【解签】')) {
          console.error('❌ content字段包含【解签】标记，这是错误的！');
          return false;
        }
        if (luckyData.content.includes('【姻缘】')) {
          console.error('❌ content字段包含【姻缘】标记，这是错误的！');
          return false;
        }
        if (luckyData.content.includes('【事业】') || luckyData.content.includes('【工作】')) {
          console.error('❌ content字段包含【事业】或【工作】标记，这是错误的！');
          return false;
        }

        console.log('✅ content字段内容正确');
      } else {
        console.error('❌ content字段为空');
        return false;
      }

      // 检查interpretation字段
      if (luckyData.interpretation && luckyData.interpretation.content) {
        console.log('📖 解读内容分析:');
        console.log('- 长度:', luckyData.interpretation.content.length);
        console.log('- 前100字符:', luckyData.interpretation.content.substring(0, 100));
        console.log('✅ interpretation字段存在且有内容');
      } else {
        console.warn('⚠️ interpretation字段缺失或为空');
      }

      console.log('✅ === 数据诊断完成 ===');
      return true;
    },

    // 清理Canvas资源
    cleanupCanvas() {
      try {
        // 清理可能的Canvas引用
        if (this._canvasContext) {
          this._canvasContext = null;
        }
        // 清理可能的定时器
        if (this._canvasTimer) {
          clearTimeout(this._canvasTimer);
          this._canvasTimer = null;
        }
      } catch (error) {
        console.error('清理Canvas资源失败:', error);
      }
    },

    // 格式化签文内容，按逗号和句号分行
    formatContent(content) {
      if (this._isDestroyed) return;

      if (!content) {
        this.safeSetData({ formattedContent: '' });
        return;
      }

      try {
        // 检查是否错误传入了解读内容
        if (content.includes('【解签】') || content.includes('【姻缘】') || content.includes('【事业】') || content.includes('【工作】')) {
          console.error('⚠️ 检测到错误的内容类型：传入了解读内容而不是签文内容');
          console.error('错误内容:', content.substring(0, 100) + '...');

          // 尝试从当前的 luckyData 中获取正确的签文内容
          if (this.properties.luckyData && this.properties.luckyData.content) {
            const correctContent = this.properties.luckyData.content;
            console.log('使用正确的签文内容:', correctContent);
            this.formatContent(correctContent);
            return;
          } else {
            // 如果无法获取正确内容，使用默认内容
            console.warn('无法获取正确的签文内容，使用默认内容');
            this.safeSetData({ formattedContent: '两眼盯铜板，一心钻钱眼。\n朝朝算收益，夜夜梦财源。' });
            return;
          }
        }

        // 将签文按逗号和句号分割，每句话独立一行
        // 先替换现有的\n为空格，因为我们要重新分行
        let formatted = content.replace(/\n/g, ' ');

        // 按中文逗号和句号分割，并在每个标点后添加换行
        formatted = formatted.replace(/([，。])/g, '$1\n');

        // 清理多余的空格和空行
        formatted = formatted.replace(/\s+\n/g, '\n').replace(/\n\s+/g, '\n').trim();

        console.log('✅ 正确的签文内容:', content);
        console.log('✅ 格式化后的签文:', formatted);
        this.safeSetData({ formattedContent: formatted });
      } catch (error) {
        console.error('格式化内容失败:', error);
        this.safeSetData({ formattedContent: content || '' });
      }
    },

    // 设置当前日期
    setCurrentDate() {
      if (this._isDestroyed) return;

      try {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');

        this.safeSetData({
          currentDate: `${year}.${month}.${day}`
        });
      } catch (error) {
        console.error('设置日期失败:', error);
      }
    },

    // 初始化卡片
    initCard() {
      if (this._isDestroyed) return;

      console.log('初始化卡片状态');
      this.safeSetData({
        flipped: false,
        isAnimating: false,
        showButtons: false,
        isVisible: false, // 初始化时隐藏，等待动画开始
        isEntering: false // 重置入场动画状态
      });
    },

    // 重置并显示卡片（用于重复显示）
    resetAndShow() {
      if (this._isDestroyed) return;

      console.log('重置并显示卡片');
      this.initCard();
      this.setCurrentDate();

      // 清理之前的定时器
      if (this._animationTimer) {
        clearTimeout(this._animationTimer);
      }

      // 短暂延迟后开始动画，确保状态重置完成
      this._animationTimer = setTimeout(() => {
        if (!this._isDestroyed) {
          this.startEnterAnimation();
        }
      }, 100);
    },

    // 开始入场动画
    startEnterAnimation() {
      if (this._isDestroyed) return;

      console.log('开始卡片入场动画');

      // 立即显示卡片并开始入场动画
      this.safeSetData({
        isVisible: true,
        isEntering: true
      });

      // 清理之前的定时器
      if (this._enterAnimationTimer) {
        clearTimeout(this._enterAnimationTimer);
      }
      if (this._buttonTimer) {
        clearTimeout(this._buttonTimer);
      }

      // 1.5秒后结束入场动画状态
      this._enterAnimationTimer = setTimeout(() => {
        if (!this._isDestroyed) {
          this.safeSetData({
            isEntering: false
          });
          console.log('入场动画完成');
        }
      }, 1500);

      // 延迟显示按钮，给卡片动画时间
      this._buttonTimer = setTimeout(() => {
        if (!this._isDestroyed) {
          this.safeSetData({
            showButtons: true
          });
          console.log('按钮显示完成');
        }
      }, 1600); // 1.6秒后显示按钮，确保翻转动画完成
    },

    // 开始退场动画
    startExitAnimation() {
      if (this._isDestroyed) return;

      console.log('开始卡片退场动画');

      // 防止重复触发
      if (this.data.isAnimating) {
        console.log('退场动画被阻止：正在动画中');
        return;
      }

      this.safeSetData({
        isAnimating: true,
        showButtons: false
      });

      // 清理之前的退场定时器
      if (this._exitTimer) {
        clearTimeout(this._exitTimer);
      }

      // 延迟隐藏卡片
      this._exitTimer = setTimeout(() => {
        if (this._isDestroyed) return;

        this.safeSetData({
          isVisible: false
        });

        // 动画完成后触发关闭事件并重置状态
        this._exitTimer = setTimeout(() => {
          if (this._isDestroyed) return;

          console.log('退场动画完成，重置状态并触发关闭事件');
          // 重置所有状态为初始值
          this.safeSetData({
            flipped: false,
            isAnimating: false,
            isVisible: false,
            showButtons: false,
            isEntering: false
          });

          // 触发关闭事件，通知父组件
          this.triggerEvent('close');
          console.log('关闭事件已触发');
        }, 300);
      }, 100);
    },

    // 翻转卡片
    flipCard() {
      if (this._isDestroyed) return;

      // 防止动画期间重复点击
      if (this.data.isAnimating) {
        console.log('翻转被阻止：正在动画中');
        return;
      }

      console.log('开始翻转，当前状态：', this.data.flipped);

      this.safeSetData({
        isAnimating: true,
        flipped: !this.data.flipped
      });

      // 清理之前的翻转定时器
      if (this._flipTimer) {
        clearTimeout(this._flipTimer);
      }

      // 动画完成后重置状态
      this._flipTimer = setTimeout(() => {
        if (!this._isDestroyed) {
          this.safeSetData({ isAnimating: false });
          console.log('翻转完成，新状态：', this.data.flipped);
        }
      }, 600); // 与CSS动画时间一致

      // 触发翻转事件，通知父组件
      this.triggerEvent('flip', { flipped: !this.data.flipped });
    },

    // 关闭卡片
    closeCard() {
      console.log('开始关闭卡片');
      this.startExitAnimation();
    },

    // 强制显示卡片（用于测试和调试）
    forceShow() {
      console.log('🔧 强制显示卡片');

      // 运行数据诊断
      if (this.properties.luckyData) {
        this.debugLuckyData();
      }

      // 重置并显示
      this.resetAndShow();
    },

    // 生成分享图片 - 临时禁用以避免 _getData 错误
    async generateShareImage() {
      // 临时禁用Canvas分享图片生成功能，避免_getData错误
      console.log('分享图片生成功能已临时禁用，避免Canvas相关的_getData错误');
      return null;

      // 以下代码被注释掉，避免Canvas操作导致的_getData错误
      /*
      // 检查组件状态
      if (this._isDestroyed) {
        console.warn('组件已销毁，取消生成分享图片');
        return null;
      }

      const { luckyData, formattedContent, currentDate } = this.data;

      if (!luckyData) {
        console.warn('生成分享图片失败：缺少数据');
        return null;
      }

      try {
        console.log('开始生成幸运签分享图片，数据:', {
          colorName: luckyData.colorName,
          title: luckyData.title,
          fortune: luckyData.fortune,
          formattedContent: formattedContent
        });

        // 增加更长的延迟，确保Canvas完全准备好
        await new Promise(resolve => setTimeout(resolve, 300));

        // 使用更安全的Canvas获取方式，增加组件状态检查
        const canvasRes = await this.getCanvasWithRetry('#luckyShareCanvas', 5, 800);

        // 再次检查组件状态
        if (this._isDestroyed) {
          console.warn('组件在Canvas获取过程中被销毁');
          return null;
        }

        if (!canvasRes || !canvasRes.node) {
          console.error('获取Canvas节点失败');
          return null;
        }

        */
    },

    // 带重试机制的Canvas获取方法 - 临时禁用
    getCanvasWithRetry(selector, maxRetries = 3, retryInterval = 500) {
      // 临时禁用Canvas获取功能，避免_getData错误
      console.log('Canvas获取功能已临时禁用，避免_getData错误');
      return Promise.resolve(null);

      /*
      return new Promise((resolve) => {
        let retryCount = 0;

        const tryGetCanvas = () => {
          // 检查组件状态
          if (this._isDestroyed) {
            console.warn('组件已销毁，停止Canvas获取');
            resolve(null);
            return;
          }

          retryCount++;

          try {
            // 使用更安全的查询方式
            let query;

            // 确保查询在正确的组件上下文中执行
            if (this && typeof this.createSelectorQuery === 'function') {
              query = this.createSelectorQuery();
            } else {
              query = wx.createSelectorQuery().in(this);
            }

            // 添加额外的安全检查
            if (!query || typeof query.select !== 'function') {
              console.error('SelectorQuery 创建失败');
              if (retryCount >= maxRetries) {
                resolve(null);
              } else {
                this._canvasTimer = setTimeout(tryGetCanvas, retryInterval);
              }
              return;
            }

            query.select(selector)
              .fields({ node: true, size: true })
              .exec((res) => {
                // 再次检查组件状态
                if (this._isDestroyed) {
                  console.warn('组件在Canvas查询回调中被销毁');
                  resolve(null);
                  return;
                }

                if (res && res[0] && res[0].node) {
                  // Canvas节点获取成功，验证上下文
                  try {
                    const node = res[0].node;

                    // 验证节点有效性
                    if (!node || typeof node.getContext !== 'function') {
                      console.log(`Canvas节点无效，第 ${retryCount} 次重试`);
                      if (retryCount >= maxRetries) {
                        console.warn(`Canvas节点无效，已达最大重试次数:`, maxRetries);
                        resolve(null);
                      } else {
                        this._canvasTimer = setTimeout(tryGetCanvas, retryInterval);
                      }
                      return;
                    }

                    const ctx = node.getContext('2d');

                    if (ctx && typeof ctx.clearRect === 'function') {
                      console.log(`Canvas节点获取成功，重试次数:`, retryCount);
                      resolve(res[0]);
                    } else {
                      console.log(`Canvas上下文未准备好，第 ${retryCount} 次重试`);
                      if (retryCount >= maxRetries) {
                        console.warn(`Canvas上下文获取失败，已达最大重试次数:`, maxRetries);
                        resolve(null);
                      } else {
                        this._canvasTimer = setTimeout(tryGetCanvas, retryInterval);
                      }
                    }
                  } catch (ctxError) {
                    console.log(`Canvas上下文获取异常，第 ${retryCount} 次重试:`, ctxError);

                    // 检查是否是_getData相关错误或其他微信内部错误
                    if (ctxError.message && (
                      ctxError.message.includes('_getData') ||
                      ctxError.message.includes('webviewScriptError') ||
                      ctxError.message.includes('SystemError') ||
                      ctxError.message.includes('too early')
                    )) {
                      console.warn('检测到微信内部错误，停止重试:', ctxError.message);
                      resolve(null);
                      return;
                    }

                    if (retryCount >= maxRetries) {
                      console.warn(`Canvas上下文异常，已达最大重试次数:`, maxRetries);
                      resolve(null);
                    } else {
                      this._canvasTimer = setTimeout(tryGetCanvas, retryInterval);
                    }
                  }
                } else if (retryCount >= maxRetries) {
                  // 达到最大重试次数，返回null
                  console.warn(`Canvas节点获取失败，已达最大重试次数:`, maxRetries);
                  resolve(null);
                } else {
                  // 继续重试
                  console.log(`Canvas节点获取失败，第 ${retryCount} 次重试`);
                  this._canvasTimer = setTimeout(tryGetCanvas, retryInterval);
                }
              });
          } catch (queryError) {
            console.error('Canvas查询异常:', queryError);

            // 检查是否是微信内部错误
            if (queryError.message && (
              queryError.message.includes('_getData') ||
              queryError.message.includes('webviewScriptError') ||
              queryError.message.includes('SystemError') ||
              queryError.message.includes('too early') ||
              queryError.message.includes('createSelectorQuery')
            )) {
              console.warn('检测到微信内部错误，停止重试:', queryError.message);
              resolve(null);
              return;
            }

            if (retryCount >= maxRetries) {
              console.warn(`Canvas查询失败，已达最大重试次数:`, maxRetries);
              resolve(null);
            } else {
              this._canvasTimer = setTimeout(tryGetCanvas, retryInterval);
            }
          }
        };

        // 增加初始延迟，确保DOM稳定和组件完全初始化
        const initialDelay = maxRetries > 3 ? 500 : 200; // 如果重试次数多，增加初始延迟
        this._canvasTimer = setTimeout(() => {
          if (!this._isDestroyed) {
            console.log(`开始尝试获取Canvas节点: ${selector}，最大重试次数: ${maxRetries}`);
            tryGetCanvas();
          } else {
            console.warn('组件已销毁，取消Canvas获取');
            resolve(null);
          }
        }, initialDelay);
      */
    },

    // 绘制幸运签正面 - 临时禁用
    async drawLuckyCardFront(ctx, width, height, luckyData, content, date) {
      // 临时禁用Canvas绘制功能，避免_getData错误
      console.log('Canvas绘制功能已临时禁用，避免_getData错误');
      return;

      /*
      try {
        console.log('开始绘制幸运签正面，数据:', {
          width, height,
          colorName: luckyData.colorName,
          title: luckyData.title,
          content: content
        });

        // 验证Canvas上下文
        if (!ctx || typeof ctx.clearRect !== 'function') {
          throw new Error('Canvas上下文无效');
        }

        // 完全清除画布
        try {
          ctx.clearRect(0, 0, width, height);
        } catch (clearError) {
          console.error('清除画布失败:', clearError);
          throw clearError;
        }

        // 设置白色背景
        try {
          ctx.fillStyle = '#FFFFFF';
          ctx.fillRect(0, 0, width, height);
        } catch (bgError) {
          console.error('绘制背景失败:', bgError);
          throw bgError;
        }

        // 绘制边框
        try {
          ctx.strokeStyle = luckyData.color || '#F7D000';
          ctx.lineWidth = 4;
          ctx.strokeRect(10, 10, width - 20, height - 20);
        } catch (borderError) {
          console.error('绘制边框失败:', borderError);
          // 边框绘制失败不影响整体，继续执行
        }

        // 绘制左上角颜色方块
        try {
          ctx.fillStyle = luckyData.color || '#F7D000';
          ctx.fillRect(30, 30, 48, 48);
        } catch (squareError) {
          console.error('绘制颜色方块失败:', squareError);
          // 继续执行
        }

        // 绘制运势文字
        try {
          ctx.fillStyle = '#000000';
          ctx.font = 'bold 36px sans-serif';
          ctx.textAlign = 'left';
          ctx.textBaseline = 'top';
          ctx.fillText(luckyData.fortune || '大吉', 30, 90);
        } catch (fortuneError) {
          console.error('绘制运势文字失败:', fortuneError);
          // 继续执行
        }

        // 绘制日期
        try {
          ctx.fillStyle = '#999999';
          ctx.font = '18px sans-serif';
          ctx.fillText(date, 30, 140);
        } catch (dateError) {
          console.error('绘制日期失败:', dateError);
          // 继续执行
        }

        // 绘制右侧颜色名称（竖排）
        try {
          ctx.fillStyle = luckyData.color || '#F7D000';
          ctx.font = 'bold 48px sans-serif';
          ctx.textAlign = 'center';
          const colorName = luckyData.colorName || '不焦绿';
          for (let i = 0; i < colorName.length; i++) {
            ctx.fillText(colorName[i], width - 50, 60 + i * 60);
          }
        } catch (colorNameError) {
          console.error('绘制颜色名称失败:', colorNameError);
          // 继续执行
        }

        // 绘制签文标题
        try {
          ctx.fillStyle = '#000000';
          ctx.font = '24px sans-serif';
          ctx.textAlign = 'center';
          const title = `【${luckyData.title || '搞钱神旺签'}】`;
          ctx.fillText(title, width / 2, 250);
        } catch (titleError) {
          console.error('绘制签文标题失败:', titleError);
          // 继续执行
        }

        // 绘制签文内容（使用格式化后的内容）
        try {
          ctx.font = '22px sans-serif';
          ctx.textAlign = 'center';

          // 确保使用正确的签文内容，避免背面内容干扰
          const signContent = content || luckyData.content || '';
          console.log('绘制的签文内容:', signContent);

          // 按换行符分割内容
          const lines = signContent.split('\n').filter(line => line.trim());
          lines.forEach((line, index) => {
            if (line.trim()) {
              try {
                ctx.fillText(line.trim(), width / 2, 290 + index * 35);
              } catch (lineError) {
                console.error(`绘制签文第${index}行失败:`, lineError);
              }
            }
          });
        } catch (contentError) {
          console.error('绘制签文内容失败:', contentError);
          // 继续执行
        }

        // 绘制底部色值信息
        try {
          ctx.fillStyle = '#666666';
          ctx.font = '16px sans-serif';
          ctx.textAlign = 'left';
          ctx.fillText('RGB', 40, height - 80);
          ctx.fillText(luckyData.rgb || '247, 208, 0', 80, height - 80);
          ctx.fillText('HEX', 40, height - 50);
          ctx.fillText(luckyData.hex || '#F7D000', 80, height - 50);
        } catch (colorInfoError) {
          console.error('绘制色值信息失败:', colorInfoError);
          // 继续执行
        }

        // 绘制底部提示
        try {
          ctx.fillStyle = '#999999';
          ctx.font = '14px sans-serif';
          ctx.textAlign = 'center';
          const tipText = '每日一签，仅供娱乐';
          ctx.fillText(tipText, width / 2, height - 20);
        } catch (tipError) {
          console.error('绘制底部提示失败:', tipError);
          // 继续执行
        }

        console.log('幸运签正面绘制完成');

      } catch (error) {
        console.error('绘制幸运签正面失败:', error);
        throw error;
      }
      */
    },

    // 公共方法：显示卡片
    showCard() {
      if (this._isDestroyed) {
        console.warn('组件已销毁，无法显示卡片');
        return;
      }
      console.log('公共方法：显示卡片被调用');
      this.resetAndShow();
    },

    // 公共方法：强制重新显示卡片
    forceShow() {
      if (this._isDestroyed) {
        console.warn('组件已销毁，无法强制显示卡片');
        return;
      }
      console.log('强制重新显示卡片');
      this.resetAndShow();
    }
  }
});
