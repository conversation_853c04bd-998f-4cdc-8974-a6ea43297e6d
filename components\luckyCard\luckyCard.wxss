/* 幸运签卡片容器 */
.lucky-card-container {
  position: relative;
  width: 540rpx;
  height: 960rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: scale(0.3) translateY(30rpx);
  transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.lucky-card-container.visible {
  opacity: 1;
  transform: scale(1) translateY(-80rpx);
}

/* 卡片入场动画状态 */
.lucky-card-container.entering {
  animation: cardEnterWithFlip 1.5s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

/* 卡片光芒效果 */
.card-glow {
  position: absolute;
  width: 600rpx;
  height: 1000rpx;
  background: radial-gradient(ellipse, rgba(247, 208, 0, 0.2) 0%, transparent 70%);
  opacity: 0;
  animation: glowAppear 1s ease-out 0.2s forwards, glowPulse 3s infinite ease-in-out 1.2s;
}

/* 幸运签卡片样式 - 简化版本 */
.lucky-card {
  position: relative;
  width: 540rpx;
  height: 960rpx;
  background: #FFFFFF;
  border: 12rpx solid #F7D000;
  border-radius: 24rpx;
  transform-style: preserve-3d;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  opacity: 1;
  transform: rotateY(0deg);
}

.lucky-card.flipped {
  transform: rotateY(180deg);
}

/* 确保翻转状态下的显示控制 - 使用更强的控制 */
.lucky-card:not(.flipped) .card-front {
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
  z-index: 2;
}

.lucky-card:not(.flipped) .card-back {
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
  z-index: 1;
}

.lucky-card.flipped .card-front {
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
  z-index: 1;
}

.lucky-card.flipped .card-back {
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
  z-index: 2;
}

/* 卡片入场翻转动画 */
.lucky-card.entering {
  animation: cardFlipOnEnter 1.5s ease-out forwards;
}

/* 关闭按钮 - 优化位置 */
.close-btn {
  position: absolute;
  top: -80rpx;
  right: -60rpx;
  width: 64rpx;
  height: 64rpx;
  background: #FFFFFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666666;
  z-index: 50;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  border: 2rpx solid #F0F0F0;
  opacity: 0;
  transform: scale(0);
  animation: buttonAppear 0.4s ease-out 0.8s forwards;
}

/* 卡片正面 */
.card-front {
  position: absolute;
  width: 100%;
  height: 100%;
  padding: 0;
  box-sizing: border-box;
  backface-visibility: hidden;
  transform: rotateY(0deg);
  z-index: 2;
  background-color: white;
  border-radius: 12rpx;
}

/* 卡片背面 */
.card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  padding: 40rpx 30rpx 80rpx 30rpx;
  box-sizing: border-box;
  backface-visibility: hidden;
  transform: rotateY(180deg);
  display: flex;
  flex-direction: column;
  z-index: 1;
  background-color: white; /* 确保有背景色 */
  border-radius: 12rpx;
}

/* 防止内容重叠的额外保护 */
.card-front {
  overflow: hidden;
}

.card-back {
  overflow: hidden;
}

/* 左上角黄色方块 */
.yellow-square {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  width: 48rpx;
  height: 48rpx;
  background-color: #F7D000;
}

/* 左上角运势文字 */
.fortune-text-left {
  position: absolute;
  top: 92rpx;
  left: 20rpx;
  font-size: 48rpx;
  font-weight: bold;
  color: #000000;
}

/* 日期文字 */
.date-text {
  position: absolute;
  top: 164rpx;
  left: 20rpx;
  font-size: 24rpx;
  color: #999999;
}

/* 右侧竖排颜色名称 */
.color-name-vertical {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  font-size: 60rpx;
  font-weight: bold;
  line-height: 1.1;
  writing-mode: vertical-rl;
  text-orientation: upright;
  letter-spacing: 4rpx;
  height: 300rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 中间签文区域 */
.fortune-content-center {
  position: absolute;
  top: 320rpx;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  width: 380rpx;
  padding: 0 20rpx;
}

.fortune-title {
  font-size: 32rpx;
  color: #000000;
  margin-bottom: 24rpx;
  font-weight: normal;
}

.fortune-text-main {
  font-size: 42rpx;
  color: #000000;
  line-height: 1.8;
  white-space: pre-line;
  text-align: center;
  letter-spacing: 1rpx;
}

/* 底部色值信息 */
.color-values {
  position: absolute;
  bottom: 80rpx;
  left: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.color-row {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.color-label {
  font-size: 24rpx;
  color: #666666;
  font-weight: bold;
  width: 72rpx;
}

.color-value {
  font-size: 24rpx;
  color: #333333;
}

/* 底部提示文字 */
.bottom-tip {
  position: absolute;
  bottom: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  font-size: 16rpx;
  color: #999999;
  text-align: center;
}



/* 解读内容 */
.interpretation-content {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.3;
  white-space: pre-line;
  flex: 1;
  overflow-y: visible;
  padding-bottom: 60rpx;
}

/* 背面底部提示文字 */
.bottom-tip-back {
  position: absolute;
  bottom: 24rpx;
  left: 50%;
  transform: translateX(-50%);
  font-size: 18rpx;
  color: #999999;
  text-align: center;
}

/* 外部按钮区域 */
.external-buttons {
  position: absolute;
  top: 100%;
  left: 50%;
  margin-top: 40rpx;
  transform: translateX(-50%) translateY(20rpx);
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  align-items: center;
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.external-buttons.show {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}

.external-btn {
  width: 400rpx;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: 2rpx solid transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

.external-btn.primary {
  background: #F5F5F5;
  color: #333333;
  border: 2rpx solid transparent;
}

.external-btn.secondary {
  background: #F5F5F5;
  color: #333333;
  border: 2rpx solid transparent;
}

/* 按钮图标样式 */
.btn-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
  line-height: 1;
}

/* 按钮文字样式 */
.btn-text {
  font-size: 28rpx;
  line-height: 1;
}

/* 动画关键帧 - 重新设计 */
@keyframes glowAppear {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes glowPulse {
  0%, 100% {
    opacity: 0.2;
    transform: scale(1);
  }
  50% {
    opacity: 0.4;
    transform: scale(1.02);
  }
}

@keyframes buttonAppear {
  0% {
    opacity: 0;
    transform: scale(0);
  }
  60% {
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 卡片入场动画 - 缩放+翻转 */
@keyframes cardEnterWithFlip {
  0% {
    opacity: 0;
    transform: scale(0.3) translateY(30rpx);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(-80rpx);
  }
}

/* 卡片翻转动画 - 仅在入场时使用 */
@keyframes cardFlipOnEnter {
  0% {
    transform: rotateY(0deg);
  }
  16.67% {
    transform: rotateY(180deg);
  }
  33.33% {
    transform: rotateY(360deg);
  }
  50% {
    transform: rotateY(540deg);
  }
  66.67% {
    transform: rotateY(720deg);
  }
  83.33% {
    transform: rotateY(900deg);
  }
  100% {
    transform: rotateY(1080deg);
  }
}