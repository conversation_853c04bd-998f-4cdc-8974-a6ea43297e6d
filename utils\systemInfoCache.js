// utils/systemInfoCache.js - SystemInfo 缓存管理器
// 解决多次调用 getSystemInfo/getSystemInfoSync 接口的性能问题

class SystemInfoCache {
  constructor() {
    // 缓存的系统信息
    this.cache = {
      systemInfo: null,
      windowInfo: null,
      deviceInfo: null,
      appBaseInfo: null,
      systemSetting: null,
      appAuthorizeSetting: null,
      timestamp: 0
    };
    
    // 缓存有效期（毫秒）
    this.cacheExpireTime = 10 * 60 * 1000; // 10分钟
    
    // 性能统计
    this.stats = {
      cacheHits: 0,
      apiCalls: 0,
      syncCalls: 0,
      asyncCalls: 0
    };
    
    // 初始化状态
    this.initialized = false;
    this.initializing = false;
  }

  /**
   * 检查缓存是否有效
   * @returns {boolean}
   */
  isCacheValid() {
    return this.cache.timestamp > 0 && 
           (Date.now() - this.cache.timestamp) < this.cacheExpireTime;
  }

  /**
   * 异步初始化系统信息（推荐使用）
   * @returns {Promise<Object>}
   */
  async initSystemInfoAsync() {
    if (this.initialized && this.isCacheValid()) {
      this.stats.cacheHits++;
      console.log('[SystemInfoCache] 使用缓存的系统信息');
      return this.cache.systemInfo;
    }

    if (this.initializing) {
      // 如果正在初始化，等待完成
      return new Promise((resolve) => {
        const checkInterval = setInterval(() => {
          if (this.initialized) {
            clearInterval(checkInterval);
            resolve(this.cache.systemInfo);
          }
        }, 50);
      });
    }

    this.initializing = true;
    console.log('[SystemInfoCache] 异步初始化系统信息');

    try {
      this.stats.apiCalls++;
      this.stats.asyncCalls++;

      // 使用新的推荐 API 组合获取系统信息
      const [windowInfo, deviceInfo, appBaseInfo, systemSetting, appAuthorizeSetting] = await Promise.all([
        this.getWindowInfoAsync(),
        this.getDeviceInfoAsync(),
        this.getAppBaseInfoAsync(),
        this.getSystemSettingAsync(),
        this.getAppAuthorizeSettingAsync()
      ]);

      // 组合成完整的系统信息对象（保持向后兼容）
      const systemInfo = {
        ...windowInfo,
        ...deviceInfo,
        ...appBaseInfo,
        ...systemSetting,
        ...appAuthorizeSetting,
        // 保持向后兼容的字段映射
        windowWidth: windowInfo.windowWidth,
        windowHeight: windowInfo.windowHeight,
        screenWidth: windowInfo.screenWidth,
        screenHeight: windowInfo.screenHeight,
        statusBarHeight: windowInfo.statusBarHeight,
        safeArea: windowInfo.safeArea,
        platform: deviceInfo.platform,
        system: deviceInfo.system,
        pixelRatio: deviceInfo.pixelRatio,
        version: appBaseInfo.version,
        SDKVersion: appBaseInfo.SDKVersion
      };

      // 更新缓存
      this.cache = {
        systemInfo,
        windowInfo,
        deviceInfo,
        appBaseInfo,
        systemSetting,
        appAuthorizeSetting,
        timestamp: Date.now()
      };

      this.initialized = true;
      this.initializing = false;
      
      console.log('[SystemInfoCache] 系统信息初始化完成');
      return systemInfo;
    } catch (error) {
      console.error('[SystemInfoCache] 异步初始化失败:', error);
      this.initializing = false;
      
      // 降级到同步方式
      return this.getSystemInfoSync();
    }
  }

  /**
   * 同步获取系统信息（兼容性方法，不推荐）
   * @returns {Object}
   */
  getSystemInfoSync() {
    if (this.initialized && this.isCacheValid()) {
      this.stats.cacheHits++;
      console.log('[SystemInfoCache] 使用缓存的系统信息');
      return this.cache.systemInfo;
    }

    console.log('[SystemInfoCache] 同步获取系统信息');
    this.stats.apiCalls++;
    this.stats.syncCalls++;

    try {
      const systemInfo = wx.getSystemInfoSync();
      
      // 更新缓存
      this.cache.systemInfo = systemInfo;
      this.cache.timestamp = Date.now();
      this.initialized = true;
      
      return systemInfo;
    } catch (error) {
      console.error('[SystemInfoCache] 同步获取系统信息失败:', error);
      return {};
    }
  }

  /**
   * 获取窗口信息
   * @returns {Promise<Object>}
   */
  async getWindowInfoAsync() {
    if (this.cache.windowInfo && this.isCacheValid()) {
      return this.cache.windowInfo;
    }

    try {
      const windowInfo = wx.getWindowInfo();
      this.cache.windowInfo = windowInfo;
      return windowInfo;
    } catch (error) {
      console.error('[SystemInfoCache] 获取窗口信息失败:', error);
      return {};
    }
  }

  /**
   * 获取设备信息
   * @returns {Promise<Object>}
   */
  async getDeviceInfoAsync() {
    if (this.cache.deviceInfo && this.isCacheValid()) {
      return this.cache.deviceInfo;
    }

    try {
      const deviceInfo = wx.getDeviceInfo();
      this.cache.deviceInfo = deviceInfo;
      return deviceInfo;
    } catch (error) {
      console.error('[SystemInfoCache] 获取设备信息失败:', error);
      return {};
    }
  }

  /**
   * 获取应用基础信息
   * @returns {Promise<Object>}
   */
  async getAppBaseInfoAsync() {
    if (this.cache.appBaseInfo && this.isCacheValid()) {
      return this.cache.appBaseInfo;
    }

    try {
      const appBaseInfo = wx.getAppBaseInfo();
      this.cache.appBaseInfo = appBaseInfo;
      return appBaseInfo;
    } catch (error) {
      console.error('[SystemInfoCache] 获取应用基础信息失败:', error);
      return {};
    }
  }

  /**
   * 获取系统设置
   * @returns {Object}
   */
  getSystemSetting() {
    if (this.cache.systemSetting && this.isCacheValid()) {
      return this.cache.systemSetting;
    }

    try {
      const systemSetting = wx.getSystemSetting();
      this.cache.systemSetting = systemSetting;
      return systemSetting;
    } catch (error) {
      console.error('[SystemInfoCache] 获取系统设置失败:', error);
      return {};
    }
  }

  /**
   * 异步获取系统设置
   * @returns {Promise<Object>}
   */
  async getSystemSettingAsync() {
    if (this.cache.systemSetting && this.isCacheValid()) {
      return this.cache.systemSetting;
    }

    try {
      const systemSetting = wx.getSystemSetting();
      this.cache.systemSetting = systemSetting;
      return systemSetting;
    } catch (error) {
      console.error('[SystemInfoCache] 异步获取系统设置失败:', error);
      return {};
    }
  }

  /**
   * 获取应用授权设置
   * @returns {Object}
   */
  getAppAuthorizeSetting() {
    if (this.cache.appAuthorizeSetting && this.isCacheValid()) {
      return this.cache.appAuthorizeSetting;
    }

    try {
      const appAuthorizeSetting = wx.getAppAuthorizeSetting();
      this.cache.appAuthorizeSetting = appAuthorizeSetting;
      return appAuthorizeSetting;
    } catch (error) {
      console.error('[SystemInfoCache] 获取应用授权设置失败:', error);
      return {};
    }
  }

  /**
   * 异步获取应用授权设置
   * @returns {Promise<Object>}
   */
  async getAppAuthorizeSettingAsync() {
    if (this.cache.appAuthorizeSetting && this.isCacheValid()) {
      return this.cache.appAuthorizeSetting;
    }

    try {
      const appAuthorizeSetting = wx.getAppAuthorizeSetting();
      this.cache.appAuthorizeSetting = appAuthorizeSetting;
      return appAuthorizeSetting;
    } catch (error) {
      console.error('[SystemInfoCache] 异步获取应用授权设置失败:', error);
      return {};
    }
  }

  /**
   * 获取特定的系统信息字段
   * @param {string} field 字段名
   * @returns {any}
   */
  getField(field) {
    if (!this.initialized || !this.isCacheValid()) {
      // 如果缓存无效，先同步获取
      this.getSystemInfoSync();
    }

    return this.cache.systemInfo?.[field];
  }

  /**
   * 获取窗口尺寸信息
   * @returns {Object}
   */
  getWindowSize() {
    const windowInfo = this.cache.windowInfo || this.cache.systemInfo;
    return {
      windowWidth: windowInfo?.windowWidth || 375,
      windowHeight: windowInfo?.windowHeight || 667,
      screenWidth: windowInfo?.screenWidth || 375,
      screenHeight: windowInfo?.screenHeight || 667,
      statusBarHeight: windowInfo?.statusBarHeight || 20,
      safeArea: windowInfo?.safeArea || {}
    };
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.cache = {
      systemInfo: null,
      windowInfo: null,
      deviceInfo: null,
      appBaseInfo: null,
      systemSetting: null,
      appAuthorizeSetting: null,
      timestamp: 0
    };
    this.initialized = false;
    console.log('[SystemInfoCache] 缓存已清除');
  }

  /**
   * 获取性能统计
   * @returns {Object}
   */
  getStats() {
    const total = this.stats.cacheHits + this.stats.apiCalls;
    const hitRate = total > 0 ? (this.stats.cacheHits / total * 100).toFixed(2) : 0;
    
    return {
      ...this.stats,
      hitRate: `${hitRate}%`,
      cacheValid: this.isCacheValid(),
      initialized: this.initialized
    };
  }
}

// 创建全局实例
const systemInfoCache = new SystemInfoCache();

module.exports = {
  systemInfoCache,
  SystemInfoCache
};
