{"rules": [{"action": "allow", "page": "pages/index/index", "priority": 10, "index": "true"}, {"action": "allow", "page": "basic-tools/pages/colorQuery/colorQuery", "priority": 9, "index": "true"}, {"action": "allow", "page": "basic-tools/pages/colorDetail/colorDetail", "params": ["id"], "matching": "exact", "priority": 9, "index": "true"}, {"action": "allow", "page": "basic-tools/pages/colorPalette/colorPalette", "priority": 8, "index": "true"}, {"action": "allow", "page": "basic-tools/pages/contrastChecker/contrastChecker", "priority": 7, "index": "true"}, {"action": "allow", "page": "advanced-tools/pages/colorblindSimulator/colorblindSimulator", "priority": 7, "index": "true"}, {"action": "allow", "page": "advanced-tools/pages/gradientGenerator/gradientGenerator", "priority": 8, "index": "true"}, {"action": "allow", "page": "advanced-tools/pages/gradientWallpaper/gradientWallpaper", "priority": 8, "index": "true"}, {"action": "allow", "page": "basic-tools/pages/colorConverter/colorConverter", "priority": 6, "index": "true"}, {"action": "allow", "page": "advanced-tools/pages/toneGenerator/toneGenerator", "priority": 6, "index": "true"}, {"action": "allow", "page": "ambient-light/pages/ambientLight/ambientLight", "priority": 6, "index": "true"}, {"action": "allow", "page": "pages/imageColorPicker/imageColorPicker", "priority": 6, "index": "true"}, {"action": "allow", "page": "clothing-package/pages/clothingColorTool/clothingColorTool", "priority": 8, "index": "true"}, {"action": "allow", "page": "clothing-package/pages/skinToneTest/skinToneTest", "priority": 8, "index": "true"}, {"action": "allow", "page": "card-package/pages/customColorEditor/customColorEditor", "priority": 6, "index": "true"}, {"action": "allow", "page": "card-package/pages/customImageColorPicker/customImageColorPicker", "priority": 6, "index": "true"}, {"action": "allow", "page": "card-package/pages/quickColorPicker/quickColorPicker", "priority": 6, "index": "true"}, {"action": "allow", "page": "ambient-light/pages/about/about", "priority": 6, "index": "true"}, {"action": "disallow", "page": "pages/template/template"}, {"action": "disallow", "page": "card-package/pages/colorPicker/colorPicker"}, {"action": "disallow", "page": "pages/preview/preview"}, {"action": "disallow", "page": "clothing-package/pages/clothingPreview/clothingPreview"}, {"action": "disallow", "page": "clothing-package/pages/skinToneReport/skinToneReport"}, {"action": "disallow", "page": "*"}]}