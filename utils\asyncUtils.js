/**
 * 异步操作工具类
 * 提供统一的异步操作管理和错误处理
 */

class AsyncOperationManager {
  constructor() {
    this.operations = new Map();
    this.retryConfig = {
      maxRetries: 3,
      retryDelay: 1000,
      backoffMultiplier: 2
    };
  }

  // 带重试机制的异步操作
  async withRetry(operation, operationId, config = {}) {
    const finalConfig = { ...this.retryConfig, ...config };
    let lastError;

    for (let attempt = 0; attempt <= finalConfig.maxRetries; attempt++) {
      try {
        if (attempt > 0) {
          const delay = finalConfig.retryDelay * Math.pow(finalConfig.backoffMultiplier, attempt - 1);
          await this.delay(delay);
          console.log(`${operationId} 第${attempt}次重试`);
        }

        const result = await operation();
        if (attempt > 0) {
          console.log(`${operationId} 重试成功`);
        }
        return result;

      } catch (error) {
        lastError = error;
        console.error(`${operationId} 第${attempt + 1}次尝试失败:`, error);

        if (attempt === finalConfig.maxRetries) {
          break;
        }

        // 检查是否为不可重试的错误
        if (this.isNonRetryableError(error)) {
          console.log(`${operationId} 遇到不可重试错误，停止重试`);
          break;
        }
      }
    }

    console.error(`${operationId} 最终失败:`, lastError);
    throw lastError;
  }

  // 判断是否为不可重试的错误
  isNonRetryableError(error) {
    const message = error.message || '';
    return message.includes('用户取消') || 
           message.includes('权限被拒绝') ||
           message.includes('不支持');
  }

  // 并发控制
  async withConcurrencyLimit(operations, limit = 3) {
    const results = [];
    const executing = [];

    for (const operation of operations) {
      const promise = operation().then(result => {
        executing.splice(executing.indexOf(promise), 1);
        return result;
      }).catch(error => {
        executing.splice(executing.indexOf(promise), 1);
        throw error;
      });

      results.push(promise);
      executing.push(promise);

      if (executing.length >= limit) {
        await Promise.race(executing);
      }
    }

    return Promise.allSettled(results);
  }

  // 超时控制
  withTimeout(operation, timeout = 10000, timeoutMessage = '操作超时') {
    return Promise.race([
      operation(),
      new Promise((_, reject) => {
        setTimeout(() => {
          reject(new Error(`${timeoutMessage} (${timeout}ms)`));
        }, timeout);
      })
    ]);
  }

  // 延迟工具
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 安全的异步操作包装
  async safeAsync(operation, fallback = null, errorHandler = null) {
    try {
      return await operation();
    } catch (error) {
      if (errorHandler) {
        errorHandler(error);
      } else {
        console.error('异步操作失败:', error);
      }
      return fallback;
    }
  }

  // 批量操作
  async batchProcess(items, processor, batchSize = 5) {
    const results = [];
    
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      const batchPromises = batch.map(item => 
        this.safeAsync(() => processor(item))
      );
      
      const batchResults = await Promise.allSettled(batchPromises);
      results.push(...batchResults);
      
      // 批次间延迟，避免过于频繁的操作
      if (i + batchSize < items.length) {
        await this.delay(100);
      }
    }
    
    return results;
  }
}

// 单例模式
const asyncManager = new AsyncOperationManager();

// 便捷的工具函数
const asyncUtils = {
  // 重试操作
  withRetry: (operation, operationId, config) => 
    asyncManager.withRetry(operation, operationId, config),
    
  // 超时控制
  withTimeout: (operation, timeout, timeoutMessage) => 
    asyncManager.withTimeout(operation, timeout, timeoutMessage),
    
  // 并发控制
  withConcurrencyLimit: (operations, limit) => 
    asyncManager.withConcurrencyLimit(operations, limit),
    
  // 延迟
  delay: (ms) => asyncManager.delay(ms),
  
  // 安全异步操作
  safeAsync: (operation, fallback, errorHandler) =>
    asyncManager.safeAsync(operation, fallback, errorHandler),
    
  // 批量处理
  batchProcess: (items, processor, batchSize) =>
    asyncManager.batchProcess(items, processor, batchSize),

  // Promise化微信API的通用方法
  promisify: (wxApi, options = {}) => {
    return new Promise((resolve, reject) => {
      wxApi({
        ...options,
        success: resolve,
        fail: reject
      });
    });
  },

  // 带超时的Promise化微信API
  promisifyWithTimeout: (wxApi, options = {}, timeout = 10000) => {
    return asyncUtils.withTimeout(
      () => asyncUtils.promisify(wxApi, options),
      timeout,
      '微信API调用超时'
    );
  },

  // 安全的页面跳转
  safeNavigate: async (navigateFunc, fallbackUrl = '/pages/index/index') => {
    try {
      await navigateFunc();
    } catch (error) {
      console.error('页面跳转失败:', error);
      try {
        await asyncUtils.promisify(wx.switchTab, { url: fallbackUrl });
      } catch (fallbackError) {
        console.error('降级跳转也失败:', fallbackError);
      }
    }
  },

  // 安全的数据存储
  safeStorage: {
    set: async (key, data) => {
      return asyncUtils.safeAsync(
        () => asyncUtils.promisify(wx.setStorage, { key, data }),
        false,
        (error) => console.error(`存储${key}失败:`, error)
      );
    },
    
    get: async (key, defaultValue = null) => {
      return asyncUtils.safeAsync(
        async () => {
          const result = await asyncUtils.promisify(wx.getStorage, { key });
          return result.data;
        },
        defaultValue,
        (error) => console.warn(`获取${key}失败:`, error)
      );
    },
    
    remove: async (key) => {
      return asyncUtils.safeAsync(
        () => asyncUtils.promisify(wx.removeStorage, { key }),
        false,
        (error) => console.warn(`删除${key}失败:`, error)
      );
    }
  }
};

module.exports = {
  AsyncOperationManager,
  asyncManager,
  asyncUtils
};
