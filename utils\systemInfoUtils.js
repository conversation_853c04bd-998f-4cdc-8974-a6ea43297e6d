/**
 * 系统信息工具函数
 * 解决 "too early" 错误，安全地获取系统信息
 */

/**
 * 安全地获取窗口信息
 * @param {Function} callback - 回调函数
 * @param {number} delay - 延迟时间（毫秒）
 */
function safeGetWindowInfo(callback, delay = 100) {
  // 先尝试使用全局缓存的系统信息
  const app = getApp();
  if (app && app.globalData && app.globalData.systemInfoReady) {
    const systemInfo = app.globalData.systemInfo;
    if (systemInfo && systemInfo.windowWidth) {
      callback(null, systemInfo);
      return;
    }
  }
  
  // 延迟获取，避免过早调用
  setTimeout(() => {
    try {
      const windowInfo = wx.getWindowInfo();
      callback(null, windowInfo);
    } catch (error) {
      // 返回默认值
      callback(null, {
        windowWidth: 375,
        windowHeight: 667,
        screenWidth: 375,
        screenHeight: 667,
        statusBarHeight: 20,
        pixelRatio: 2
      });
    }
  }, delay);
}

/**
 * 安全地获取设备信息
 * @param {Function} callback - 回调函数
 * @param {number} delay - 延迟时间（毫秒）
 */
function safeGetDeviceInfo(callback, delay = 100) {
  // 先尝试使用全局缓存的系统信息
  const app = getApp();
  if (app && app.globalData && app.globalData.systemInfoReady) {
    const systemInfo = app.globalData.systemInfo;
    if (systemInfo && systemInfo.pixelRatio) {
      callback(null, systemInfo);
      return;
    }
  }
  
  // 延迟获取，避免过早调用
  setTimeout(() => {
    try {
      const deviceInfo = wx.getDeviceInfo();
      callback(null, deviceInfo);
    } catch (error) {
      // 返回默认值
      callback(null, {
        pixelRatio: 2,
        platform: 'unknown',
        system: 'unknown'
      });
    }
  }, delay);
}

/**
 * 安全地获取菜单按钮信息
 * @param {Function} callback - 回调函数
 * @param {number} delay - 延迟时间（毫秒）
 */
function safeGetMenuButtonInfo(callback, delay = 100) {
  // 延迟获取，避免过早调用
  setTimeout(() => {
    try {
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      callback(null, menuButtonInfo);
    } catch (error) {
      // 返回默认值
      callback(null, {
        top: 28,
        bottom: 60,
        left: 278,
        right: 365,
        width: 87,
        height: 32
      });
    }
  }, delay);
}

/**
 * 安全地获取设备像素比
 * @returns {number} 设备像素比
 */
function safeGetPixelRatio() {
  try {
    // 优先使用全局缓存的系统信息
    const app = getApp();
    if (app && app.globalData && app.globalData.systemInfo && app.globalData.systemInfo.pixelRatio) {
      return app.globalData.systemInfo.pixelRatio;
    }
    
    // 如果全局信息不可用，尝试获取设备信息
    const deviceInfo = wx.getDeviceInfo();
    return deviceInfo.pixelRatio || 2;
  } catch (error) {
    // 返回安全的默认值
    return 2;
  }
}

/**
 * 等待系统信息准备就绪
 * @param {Function} callback - 回调函数
 * @param {number} maxWait - 最大等待时间（毫秒）
 */
function waitForSystemInfo(callback, maxWait = 3000) {
  const startTime = Date.now();
  
  function checkSystemInfo() {
    const app = getApp();
    if (app && app.globalData && app.globalData.systemInfoReady) {
      callback(null, app.globalData);
      return;
    }
    
    // 检查是否超时
    if (Date.now() - startTime > maxWait) {
      // 超时，使用默认值
      callback(null, {
        systemInfo: {
          windowWidth: 375,
          windowHeight: 667,
          statusBarHeight: 20,
          pixelRatio: 2
        },
        navBarHeight: 88,
        statusBarHeight: 20,
        systemInfoReady: true
      });
      return;
    }
    
    // 继续等待
    setTimeout(checkSystemInfo, 50);
  }
  
  checkSystemInfo();
}

/**
 * Promise版本的安全获取窗口信息
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Promise} Promise对象
 */
function safeGetWindowInfoAsync(delay = 100) {
  return new Promise((resolve) => {
    safeGetWindowInfo((error, windowInfo) => {
      resolve(windowInfo);
    }, delay);
  });
}

/**
 * Promise版本的安全获取设备信息
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Promise} Promise对象
 */
function safeGetDeviceInfoAsync(delay = 100) {
  return new Promise((resolve) => {
    safeGetDeviceInfo((error, deviceInfo) => {
      resolve(deviceInfo);
    }, delay);
  });
}

/**
 * Promise版本的等待系统信息
 * @param {number} maxWait - 最大等待时间（毫秒）
 * @returns {Promise} Promise对象
 */
function waitForSystemInfoAsync(maxWait = 3000) {
  return new Promise((resolve) => {
    waitForSystemInfo((error, systemInfo) => {
      resolve(systemInfo);
    }, maxWait);
  });
}

module.exports = {
  safeGetWindowInfo,
  safeGetDeviceInfo,
  safeGetMenuButtonInfo,
  safeGetPixelRatio,
  waitForSystemInfo,
  safeGetWindowInfoAsync,
  safeGetDeviceInfoAsync,
  waitForSystemInfoAsync
};
