/* pages/skinToneReport/skinToneReport.wxss */
.page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.container {
  flex: 1;
  padding: 30rpx 24rpx;
  overflow-y: auto;
  box-sizing: border-box;
}

/* 通用区块样式 */
.section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.06);
  box-sizing: border-box;
  width: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #191919;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 28rpx;
  background-color: #07c160;
  border-radius: 3rpx;
}

.analysis-subtitle {
  font-size: 24rpx;
  font-weight: 500;
  color: #666;
  margin: 30rpx 0 20rpx 0;
  padding-left: 10rpx;
  border-left: 3rpx solid #e0e0e0;
}

.section-subtitle {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
  line-height: 1.5;
}

/* 用户颜色预览 */
.user-color-container {
  width: 100%;
  height: 160rpx;
  border-radius: 16rpx;
  position: relative;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 24rpx;
  box-sizing: border-box;
}

.user-color-hex {
  font-size: 32rpx;
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.user-color-details {
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.color-detail-item {
  font-size: 22rpx;
  color: #666;
  font-weight: 400;
  line-height: 1.3;
}

.user-color-container .copy-btn {
  position: absolute !important;
  bottom: 20rpx !important;
  right: 20rpx !important;
  background-color: rgba(255, 255, 255, 0.9) !important;
  color: #333 !important;
  font-size: 16rpx !important;
  font-weight: 500 !important;
  padding: 12rpx 20rpx !important;
  border-radius: 12rpx !important;
  border: none !important;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: auto !important;
  max-width: none !important;
  width: auto !important;
  height: auto !important;
  line-height: normal !important;
  white-space: nowrap !important;
  margin: 0 !important;
  box-sizing: border-box !important;
  display: inline-block !important;
  text-align: center !important;
  vertical-align: middle !important;
  outline: none !important;
  box-shadow: none !important;
}

.user-color-container .copy-btn::after {
  border: none !important;
}

.user-color-container .copy-btn:active {
  background-color: rgba(255, 255, 255, 0.8) !important;
  transform: scale(0.95) !important;
  opacity: 1 !important;
}



/* 最佳匹配结果 */
.best-match-container {
  width: 100%;
}

.similarity-analysis {
  margin-top: 30rpx;
}

.match-result-text {
  width: 100%;
  padding: 20rpx 0;
  box-sizing: border-box;
}

.match-title {
  font-size: 24rpx;
  font-weight: 500;
  color: #666;
  margin-bottom: 8rpx;
}

.match-content {
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.match-name-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
}

.match-details {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-top: 2rpx;
}

.similarity-text {
  font-size: 26rpx;
  font-weight: 600;
  color: #07c160;
}

.level-text {
  font-size: 22rpx;
  font-weight: 400;
  color: #999;
}

/* 相似度列表 */
.similarity-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.similarity-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 16rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  position: relative;
  border: 2rpx solid transparent;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.similarity-item.best-match {
  background-color: rgba(7, 193, 96, 0.05);
  border-color: #07c160;
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.15);
}

.similarity-rank {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
  background-color: #07c160;
  color: white;
  border-radius: 50%;
  font-size: 20rpx;
  font-weight: 600;
  flex-shrink: 0;
}

.similarity-item.best-match .similarity-rank {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  color: #333;
  font-weight: bold;
}

/* 颜色对比区域 */
.color-comparison {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin: 0 8rpx;
}

.color-contrast-block {
  width: 110rpx;
  height: 110rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.user-skin-circle {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
}

.similarity-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  min-width: 0;
}

.similarity-name {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.similarity-percentage {
  font-size: 22rpx;
  font-weight: 600;
}

.similarity-level {
  font-size: 18rpx;
  opacity: 0.8;
}

.similarity-bar {
  width: 80rpx;
  height: 6rpx;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 3rpx;
  overflow: hidden;
  flex-shrink: 0;
}

.similarity-progress {
  height: 100%;
  border-radius: 3rpx;
  transition: all 0.3s ease;
}

.similarity-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  color: #333;
  font-size: 20rpx;
  font-weight: bold;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 15rpx rgba(255, 215, 0, 0.3);
}

/* 展开/收起按钮 */
.expand-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 16rpx;
  margin-top: 16rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
  cursor: pointer;
  transition: all 0.2s ease;
}

.expand-toggle:active {
  background-color: #e9ecef;
  border-color: #dee2e6;
  transform: scale(0.98);
}

.expand-toggle:active {
  transform: scale(0.98);
  background-color: #dee2e6;
}

.expand-text {
  font-size: 24rpx;
  font-weight: 500;
  color: #495057;
}

.expand-icon {
  font-size: 20rpx;
  color: #6c757d;
  transition: transform 0.3s ease;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
}

.action-btn {
  flex: 1;
  min-width: 200rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  height: 80rpx;
  border-radius: 12rpx;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 600;
  border: none;
  padding: 0;
  margin: 0;
  line-height: normal;
}

.action-btn::after {
  border: none;
}

.action-btn.primary {
  background-color: #07c160;
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.3);
}

.action-btn.secondary {
  background-color: #f5f5f5;
  color: #333;
  border: 1rpx solid #e0e0e0;
}

.action-btn:active {
  transform: scale(0.98);
  opacity: 0.8;
}

.action-btn-icon {
  font-size: 28rpx;
  font-weight: normal;
}

.action-btn-text {
  font-size: 26rpx;
}

/* 说明信息 */
.info-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
  padding: 16rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  border-left: 4rpx solid #07c160;
}

.info-icon {
  font-size: 24rpx;
  flex-shrink: 0;
}

.info-text {
  font-size: 24rpx;
  color: #333;
  line-height: 1.5;
  flex: 1;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 20rpx;
  text-align: center;
}

.loading-spinner {
  width: 50rpx;
  height: 50rpx;
  border: 4rpx solid rgba(7, 193, 96, 0.2);
  border-top: 4rpx solid #07c160;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 26rpx;
  color: #666;
}

/* 多肤色报告样式 */
.multi-color-report {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}



.single-color-report {
  /* 单肤色模式使用默认样式 */
}

/* 肤色分析容器样式 */
.analysis-container {
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #f0f0f0;
}

/* 肤色分析样式 */
.analysis-item {
  margin-bottom: 24rpx;
  padding: 0;
  background-color: transparent;
  border: none;
}

.analysis-item:last-child {
  margin-bottom: 0;
}

.analysis-title {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 12rpx;
}

.analysis-icon {
  font-size: 24rpx;
  flex-shrink: 0;
}

.analysis-label {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.analysis-content {
  padding-left: 32rpx;
}

.analysis-category {
  font-size: 24rpx;
  font-weight: 600;
  color: #07c160;
  margin-bottom: 8rpx;
}

.analysis-description {
  font-size: 22rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 16rpx;
}

/* 护肤建议样式 */
.skincare-suggestions {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  gap: 8rpx;
}

.suggestion-bullet {
  font-size: 20rpx;
  color: #07c160;
  font-weight: bold;
  flex-shrink: 0;
  margin-top: 2rpx;
}

.suggestion-text {
  font-size: 22rpx;
  color: #666;
  line-height: 1.5;
  flex: 1;
}

/* 美妆建议样式 */
.makeup-suggestions {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.makeup-category {
  padding: 12rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.makeup-category:last-child {
  border-bottom: none;
}

.makeup-category-title {
  font-size: 22rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 6rpx;
}

.makeup-category-content {
  font-size: 22rpx;
  color: #666;
  line-height: 1.5;
}

/* 算法说明模块样式 */
.algorithm-explanation {
  padding: 0;
}

.algorithm-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.8;
  text-align: justify;
  letter-spacing: 0.5rpx;
}
