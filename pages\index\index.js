// index.js
const { getTodayLuckyCard } = require('../../data/luckyData');
const adFreeUtils = require('../../utils/adFreeUtils');
const { storageCache } = require('../../utils/storageCache');
const logUtils = require('../../utils/logUtils');

Page({
  data: {
    // 页面数据
    showLuckyModal: false,
    luckyData: null,
    // 激励广告相关
    rewardedVideoAd: null,
    adInitialized: false,
    dailyLuckyCount: 0, // 当日抽签次数
    lastLuckyDate: '', // 上次抽签日期
    // 提示框相关
    showLuckyTipModal: false,
    tipModalType: '', // 'second' 或 'third'
    tipModalContent: '',
    tipModalButtons: []
  },

  onLoad: function(options) {
    try {
      // 异步初始化每日抽签计数，避免阻塞首屏渲染
      wx.nextTick(() => {
        this.initDailyLuckyCountAsync();
      });

      // 延迟初始化激励广告，避免阻塞页面加载
      wx.nextTick(() => {
        setTimeout(() => {
          this.initRewardedVideoAd();
        }, 100);
      });
    } catch (error) {
      console.error('页面初始化失败:', error);
    }
  },

  // 注意：所有页面跳转已使用 navigator 组件实现，不再需要 JS 方法

  // 用户点击右上角分享或使用分享按钮
  onShareAppMessage: function() {
    return {
      title: 'KALA配色 - 专业的色彩工具集',
      path: '/pages/index/index',
      imageUrl: '/assets/images/share.png' // 分享图片
    };
  },

  // 设置页面标题 - 修复缺少fail回调
  onShow: function() {
    wx.setNavigationBarTitle({
      title: 'KALA配色-专业色彩工具',
      success: () => {
        console.log('导航栏标题设置成功');
      },
      fail: (err) => {
        console.warn('设置导航栏标题失败:', err);
        // 不阻断流程，仅记录警告
      }
    });
  },

  // 显示幸运签
  showLuckyCard: function() {
    console.log('=== 首页显示幸运签 ===');
    console.log('当前状态:', {
      showLuckyModal: this.data.showLuckyModal,
      hasLuckyData: !!this.data.luckyData,
      dailyLuckyCount: this.data.dailyLuckyCount
    });

    // 检查抽签次数并决定是否显示提示框或广告
    this.checkLuckyCountAndShow();
  },

  // 检查抽签次数并决定显示逻辑
  checkLuckyCountAndShow: function() {
    const { dailyLuckyCount } = this.data;
    const isAdFree = adFreeUtils.isAdFreeActivated();

    console.log('抽签次数检查:', { dailyLuckyCount, isAdFree });

    if (dailyLuckyCount === 0) {
      // 第一次抽签，直接显示
      this.directShowLuckyCard();
    } else if (dailyLuckyCount === 1) {
      // 第二次抽签，显示提示框（不弹广告）
      this.showLuckyTipModal('second');
    } else if (dailyLuckyCount === 2) {
      // 第三次抽签，显示提示框（不弹广告）
      this.showLuckyTipModal('third');
    } else {
      // 第四次及以后
      if (isAdFree) {
        // 去广告用户直接显示幸运签
        this.directShowLuckyCard();
      } else {
        // 普通用户显示广告提示框
        this.showLuckyTipModal('fourth');
      }
    }
  },

  // 直接显示幸运签（不弹广告）
  directShowLuckyCard: function() {
    console.log('直接显示幸运签');

    // 更新抽签次数
    this.updateDailyLuckyCount();

    // 每次点击都获取新的随机幸运签
    const luckyData = getTodayLuckyCard();
    console.log('显示幸运签数据:', luckyData);
    console.log('颜色名称:', luckyData.colorName);
    console.log('签文标题:', luckyData.title);
    console.log('签文内容:', luckyData.content);

    // 使用随机生成的数据
    this.setData({
      showLuckyModal: true,
      luckyData: {
        ...luckyData,
        // 添加时间戳确保数据变化被检测到
        _timestamp: Date.now()
      }
    }, () => {
      console.log('✅ 首页setData完成，当前状态:', {
        showLuckyModal: this.data.showLuckyModal,
        hasLuckyData: !!this.data.luckyData
      });

      // 如果组件已存在，调用强制显示方法
      const luckyCardComponent = this.selectComponent('lucky-card');
      if (luckyCardComponent) {
        console.log('调用组件强制显示方法');
        luckyCardComponent.forceShow();
      }
    });
  },

  // 显示提示框
  showLuckyTipModal: function(type) {
    let content = '';
    let buttons = [];

    if (type === 'second') {
      content = '每日一签，抽多了就不灵验哦';
      buttons = [
        { text: '取消', type: 'cancel' },
        { text: '再抽一次吧', type: 'continue' }
      ];
    } else if (type === 'third') {
      content = '做人要节制，事不过三哦';
      buttons = [
        { text: '取消', type: 'cancel' },
        { text: '再抽一次吧', type: 'continue' }
      ];
    } else if (type === 'fourth') {
      content = '今日抽签次数已达上限，观看视频后可继续抽签';
      buttons = [
        { text: '取消', type: 'cancel' },
        { text: '观看视频', type: 'watchAd' }
      ];
    }

    this.setData({
      showLuckyTipModal: true,
      tipModalType: type,
      tipModalContent: content,
      tipModalButtons: buttons
    });
  },

  // 隐藏提示框
  hideLuckyTipModal: function() {
    this.setData({
      showLuckyTipModal: false,
      tipModalType: '',
      tipModalContent: '',
      tipModalButtons: []
    });
  },

  // 处理提示框按钮点击
  onTipModalButtonTap: function(e) {
    const buttonType = e.currentTarget.dataset.type;
    const modalType = this.data.tipModalType;

    console.log('提示框按钮点击:', { buttonType, modalType });

    this.hideLuckyTipModal();

    if (buttonType === 'cancel') {
      // 用户取消，不做任何操作
      return;
    } else if (buttonType === 'continue') {
      // 第二次和第三次抽签，再抽一次（不弹广告）
      this.directShowLuckyCard();
    } else if (buttonType === 'watchAd') {
      // 第四次及以后，需要观看广告
      const isAdFree = adFreeUtils.isAdFreeActivated();
      if (isAdFree) {
        // 去广告用户直接显示
        this.directShowLuckyCard();
      } else {
        // 显示激励广告
        this.showRewardedVideoAd();
      }
    }
  },

  // 隐藏幸运签
  hideLuckyCard: function() {
    console.log('=== 首页隐藏幸运签 ===');
    this.setData({
      showLuckyModal: false
    }, () => {
      console.log('✅ 首页幸运签弹窗已隐藏');
    });
  },



  // 初始化每日抽签计数
  initDailyLuckyCount: function() {
    try {
      const today = new Date().toDateString();
      // 使用缓存获取存储数据，避免重复访问
      const storedData = storageCache.getStorageSync('luckySign_dailyCount');

      if (storedData && storedData.date === today) {
        // 今天已有记录，使用存储的抽签次数
        this.setData({
          dailyLuckyCount: storedData.count || 0,
          lastLuckyDate: storedData.date
        });
        console.log('[Index] 每日抽签计数初始化完成（使用缓存）:', storedData.count);
      } else {
        // 新的一天，重置计数
        const newData = {
          date: today,
          count: 0
        };

        this.setData({
          dailyLuckyCount: 0,
          lastLuckyDate: today
        });

        // 保存到本地存储（使用缓存）
        storageCache.setStorageSync('luckySign_dailyCount', newData);
        console.log('[Index] 新的一天，重置抽签计数（使用缓存）');
      }
    } catch (error) {
      console.error('初始化每日抽签计数失败', error);
      // 出错时使用默认值
      this.setData({
        dailyLuckyCount: 0,
        lastLuckyDate: new Date().toDateString()
      });
    }
  },

  /**
   * 异步初始化每日抽签计数（优化版本）
   */
  async initDailyLuckyCountAsync() {
    try {
      const today = new Date().toDateString();
      // 使用缓存的异步方式获取存储数据
      const storedData = await storageCache.getStorage('luckySign_dailyCount');

      if (storedData && storedData.date === today) {
        // 今天已有记录，使用存储的抽签次数
        this.setData({
          dailyLuckyCount: storedData.count || 0,
          lastLuckyDate: storedData.date
        });
        console.log('[Index] 每日抽签计数异步初始化完成（使用缓存）:', storedData.count);
      } else {
        // 新的一天，重置计数
        const newData = {
          date: today,
          count: 0
        };

        this.setData({
          dailyLuckyCount: 0,
          lastLuckyDate: today
        });

        // 保存到本地存储（使用缓存）
        await storageCache.setStorage('luckySign_dailyCount', newData);
        console.log('[Index] 新的一天，异步重置抽签计数（使用缓存）');
      }
    } catch (error) {
      console.error('异步初始化每日抽签计数失败', error);
      // 出错时使用默认值
      this.setData({
        dailyLuckyCount: 0,
        lastLuckyDate: new Date().toDateString()
      });
    }
  },

  // 更新每日抽签计数
  updateDailyLuckyCount: function() {
    const today = new Date().toDateString();
    let newCount = this.data.dailyLuckyCount + 1;

    this.setData({
      dailyLuckyCount: newCount,
      lastLuckyDate: today
    });

    // 保存到本地存储（使用缓存）
    try {
      const newData = {
        date: today,
        count: newCount
      };
      storageCache.setStorageSync('luckySign_dailyCount', newData);
      console.log('[Index] 抽签计数已更新（使用缓存）:', newCount);
    } catch (error) {
      console.error('保存每日抽签计数失败', error);
    }
  },

  // 卡片翻转事件
  onCardFlip: function(e) {
    console.log('卡片翻转:', e.detail.flipped);
  },

  // 初始化激励广告
  initRewardedVideoAd: function() {
    // 防止重复初始化
    if (this.data.adInitialized) {
      return;
    }

    // 检查是否支持激励广告
    if (wx.createRewardedVideoAd) {
      let rewardedVideoAd = null;

      try {
        console.log('创建幸运签激励广告实例');
        // 创建激励广告实例
        rewardedVideoAd = wx.createRewardedVideoAd({
          adUnitId: 'adunit-4c73f639dd69c844' // 幸运签的激励广告位ID
        });

        console.log('幸运签激励广告实例创建成功，立即设置错误处理函数');
      } catch (createError) {
        console.error('创建幸运签激励广告实例失败:', createError);
        this.setData({
          adInitialized: false,
          rewardedVideoAd: null
        });
        return;
      }

      // 确保广告实例存在后再设置错误处理函数
      if (rewardedVideoAd) {
        try {
          // 立即设置错误处理函数，防止创建时就出错
          rewardedVideoAd.onError(err => {
            console.error('幸运签激励广告加载失败', err);

            // 处理两种主要错误情况
            if (err.errCode === 1004) {
              // 情况1: 调用正常，但没有合适的广告返回给用户
              console.log('没有合适的广告返回，隐藏广告入口');
              this.setData({
                adInitialized: false,
                rewardedVideoAd: null
              });
              // 无广告返回时，建议不设置激励视频入口，直接显示幸运签
              this.directShowLuckyCard();
            } else if (err.errCode === 1000 || err.errCode === 1001 || err.errCode === 1002) {
              // 情况2: 错误的调用导致的异常返回
              console.log('广告调用异常，错误码:', err.errCode);
              this.setData({
                adInitialized: false,
                rewardedVideoAd: null
              });
              // 调用异常时也直接显示幸运签，不阻塞用户流程
              this.directShowLuckyCard();
            } else {
              // 其他未知错误
              console.log('广告未知错误，错误码:', err.errCode);
              this.setData({
                adInitialized: false,
                rewardedVideoAd: null
              });
              // 保险起见，也直接显示幸运签
              this.directShowLuckyCard();
            }
          });

          console.log('幸运签广告错误处理函数设置成功');
        } catch (errorHandlerError) {
          console.error('设置幸运签广告错误处理函数失败:', errorHandlerError);
          this.setData({
            adInitialized: false,
            rewardedVideoAd: null
          });
          return;
        }

        try {
          // 设置其他监听器
          rewardedVideoAd.onLoad(() => {
            console.log('幸运签激励广告加载成功');
          });

          rewardedVideoAd.onClose(res => {
            console.log('幸运签广告关闭事件:', res);
            if (res && res.isEnded) {
              // 用户完整观看了广告，继续显示幸运签
              console.log('用户完整观看广告，显示幸运签');
              this.directShowLuckyCard();
            } else {
              // 用户中途退出了广告
              console.log('用户中途退出广告');
              wx.showToast({
                title: '请观看完整广告',
                icon: 'none',
                duration: 1500
              });
            }
          });

          console.log('幸运签广告事件监听器设置成功');
        } catch (listenerError) {
          console.error('设置幸运签广告事件监听器失败:', listenerError);
          this.setData({
            adInitialized: false,
            rewardedVideoAd: null
          });
          return;
        }

        // 最后设置数据，表示初始化完成
        try {
          this.setData({
            rewardedVideoAd: rewardedVideoAd,
            adInitialized: true
          });
          console.log('幸运签激励广告初始化成功');
        } catch (setDataError) {
          console.error('设置幸运签广告数据失败:', setDataError);
          this.setData({
            adInitialized: false,
            rewardedVideoAd: null
          });
        }
      } else {
        console.error('幸运签激励广告实例创建失败，实例为空');
        this.setData({
          adInitialized: false,
          rewardedVideoAd: null
        });
      }
    } else {
      console.log('当前版本不支持激励广告');
    }
  },

  // 显示激励广告 - 修复Promise链错误处理
  showRewardedVideoAd: async function() {
    console.log('尝试显示幸运签激励广告');
    const rewardedVideoAd = this.data.rewardedVideoAd;

    // 如果广告还未初始化，先初始化
    if (!rewardedVideoAd) {
      console.log('广告未初始化，开始初始化');
      this.initRewardedVideoAd();
      // 给一点时间让广告初始化，但设置最大重试次数
      if (!this.adRetryCount) {
        this.adRetryCount = 0;
      }

      if (this.adRetryCount < 3) {
        this.adRetryCount++;
        setTimeout(() => {
          this.showRewardedVideoAd();
        }, 1000);
      } else {
        console.log('广告初始化重试次数超限，直接显示幸运签');
        this.directShowLuckyCard();
      }
      return;
    }

    try {
      // 第一次尝试显示广告
      console.log('开始显示幸运签广告');
      await rewardedVideoAd.show();
      console.log('广告显示成功');
    } catch (firstErr) {
      console.log('广告显示失败，尝试重新加载:', firstErr);

      try {
        // 重新加载广告
        await rewardedVideoAd.load();
        console.log('广告重新加载成功，再次显示');

        // 第二次尝试显示广告
        await rewardedVideoAd.show();
        console.log('广告重新显示成功');
      } catch (secondErr) {
        // 最终错误处理
        console.error('幸运签激励广告最终失败', secondErr);
        wx.showToast({
          title: '广告加载失败，直接显示幸运签',
          icon: 'none',
          duration: 1500
        });
        // 广告失败时直接显示幸运签
        this.directShowLuckyCard();
      }
    }
  },

  // 页面卸载时清理资源
  onUnload: function() {
    console.log('首页卸载，开始清理资源');

    // 清理激励广告事件监听器
    if (this.data.rewardedVideoAd) {
      try {
        // 移除所有事件监听器
        this.data.rewardedVideoAd.offLoad();
        this.data.rewardedVideoAd.offError();
        this.data.rewardedVideoAd.offClose();
        console.log('激励广告事件监听器已清理');
      } catch (error) {
        console.error('清理激励广告事件监听器失败:', error);
      }

      // 清空广告实例引用
      this.setData({
        rewardedVideoAd: null,
        adInitialized: false
      });
    }

    // 重置广告重试计数器
    this.adRetryCount = 0;

    console.log('首页资源清理完成');
  },

  // 分享方法 - 支持幸运签分享
  onShareAppMessage: async function(res) {
    console.log('分享事件触发:', res);

    // 检查是否是从幸运签组件触发的分享
    if (this.data.showLuckyModal && this.data.luckyData) {
      const luckyData = this.data.luckyData;

      try {
        // 构建分享标题，包含运势信息
        const shareTitle = `我今天抽到${luckyData.fortune}-【${luckyData.title}】，你也赶紧来试试运气吧！`;

        // 获取幸运签组件实例
        const luckyCardComponent = this.selectComponent('lucky-card');
        let shareImagePath = null;

        if (luckyCardComponent) {
          try {
            // 生成分享图片
            shareImagePath = await luckyCardComponent.generateShareImage();
          } catch (imageError) {
            console.error('生成分享图片失败，使用默认图片:', imageError);
            // 继续使用默认图片
          }
        }

        console.log('幸运签分享内容:', {
          title: shareTitle,
          imageUrl: shareImagePath || '/assets/images/share.png'
        });

        return {
          title: shareTitle,
          path: '/pages/index/index',
          imageUrl: shareImagePath || '/assets/images/share.png'
        };

      } catch (error) {
        console.error('幸运签分享失败:', error);
        // 降级到默认分享，但保持幸运签标题
        const shareTitle = `我今天抽到${luckyData.fortune}-【${luckyData.title}】，你也赶紧来试试运气吧！`;
        return {
          title: shareTitle,
          path: '/pages/index/index',
          imageUrl: '/assets/images/share.png'
        };
      }
    }

    // 默认分享内容
    return {
      title: 'KALA配色 - 专业的色彩工具集',
      path: '/pages/index/index',
      imageUrl: '/assets/images/share.png'
    };
  }
})
