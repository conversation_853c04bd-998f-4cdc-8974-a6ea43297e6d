// app.js
const { setupGlobalErrorInterceptor } = require('./utils/errorInterceptor');
const { systemInfoCache } = require('./utils/systemInfoCache');
const { storageCache } = require('./utils/storageCache');
const logUtils = require('./utils/logUtils');

App({
  globalData: {
    // 全局数据
    systemInfo: {},
    navBarHeight: 0,
    statusBarHeight: 0,
    // 添加系统信息初始化状态
    systemInfoReady: false
  },

  onLaunch() {
    // 设置全局错误拦截器（优先级最高）
    setupGlobalErrorInterceptor();

    // 异步获取系统信息，避免阻塞启动
    this.initSystemInfo();

    // 预加载缓存数据
    this.preloadCacheData();

    // 添加全局错误处理
    this.setupGlobalErrorHandler();
  },

  // 设置全局错误处理
  setupGlobalErrorHandler() {
    // 捕获未处理的错误
    wx.onError((error) => {
      // 过滤掉开发者工具和微信内部的错误
      if (error && typeof error === 'string') {
        // 忽略开发者工具相关错误
        if (error.includes('aiad error') ||
            error.includes('benchmark') ||
            error.includes('unexpected page') ||
            error.includes('.html')) {
          return;
        }
        // 忽略微信小程序内部视图管理错误和系统错误
        if (error.includes('removeTextView') ||
            error.includes('removeBaseView') ||
            error.includes('removeView') ||
            error.includes('updateTextView') ||
            error.includes('reportKeyValue') ||
            error.includes('fail no root view') ||
            error.includes('too early') ||
            error.includes('too eayly') ||
            error.includes('jsbridge') ||
            error.includes('WAServiceMainContext') ||
            error.includes('Cannot read property \'length\' of undefined') ||
            error.includes('upperBound') ||
            error.includes('HP.<anonymous>')) {
          // console.warn('忽略微信内部系统错误:', error);
          return;
        }
        // 忽略 _getData 相关错误（微信小程序内部问题）
        if (error.includes('_getData is not a function') ||
            error.includes('this._getData is not a function') ||
            error.includes('_getDataFromDataSet') ||
            error.includes('getData is not a function') ||
            error.includes('_getData') ||
            error.includes('getDataFromDataSet') ||
            error.includes('webviewScriptError') ||
            error.includes('SystemError')) {
          // 静默忽略，这些通常是微信小程序内部或开发工具的问题
          // 在开发环境下可以输出警告，生产环境完全静默
          if (typeof __wxConfig !== 'undefined' && __wxConfig.debug) {
            logUtils.warn('忽略微信内部错误:', error.substring(0, 100));
          }
          return;
        }
        // webviewScriptError 已在上面的 _getData 错误处理中包含
        // 忽略 routeDone 相关错误
        if (error.includes('routeDone') ||
            error.includes('webviewId') ||
            error.includes('not the current page') ||
            error.includes('Page route') ||
            error.includes('system error')) {
          // console.warn('忽略路由相关错误:', error);
          return;
        }
        // 忽略 Axure 相关错误（可能是 PP 目录下的原型文件引起的）
        if (error.includes('$ax') ||
            error.includes('$axure') ||
            error.includes('axQuery') ||
            error.includes('repeater') ||
            error.includes('getData') ||
            error.includes('_getDataFromDataSet') ||
            error.includes('PP/') ||
            error.includes('PP\\') ||
            error.includes('axure') ||
            error.includes('sitemap')) {
          // 只是静默忽略
          return;
        }
      }
      // 生产环境中可以选择上报错误到服务器
      // console.error('全局错误:', error);
    });

    // 捕获未处理的Promise rejection
    wx.onUnhandledRejection((res) => {
      // 过滤掉微信小程序内部的视图管理错误
      if (res && res.reason) {
        let errorMsg = '';

        // 处理不同类型的错误信息
        if (typeof res.reason === 'string') {
          errorMsg = res.reason;
        } else if (typeof res.reason === 'object') {
          errorMsg = res.reason.errMsg || res.reason.message || '';
        }

        // 忽略微信内部视图管理相关错误和系统错误
        if (errorMsg.includes('removeTextView') ||
            errorMsg.includes('removeBaseView') ||
            errorMsg.includes('removeView') ||
            errorMsg.includes('updateTextView') ||
            errorMsg.includes('reportKeyValue') ||
            errorMsg.includes('fail no root view') ||
            errorMsg.includes('createTextView') ||
            errorMsg.includes('createBaseView') ||
            errorMsg.includes('too early') ||
            errorMsg.includes('too eayly') ||
            errorMsg.includes('jsbridge') ||
            errorMsg.includes('WAServiceMainContext') ||
            errorMsg.includes('Cannot read property \'length\' of undefined') ||
            errorMsg.includes('upperBound') ||
            errorMsg.includes('HP.<anonymous>')) {
          // console.warn('忽略微信内部系统错误:', errorMsg);
          return;
        }
      }
      // 生产环境中可以选择上报错误到服务器
      // console.error('未处理的Promise rejection:', res);
    });
  },



  // 预加载缓存数据
  async preloadCacheData() {
    try {
      logUtils.log('[App] 开始预加载缓存数据');

      // 异步初始化系统信息缓存
      await systemInfoCache.initSystemInfoAsync();

      // 预加载常用的存储数据
      await storageCache.preloadCommonData();

      logUtils.log('[App] 缓存数据预加载完成');

      // 输出性能统计
      logUtils.log('[App] SystemInfo缓存统计:', systemInfoCache.getStats());
      logUtils.log('[App] Storage缓存统计:', storageCache.getCacheStats());
    } catch (error) {
      logUtils.error('[App] 预加载缓存数据失败:', error);
    }
  },

  // 异步初始化系统信息
  initSystemInfo() {
    // 先设置默认值，确保应用能正常启动
    this.setDefaultSystemInfo();

    // 延迟更长时间获取真实系统信息，避免 "too early" 错误
    setTimeout(() => {
      this.getSystemInfo();
    }, 200);
  },

  // 设置默认系统信息
  setDefaultSystemInfo() {
    this.globalData.navBarHeight = 88; // 默认导航栏高度
    this.globalData.statusBarHeight = 20; // 默认状态栏高度
    this.globalData.systemInfoReady = false;
  },

  // 获取系统信息
  getSystemInfo() {
    try {
      // 使用新的API组合获取系统信息，增加错误处理
      let windowInfo, deviceInfo, appBaseInfo;

      try {
        windowInfo = wx.getWindowInfo();
      } catch (e) {
        windowInfo = {
          windowWidth: 375,
          windowHeight: 667,
          screenWidth: 375,
          screenHeight: 667,
          statusBarHeight: 20
        };
      }

      try {
        deviceInfo = wx.getDeviceInfo();
      } catch (e) {
        deviceInfo = {
          pixelRatio: 2,
          platform: 'unknown',
          system: 'unknown'
        };
      }

      try {
        appBaseInfo = wx.getAppBaseInfo();
      } catch (e) {
        appBaseInfo = {
          version: '1.0.0'
        };
      }

      // 组合系统信息对象
      const systemInfo = {
        ...windowInfo,
        ...deviceInfo,
        ...appBaseInfo,
        // 保持向后兼容性
        windowWidth: windowInfo.windowWidth,
        windowHeight: windowInfo.windowHeight,
        screenWidth: windowInfo.screenWidth,
        screenHeight: windowInfo.screenHeight,
        statusBarHeight: windowInfo.statusBarHeight,
        platform: deviceInfo.platform,
        system: deviceInfo.system,
        version: appBaseInfo.version
      };

      this.processSystemInfo(systemInfo);
    } catch (e) {
      // 生产环境中静默处理错误
      // console.warn('获取系统信息失败，使用默认值:', e);
      // 保持默认值
      this.globalData.systemInfoReady = true;
    }
  },

  // 处理系统信息
  processSystemInfo(systemInfo) {
    try {
      // 获取菜单按钮位置信息，增加延迟
      let menuButtonInfo;
      try {
        // 延迟获取菜单按钮信息，避免过早调用
        setTimeout(() => {
          try {
            menuButtonInfo = wx.getMenuButtonBoundingClientRect();
          } catch (err) {
            // 使用默认值
            menuButtonInfo = {
              top: systemInfo.statusBarHeight + 8,
              height: 32,
              width: 87
            };
          }

          // 重新计算导航栏高度
          this.recalculateNavBarHeight(systemInfo, menuButtonInfo);
        }, 100);

        // 先使用默认值
        menuButtonInfo = {
          top: systemInfo.statusBarHeight + 8,
          height: 32,
          width: 87
        };
      } catch (err) {
        // 使用默认值
        menuButtonInfo = {
          top: systemInfo.statusBarHeight + 8,
          height: 32,
          width: 87
        };
      }

      // 计算导航栏高度
      const statusBarHeight = systemInfo.statusBarHeight || 20;
      const navBarHeight = menuButtonInfo ?
        (menuButtonInfo.top - statusBarHeight) * 2 + menuButtonInfo.height + statusBarHeight :
        statusBarHeight + 44;

      // 更新全局数据
      this.globalData.systemInfo = systemInfo;
      this.globalData.navBarHeight = navBarHeight;
      this.globalData.statusBarHeight = statusBarHeight;
      this.globalData.systemInfoReady = true;

      // 通知页面系统信息已准备好
      this.notifySystemInfoReady();
    } catch (e) {
      // 保持默认值
      this.globalData.systemInfoReady = true;
    }
  },

  // 重新计算导航栏高度
  recalculateNavBarHeight(systemInfo, menuButtonInfo) {
    try {
      const statusBarHeight = systemInfo.statusBarHeight || 20;
      const navBarHeight = menuButtonInfo ?
        (menuButtonInfo.top - statusBarHeight) * 2 + menuButtonInfo.height + statusBarHeight :
        statusBarHeight + 44;

      // 更新全局数据
      this.globalData.navBarHeight = navBarHeight;
      this.globalData.statusBarHeight = statusBarHeight;

      // 通知页面系统信息已更新
      this.notifySystemInfoReady();
    } catch (e) {
      // 保持默认值
    }
  },

  // 通知页面系统信息已准备好
  notifySystemInfoReady() {
    // 可以在这里触发自定义事件或回调
    // 让需要系统信息的页面知道数据已准备好
  },

  // 获取系统信息的便捷方法
  getGlobalSystemInfo() {
    return {
      systemInfo: this.globalData.systemInfo,
      navBarHeight: this.globalData.navBarHeight,
      statusBarHeight: this.globalData.statusBarHeight,
      ready: this.globalData.systemInfoReady
    };
  }
})
