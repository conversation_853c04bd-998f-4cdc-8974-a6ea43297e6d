/**index.wxss**/
.page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 32rpx 32rpx;
  box-sizing: border-box;
  height: 100%;
}

/* 顶部主要功能卡片 */
.main-cards {
  width: 100%;
  display: flex;
  gap: 24rpx;
  margin-top: 8rpx;
  margin-bottom: 24rpx;
  box-sizing: border-box;
}

.main-card {
  flex: 1;
  height: 180rpx;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.create-card {
  background-color: #00CC66;
  color: white;
}

.clothing-card {
  background-color: #4080FF;
  color: white;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
  position: relative;
  display: inline-block;
}

.card-subtitle {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 工具部分 */
.tools-section {
  width: 100%;
  margin-top: 16rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-indicator {
  width: 6rpx;
  height: 28rpx;
  background-color: #00CC66;
  border-radius: 3rpx;
  margin-right: 12rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #191919;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  margin-bottom: 16rpx;
}

.tool-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.tool-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  margin-bottom: 12rpx;
  background-size: 50% 50%;
  background-position: center;
  background-repeat: no-repeat;
  transition: transform 0.2s ease;
}

.tool-item:active .tool-icon,
.navigator-hover .tool-icon {
  transform: scale(0.95);
}

/* 导航组件的hover样式 */
.navigator-hover {
  background-color: transparent;
  opacity: 0.9;
}

.palette-icon {
  background-color: #00CC66;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-2a8 8 0 100-16 8 8 0 000 16zm-5-8h2a3 3 0 016 0h2a5 5 0 00-10 0z'/%3E%3C/svg%3E");
}

.colorblind-icon {
  background-color: #4080FF;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z'/%3E%3C/svg%3E");
}

.contrast-icon {
  background-color: #f94346;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5-9h10v2H7z'/%3E%3C/svg%3E");
}

.gradient-icon {
  background-color: #a67cf3;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M2 12h2v9H2v-9zm18 0h2v9h-2v-9zm-4-9h2v18h-2V3zm-4 0h2v18h-2V3zM6 3h2v18H6V3z'/%3E%3C/svg%3E");
}

.wallpaper-icon {
  background-color: #FF9500;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2zm0 2v12h16V6H4zm7 9l-3 4h8l-2.5-3-1.5 2-1-3z'/%3E%3Ccircle cx='6.5' cy='8.5' r='1.5'/%3E%3C/svg%3E");
}

.converter-icon {
  background-color: #FF9500;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9-4.03-9-9-9zm0 16c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7z'/%3E%3Cpath d='M13 7h-2v5.414l3.293 3.293 1.414-1.414L13 11.586z'/%3E%3C/svg%3E");
}

.traditional-color-icon {
  background-color: #f94346;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z'/%3E%3Cpath d='M12 6l-6 6h12l-6-6zm0 2.83L14.17 11H9.83L12 8.83zM7 14h10v2H7z'/%3E%3C/svg%3E");
}

.ambient-light-icon {
  background-color: #FF6B8B;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 7c-2.76 0-5 2.24-5 5s2.24 5 5 5 5-2.24 5-5-2.24-5-5-5zm0 8c-1.65 0-3-1.35-3-3s1.35-3 3-3 3 1.35 3 3-1.35 3-3 3zm0-13C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z'/%3E%3C/svg%3E");
}

.about-icon {
  background-color: #4080FF;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z'/%3E%3C/svg%3E");
}

.tone-generator-icon {
  background-color: #FF9500;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9c.83 0 1.5-.67 1.5-1.5 0-.39-.15-.74-.39-1.01-.23-.26-.38-.61-.38-.99 0-.83.67-1.5 1.5-1.5H16c2.76 0 5-2.24 5-5 0-4.42-4.03-8-9-8zm-5.5 9c-.83 0-1.5-.67-1.5-1.5S5.67 9 6.5 9 8 9.67 8 10.5 7.33 12 6.5 12zm3-4C8.67 8 8 7.33 8 6.5S8.67 5 9.5 5s1.5.67 1.5 1.5S10.33 8 9.5 8zm5 0c-.83 0-1.5-.67-1.5-1.5S13.67 5 14.5 5s1.5.67 1.5 1.5S15.33 8 14.5 8zm3 4c-.83 0-1.5-.67-1.5-1.5S16.67 9 17.5 9s1.5.67 1.5 1.5-.67 1.5-1.5 1.5z'/%3E%3C/svg%3E");
}

.image-picker-icon {
  background-color: #00CC66;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9-4.03-9-9-9zm0 16c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7zm3-8h-2V7h-2v4H7v2h4v4h2v-4h4v-2z'/%3E%3C/svg%3E");
}

.skin-tone-icon {
  background-color: #00CC66;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Ccircle cx='12' cy='12' r='10'/%3E%3Ccircle cx='9' cy='10' r='1.5' fill='%2300CC66'/%3E%3Ccircle cx='15' cy='10' r='1.5' fill='%2300CC66'/%3E%3Cpath d='M8 15c0 2.21 1.79 4 4 4s4-1.79 4-4' stroke='%2300CC66' stroke-width='2' fill='none' stroke-linecap='round'/%3E%3C/svg%3E");
}

.tool-name {
  font-size: 24rpx;
  color: #333333;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

/* Hot角标容器 */
.tool-icon-container {
  position: relative;
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto 12rpx;
}

.tool-icon-container .tool-icon {
  width: 100%;
  height: 100%;
  margin: 0;
}

/* Hot角标样式 */
.hot-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: linear-gradient(135deg, #FF4757 0%, #FF3742 100%);
  color: white;
  font-size: 18rpx;
  font-weight: bold;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.4);
  z-index: 10;
  transform: scale(0.9);
  animation: hotPulse 2s ease-in-out infinite;
}

@keyframes hotPulse {
  0%, 100% {
    transform: scale(0.9);
  }
  50% {
    transform: scale(1);
  }
}

/* 推荐角标样式 */
.recommend-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  color: white;
  font-size: 18rpx;
  font-weight: bold;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.4);
  z-index: 10;
  transform: scale(0.9);
  animation: recommendPulse 3s ease-in-out infinite;
}

@keyframes recommendPulse {
  0%, 100% {
    transform: scale(0.9);
  }
  50% {
    transform: scale(1);
  }
}

/* 幸运签入口样式 - 纯SVG图标按钮 */
.lucky-float-btn {
  position: fixed;
  right: 32rpx;
  bottom: 80rpx;
  width: 70rpx;
  height: 210rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 999;
  background: transparent;
  border: none;
  animation: luckyBreath 2.5s ease-in-out infinite;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.lucky-float-btn:active {
  transform: scale(0.95) translateY(-5rpx);
}

/* lucky-content 样式已移除，直接使用SVG图标 */

.lucky-svg-icon {
  width: 70rpx;
  height: 210rpx;
  background: transparent;
  border: none;
  filter: drop-shadow(0 5rpx 10rpx rgba(0, 0, 0, 0.18));
  animation: luckyShake 8.8s infinite;
}

/* 光晕效果已移除，使用纯SVG图标 */

/* 闪烁装饰样式已移除，使用纯SVG图标 */

@keyframes luckyBreath {
  0%, 100% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-10rpx) scale(1.05);
  }
}

@keyframes glowBreath {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
    filter: blur(10rpx);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
    filter: blur(14rpx);
  }
}

/* 幸运签图标抖动动画 - 8.8秒一次，更大幅度，更丝滑 */
@keyframes luckyShake {
  0%, 95%, 100% {
    transform: translateX(0);
  }
  95.2% {
    transform: translateX(-6rpx);
  }
  95.4% {
    transform: translateX(6rpx);
  }
  95.6% {
    transform: translateX(-5rpx);
  }
  95.8% {
    transform: translateX(5rpx);
  }
  96% {
    transform: translateX(-4rpx);
  }
  96.2% {
    transform: translateX(4rpx);
  }
  96.4% {
    transform: translateX(-3rpx);
  }
  96.6% {
    transform: translateX(3rpx);
  }
  96.8% {
    transform: translateX(-2rpx);
  }
  97% {
    transform: translateX(2rpx);
  }
  97.2% {
    transform: translateX(-1rpx);
  }
  97.4% {
    transform: translateX(1rpx);
  }
  97.6% {
    transform: translateX(-0.5rpx);
  }
  97.8% {
    transform: translateX(0.5rpx);
  }
}

/* 幸运签弹窗样式 */
.lucky-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.lucky-modal.show {
  opacity: 1;
  visibility: visible;
}

.lucky-card-container {
  width: 540rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: cardSlideIn 0.4s ease-out;
  position: relative;
  z-index: 10000;
}

@keyframes cardSlideIn {
  from {
    transform: translateY(100rpx) scale(0.9);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

/* 全屏粒子特效 */
.fullscreen-particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  z-index: 9990;
  opacity: 0;
  transition: opacity 0.8s ease-out;
}

.fullscreen-particles.active {
  opacity: 1;
  animation: particlesAppear 0.8s ease-out 0.3s forwards;
}

.fullscreen-particles .particle {
  position: absolute;
  width: 8rpx;
  height: 8rpx;
  background: #FFD700;
  border-radius: 50%;
  animation: particleFloat 4s infinite ease-in-out;
  box-shadow: 0 0 10rpx rgba(255, 215, 0, 0.6);
}

/* 粒子位置分布 - 全屏随机分布 */
.fullscreen-particles .particle-1 { top: 10%; left: 8%; animation-delay: 0s; }
.fullscreen-particles .particle-2 { top: 15%; right: 12%; animation-delay: 0.3s; }
.fullscreen-particles .particle-3 { top: 25%; left: 20%; animation-delay: 0.6s; }
.fullscreen-particles .particle-4 { top: 35%; right: 25%; animation-delay: 0.9s; }
.fullscreen-particles .particle-5 { top: 45%; left: 15%; animation-delay: 1.2s; }
.fullscreen-particles .particle-6 { top: 55%; right: 18%; animation-delay: 1.5s; }
.fullscreen-particles .particle-7 { top: 65%; left: 30%; animation-delay: 1.8s; }
.fullscreen-particles .particle-8 { top: 75%; right: 35%; animation-delay: 2.1s; }
.fullscreen-particles .particle-9 { top: 85%; left: 25%; animation-delay: 2.4s; }
.fullscreen-particles .particle-10 { top: 20%; left: 60%; animation-delay: 2.7s; }
.fullscreen-particles .particle-11 { top: 30%; right: 45%; animation-delay: 3s; }
.fullscreen-particles .particle-12 { top: 40%; left: 70%; animation-delay: 0.2s; }
.fullscreen-particles .particle-13 { top: 50%; right: 55%; animation-delay: 0.5s; }
.fullscreen-particles .particle-14 { top: 60%; left: 80%; animation-delay: 0.8s; }
.fullscreen-particles .particle-15 { top: 70%; right: 65%; animation-delay: 1.1s; }
.fullscreen-particles .particle-16 { top: 80%; left: 90%; animation-delay: 1.4s; }
.fullscreen-particles .particle-17 { top: 5%; left: 45%; animation-delay: 1.7s; }
.fullscreen-particles .particle-18 { top: 90%; right: 8%; animation-delay: 2s; }
.fullscreen-particles .particle-19 { top: 12%; right: 75%; animation-delay: 2.3s; }
.fullscreen-particles .particle-20 { top: 95%; left: 50%; animation-delay: 2.6s; }
.fullscreen-particles .particle-21 { top: 18%; left: 35%; animation-delay: 2.9s; }
.fullscreen-particles .particle-22 { top: 28%; right: 30%; animation-delay: 3.2s; }
.fullscreen-particles .particle-23 { top: 38%; left: 5%; animation-delay: 3.5s; }
.fullscreen-particles .particle-24 { top: 48%; right: 10%; animation-delay: 3.8s; }
.fullscreen-particles .particle-25 { top: 58%; left: 95%; animation-delay: 4.1s; }
.fullscreen-particles .particle-26 { top: 68%; right: 85%; animation-delay: 0.1s; }
.fullscreen-particles .particle-27 { top: 78%; left: 40%; animation-delay: 0.4s; }
.fullscreen-particles .particle-28 { top: 88%; right: 50%; animation-delay: 0.7s; }
.fullscreen-particles .particle-29 { top: 8%; left: 75%; animation-delay: 1.0s; }
.fullscreen-particles .particle-30 { top: 92%; right: 25%; animation-delay: 1.3s; }

/* 粒子动画 */
@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0) scale(1) rotate(0deg);
    opacity: 0.6;
  }
  25% {
    transform: translateY(-20rpx) scale(1.2) rotate(90deg);
    opacity: 1;
  }
  50% {
    transform: translateY(-15rpx) scale(0.8) rotate(180deg);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-25rpx) scale(1.1) rotate(270deg);
    opacity: 1;
  }
}

@keyframes particlesAppear {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* 原生alert样式的提示框 */
.native-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease;
}

.native-modal.show {
  opacity: 1;
  visibility: visible;
}

.native-modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
}

.native-modal-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 540rpx;
  background: #fafafa;
  border-radius: 12rpx;
  overflow: hidden;
  animation: nativeModalSlideIn 0.25s ease-out;
}

.native-modal-content {
  background: #fafafa;
}

.native-modal-body {
  padding: 48rpx 48rpx 32rpx;
  text-align: center;
  background: #fafafa;
}

.native-modal-text {
  font-size: 34rpx;
  color: #000000;
  line-height: 1.4;
  font-weight: 400;
  word-wrap: break-word;
}

.native-modal-footer {
  display: flex;
  border-top: 1rpx solid #d5d5d6;
  background: #fafafa;
}

.native-modal-btn {
  flex: 1;
  height: 106rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 34rpx;
  font-weight: 400;
  background: #fafafa;
  position: relative;
  transition: background-color 0.1s ease;
}

.native-modal-btn:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  width: 1rpx;
  height: 100%;
  background: #d5d5d6;
}

.native-modal-btn.cancel {
  color: #000000;
}

.native-modal-btn.primary {
  color: #576b95;
  font-weight: 500;
}

.native-modal-btn:active {
  background: #ececec;
}

@keyframes nativeModalSlideIn {
  from {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0;
  }
  to {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
}