Component({
  properties: {
    colors: {
      type: Array,
      value: []
    },
    title: {
      type: String,
      value: '春日樱语'
    },
    subTitle: {
      type: String,
      value: '@KALA配色'
    },
    topBackgroundColor: {
      type: String,
      value: '#FFF1F1'
    },
    bottomBackgroundColor: {
      type: String,
      value: '#FFFFFF'
    },
    fontColor: {
      type: String,
      value: '#E89EC3'
    }
  },

  data: {
    canvasWidth: 1200, // 增加Canvas宽度，参考P02模板
    canvasHeight: 1599, // 按比例缩放 1200 * (2132/1600)
    svgDataUrls: [] // 存储生成的SVG Data URLs
  },

  lifetimes: {
    attached() {
      // 组件实例进入页面节点树时执行
      console.log('P04自定义色卡组件已加载');
    },

    ready() {
      // 在组件在视图层布局完成后执行
      console.log('P04自定义色卡组件布局完成，开始生成色卡');
      // 生成SVG Data URLs
      this.generateSVGDataUrls();
      // 延迟一段时间后开始生成，确保DOM已准备好
      setTimeout(() => {
        this.generateColorCard();
      }, 300);
    }
  },

  observers: {
    'colors': function(colors) {
      // 当颜色数组变化时，重新生成SVG Data URLs
      if (colors && colors.length > 0) {
        this.generateSVGDataUrls();
      }
    }
  },

  methods: {
    // 生成SVG Data URLs
    generateSVGDataUrls() {
      try {
        const { colors } = this.properties;
        if (!colors || !Array.isArray(colors) || colors.length === 0) {
          console.warn('generateSVGDataUrls: 颜色数据无效或为空:', colors);
          this.setData({ svgDataUrls: [] });
          return;
        }

        console.log('开始生成SVG Data URLs，颜色数组:', colors);

        // 引入P04 SVG模板工具类
        const P04SvgTemplates = require('../../utils/p04SvgTemplates');

        // 使用工具类生成SVG Data URLs
        const svgDataUrls = P04SvgTemplates.generatePetalDataURLs(colors);

        // 验证生成结果
        if (!svgDataUrls || !Array.isArray(svgDataUrls)) {
          console.error('generateSVGDataUrls: SVG Data URLs生成失败');
          this.setData({ svgDataUrls: [] });
          return;
        }

        this.setData({ svgDataUrls });
        console.log('SVG Data URLs生成完成:', svgDataUrls.length);

      } catch (error) {
        console.error('生成SVG Data URLs失败:', error);
        this.setData({ svgDataUrls: [] });
      }
    },

    // 花瓣点击事件
    onPetalTap(e) {
      const { index, color } = e.currentTarget.dataset;
      console.log(`点击了第${index + 1}个花瓣，颜色: ${color}`);

      // 触发自定义事件
      this.triggerEvent('petalTap', {
        index,
        color
      });
    },

    // 获取设备像素比
    getDevicePixelRatio() {
      try {
        const deviceInfo = wx.getDeviceInfo();
        return deviceInfo.pixelRatio || 2;
      } catch (error) {
        console.warn('获取设备像素比失败，使用默认值:', error);
        return 2; // 默认值
      }
    },

    async generateColorCard() {
      try {
        const { colors, title, subTitle, topBackgroundColor, bottomBackgroundColor, fontColor } = this.properties;

        console.log('P04开始生成色卡，属性:', { colors, title, topBackgroundColor, bottomBackgroundColor, fontColor });

        // 检查必要的属性
        if (!colors || colors.length === 0) {
          console.warn('春日樱语模板：颜色数组为空');
          return;
        }

        // 直接使用SVG合成图片
        await this.generateImageFromSVG();

      } catch (err) {
        console.error('生成P04样式自定义色卡失败:', err);
        this.triggerEvent('generated', { path: '' });
      }
    },

    async generateImageFromSVG() {
      try {
        const { colors, title, subTitle, topBackgroundColor, bottomBackgroundColor, fontColor } = this.properties;

        console.log('P04开始使用本地SVG绘制，属性:', { colors, title, topBackgroundColor, bottomBackgroundColor, fontColor });

        // 获取Canvas节点
        const query = wx.createSelectorQuery().in(this);
        const canvas = await new Promise((resolve, reject) => {
          query.select('#colorCardCanvas')
            .fields({ node: true, size: true })
            .exec((res) => {
              console.log('P04 Canvas查询结果:', res);
              if (res && res[0] && res[0].node) {
                resolve(res[0]);
              } else {
                reject(new Error('P04 Canvas节点获取失败'));
              }
            });
        });

        const canvasNode = canvas.node;
        const ctx = canvasNode.getContext('2d');

        // 保存Canvas节点到组件数据中，供其他方法使用
        this.setData({
          canvasNode: canvasNode
        });

        // 设置Canvas尺寸
        const { canvasWidth, canvasHeight } = this.data;
        const dpr = this.getDevicePixelRatio();

        canvasNode.width = canvasWidth * dpr;
        canvasNode.height = canvasHeight * dpr;
        ctx.scale(dpr, dpr);

        // 使用本地SVG文件绘制
        await this.drawWithLocalSVG(ctx, canvasWidth, canvasHeight, colors, title, subTitle, topBackgroundColor, bottomBackgroundColor, fontColor);

        // 保存Canvas
        setTimeout(() => {
          this.saveCanvas(canvasNode);
          // 清理Canvas上下文，释放内存
          this.cleanupCanvas(canvasNode, ctx);
        }, 100);

      } catch (err) {
        console.error('本地SVG绘制失败:', err);
        this.triggerEvent('generated', { path: '' });
      }
    },

    // 使用本地SVG文件绘制完整色卡
    async drawWithLocalSVG(ctx, width, height, colors, title, subTitle, topBackgroundColor, bottomBackgroundColor, fontColor) {
      // 绘制背景
      ctx.fillStyle = '#FFFFFF';
      ctx.fillRect(0, 0, width, height);

      // 绘制上半部分背景 - 按比例 1052/2132
      const topHeight = Math.floor(height * (1052 / 2132));
      ctx.fillStyle = topBackgroundColor;
      ctx.fillRect(0, 0, width, topHeight);

      // 绘制下半部分背景
      ctx.fillStyle = bottomBackgroundColor;
      ctx.fillRect(0, topHeight, width, height - topHeight);

      // 绘制主标题 - 居中对齐并上移位置
      const titleX = width / 2; // 水平居中
      const titleY = Math.floor(height * (300 / 2132)); // 调整位置，为副标题留出空间
      const titleFontSize = Math.floor(width * (188 / 1600));

      ctx.fillStyle = fontColor;
      ctx.font = `bold ${titleFontSize}px Arial, sans-serif`;
      ctx.textAlign = 'center'; // 改为居中对齐
      ctx.textBaseline = 'top';
      ctx.fillText(title, titleX, titleY);

      // 绘制副标题 - 在主标题下方，位置再下移，字号再调小
      const subTitleY = Math.floor(height * (500 / 2132)); // 位置再下移
      const subTitleFontSize = Math.floor(width * (60 / 1600)); // 字号再调小

      ctx.fillStyle = fontColor;
      ctx.globalAlpha = 0.8; // 设置透明度
      ctx.font = `normal ${subTitleFontSize}px Arial, sans-serif`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'top';
      // 绘制副标题（只有当副标题不为空时才绘制）
      if (subTitle && subTitle !== '' && subTitle.trim().length > 0) {
        ctx.fillText(subTitle, titleX, subTitleY);
      }
      ctx.globalAlpha = 1.0; // 重置透明度

      // 计算色块位置
      const positions = this.calculatePositions(colors, 1600, 2132);

      // 绘制上半部分的花瓣形状色块 - 使用Canvas直接绘制
      for (let index = 0; index < colors.length; index++) {
        const pos = positions.topShapes[index];
        const color = colors[index];
        if (pos) {
          await this.drawCanvasShape(ctx, pos.x, pos.y, color, width, height, 'petal');
        }
      }

      // 绘制下半部分的颜色代码
      const codeFontSize = Math.floor(width * (42 / 1600));
      ctx.font = `bold ${codeFontSize}px Courier New, monospace`;
      ctx.fillStyle = '#7D7D7D';
      ctx.textAlign = 'left';
      ctx.textBaseline = 'top';

      for (let index = 0; index < colors.length; index++) {
        const pos = positions.colorCodes[index];
        const color = colors[index];
        if (pos) {
          const codeX = Math.floor(width * (pos.x / 1600));
          const codeY = Math.floor(height * (pos.y / 2132));
          ctx.fillText(color.toUpperCase(), codeX, codeY);
        }
      }

      // 绘制下半部分的圆形色块 - 使用Canvas直接绘制
      for (let index = 0; index < colors.length; index++) {
        const pos = positions.bottomShapes[index];
        const color = colors[index];
        if (pos) {
          await this.drawCanvasShape(ctx, pos.x, pos.y, color, width, height, 'circle');
        }
      }
    },

    // 计算所有元素的位置 - 参考P02模板的动态间距算法
    calculatePositions(colors, width, height) {
      // P04原始位置数据（4个颜色时的精确位置）
      const originalPositions = {
        topShapes: [
          { x: 132, y: 847 },
          { x: 442, y: 847 },
          { x: 752, y: 847 },
          { x: 1062, y: 847 }
        ],
        colorCodes: [
          { x: 245, y: 1379 },
          { x: 553, y: 1379 },
          { x: 868, y: 1379 },
          { x: 1169, y: 1379 }
        ],
        bottomShapes: [
          { x: 231, y: 1546 },
          { x: 543, y: 1546 },
          { x: 855, y: 1546 },
          { x: 1167, y: 1546 }
        ]
      };

      const result = {
        topShapes: [],
        colorCodes: [],
        bottomShapes: []
      };

      if (colors.length === 4) {
        // 4个颜色使用原始精确位置
        result.topShapes = originalPositions.topShapes.slice(0, 4);
        result.colorCodes = originalPositions.colorCodes.slice(0, 4);
        result.bottomShapes = originalPositions.bottomShapes.slice(0, 4);
      } else {
        // 其他数量使用动态居中算法 - 参考P02和P03模板
        const colorCount = colors.length;

        // 固定色块尺寸
        const petalSize = 406; // 花瓣形状尺寸
        const circleSize = 202; // 圆形尺寸

        // 计算可用宽度（留出边距）
        const margin = Math.floor(50 * width / 1600); // 左右边距
        const maxWidth = width - 2 * margin;

        let petalGap, circleGap, petalStartX, circleStartX;

        if (colorCount === 5) {
          // 5个颜色：使用紧凑布局避免溢出 - 参考P03模板
          console.log('P04处理5个颜色，使用紧凑布局');

          // 花瓣重叠布局
          const petalOverlap = Math.floor(petalSize * 0.2); // 20%重叠
          petalGap = -petalOverlap; // 负间距表示重叠
          const totalPetalWidth = colorCount * petalSize + (colorCount - 1) * petalGap;

          // 如果仍然溢出，增加重叠
          if (totalPetalWidth > maxWidth) {
            const extraOverlap = Math.floor((totalPetalWidth - maxWidth) / (colorCount - 1));
            petalGap -= extraOverlap;
          }

          const finalPetalWidth = colorCount * petalSize + (colorCount - 1) * petalGap;
          petalStartX = (width - finalPetalWidth) / 2;

          // 圆形也使用紧凑间距，与花瓣布局保持一致
          const minCircleGap = Math.floor(30 * width / 1600); // 最小间距，按比例缩放
          const maxCircleGap = Math.floor(80 * width / 1600); // 最大间距，避免过于分散

          const availableCircleWidth = maxWidth - colorCount * circleSize;
          circleGap = colorCount > 1 ? Math.floor(availableCircleWidth / (colorCount - 1)) : 0;
          circleGap = Math.max(circleGap, minCircleGap); // 保证最小间距
          circleGap = Math.min(circleGap, maxCircleGap); // 限制最大间距，保持紧凑

          const totalCircleWidth = colorCount * circleSize + (colorCount - 1) * circleGap;
          circleStartX = (width - totalCircleWidth) / 2;

          console.log(`5个颜色紧凑布局 - 花瓣重叠: ${Math.abs(petalGap)}px, 花瓣总宽度: ${finalPetalWidth}, 圆形间距: ${circleGap}, 圆形总宽度: ${totalCircleWidth}`);

        } else {
          // 2-3个颜色：正常间距布局
          const maxGap = Math.floor(200 * width / 1600); // 最大间距限制

          // 花瓣间距
          const availablePetalWidth = maxWidth - colorCount * petalSize;
          petalGap = colorCount > 1 ? Math.floor(availablePetalWidth / (colorCount - 1)) : 0;
          petalGap = Math.min(petalGap, maxGap); // 限制最大间距
          petalGap = Math.max(petalGap, 50); // 最小间距

          // 圆形间距
          const availableCircleWidth = maxWidth - colorCount * circleSize;
          circleGap = colorCount > 1 ? Math.floor(availableCircleWidth / (colorCount - 1)) : 0;
          circleGap = Math.min(circleGap, maxGap); // 限制最大间距
          circleGap = Math.max(circleGap, 50); // 最小间距

          // 计算起始位置
          const totalPetalWidth = colorCount * petalSize + (colorCount - 1) * petalGap;
          const totalCircleWidth = colorCount * circleSize + (colorCount - 1) * circleGap;
          petalStartX = (width - totalPetalWidth) / 2;
          circleStartX = (width - totalCircleWidth) / 2;

          console.log(`P04正常布局 - 颜色数量: ${colorCount}, 花瓣间距: ${petalGap}, 圆形间距: ${circleGap}`);
        }

        // 生成位置
        for (let i = 0; i < colorCount; i++) {
          // 花瓣形状位置
          const petalX = petalStartX + i * (petalSize + petalGap);
          result.topShapes.push({ x: petalX, y: 847 });

          // 圆形位置
          const circleX = circleStartX + i * (circleSize + circleGap);
          result.bottomShapes.push({ x: circleX, y: 1546 });

          // 颜色代码位置（与花瓣垂直对齐）
          const codeX = petalX + petalSize / 2 - 50; // 花瓣中心位置，减去文字宽度的一半
          result.colorCodes.push({ x: codeX, y: 1379 });
        }
      }

      return result;
    },

    // 使用Canvas直接绘制形状 - 基于04A.svg.html和04B.svg.html
    async drawCanvasShape(ctx, x, y, color, canvasWidth, canvasHeight, shapeType) {
      try {
        console.log(`Canvas直接绘制${shapeType}形状，位置:(${x}, ${y})，颜色:${color}`);

        ctx.save();

        // 转换坐标到Canvas比例
        const canvasX = Math.floor(canvasWidth * (x / 1600));
        const canvasY = Math.floor(canvasHeight * (y / 2132));

        // 移动到目标位置
        ctx.translate(canvasX, canvasY);

        // 计算缩放比例
        const scaleX = canvasWidth / 1600;
        const scaleY = canvasHeight / 2132;
        ctx.scale(scaleX, scaleY);

        // 设置填充颜色
        ctx.fillStyle = color;

        if (shapeType === 'petal') {
          // 绘制花瓣形状 - 基于04A.svg.html
          this.drawPetalPath(ctx);
        } else {
          // 绘制圆形形状 - 基于04B.svg.html
          this.drawCirclePath(ctx);
        }

        ctx.restore();

      } catch (err) {
        console.error(`Canvas绘制${shapeType}形状失败:`, err);
        // 使用简化备用绘制方法
        this.drawFallbackShape(ctx, x, y, color, canvasWidth, canvasHeight, shapeType);
      }
    },

    // 绘制花瓣形状路径 - 基于04A.svg.html完整路径
    drawPetalPath(ctx) {
      ctx.beginPath();
      // 外圈花瓣路径
      ctx.moveTo(0, 203);
      ctx.bezierCurveTo(0, 188.42931, 5.31923, 175.30931, 14.16028, 165.43744);
      ctx.bezierCurveTo(9.77002, 152.9337, 9.87649, 138.77682, 15.45245, 125.31526);
      ctx.bezierCurveTo(21.02842, 111.8537, 30.96355, 101.76799, 42.90941, 96.03089);
      ctx.bezierCurveTo(43.63832, 82.79886, 49.15429, 69.76036, 59.45732, 59.45732);
      ctx.bezierCurveTo(69.76036, 49.15429, 82.79886, 43.63832, 96.03089, 42.90941);
      ctx.bezierCurveTo(101.76799, 30.96355, 111.8537, 21.02842, 125.31526, 15.45245);
      ctx.bezierCurveTo(138.77682, 9.87649, 152.9337, 9.77002, 165.43744, 14.16028);
      ctx.bezierCurveTo(175.30931, 5.31923, 188.42931, 0, 203, 0);
      ctx.bezierCurveTo(217.57069, 0, 230.69069, 5.31923, 240.56256, 14.16028);
      ctx.bezierCurveTo(253.0663, 9.77002, 267.22318, 9.87649, 280.68472, 15.45245);
      ctx.bezierCurveTo(294.1463, 21.02842, 304.23203, 30.96355, 309.96912, 42.90941);
      ctx.bezierCurveTo(323.20114, 43.63832, 336.23965, 49.15429, 346.54266, 59.45732);
      ctx.bezierCurveTo(356.8457, 69.76036, 362.36169, 82.79886, 363.09058, 96.03089);
      ctx.bezierCurveTo(375.03644, 101.76799, 384.97159, 111.8537, 390.54755, 125.31526);
      ctx.bezierCurveTo(396.1235, 138.77682, 396.22998, 152.9337, 391.83972, 165.43744);
      ctx.bezierCurveTo(400.68076, 175.30931, 406, 188.42931, 406, 203);
      ctx.bezierCurveTo(406, 217.57069, 400.68076, 230.69069, 391.83972, 240.56256);
      ctx.bezierCurveTo(396.22998, 253.0663, 396.1235, 267.22318, 390.54755, 280.68472);
      ctx.bezierCurveTo(384.97159, 294.1463, 375.03644, 304.23203, 363.09058, 309.96912);
      ctx.bezierCurveTo(362.36169, 323.20114, 356.8457, 336.23965, 346.54266, 346.54266);
      ctx.bezierCurveTo(336.23965, 356.8457, 323.20114, 362.36169, 309.96912, 363.09058);
      ctx.bezierCurveTo(304.23203, 375.03644, 294.1463, 384.97159, 280.68472, 390.54755);
      ctx.bezierCurveTo(267.22318, 396.1235, 253.0663, 396.22998, 240.56256, 391.83972);
      ctx.bezierCurveTo(230.69069, 400.68076, 217.57069, 406, 203, 406);
      ctx.bezierCurveTo(188.42931, 406, 175.30931, 400.68076, 165.43744, 391.83972);
      ctx.bezierCurveTo(152.9337, 396.22998, 138.77682, 396.1235, 125.31526, 390.54755);
      ctx.bezierCurveTo(111.8537, 384.97159, 101.76799, 375.03644, 96.03089, 363.09058);
      ctx.bezierCurveTo(82.79886, 362.36169, 69.76036, 356.8457, 59.45732, 346.54266);
      ctx.bezierCurveTo(49.15429, 336.23965, 43.63832, 323.20114, 42.90941, 309.96912);
      ctx.bezierCurveTo(30.96355, 304.23203, 21.02842, 294.1463, 15.45245, 280.68472);
      ctx.bezierCurveTo(9.87649, 267.22318, 9.77002, 253.0663, 14.16028, 240.56256);
      ctx.bezierCurveTo(5.31923, 230.69069, 0, 217.57069, 0, 203);
      ctx.closePath();

      // 中圈花瓣路径
      ctx.moveTo(67.9426, 67.9426);
      ctx.bezierCurveTo(58.28485, 77.60036, 52.98909, 89.75069, 52.05534, 102.142);
      ctx.bezierCurveTo(40.96458, 107.74664, 31.76575, 117.289, 26.53901, 129.90747);
      ctx.bezierCurveTo(21.31227, 142.52592, 21.06936, 155.77797, 24.94863, 167.58337);
      ctx.bezierCurveTo(16.84691, 177.00563, 12, 189.34187, 12, 203);
      ctx.bezierCurveTo(12, 216.65813, 16.84691, 228.99437, 24.94863, 238.41663);
      ctx.bezierCurveTo(21.06936, 250.22203, 21.31227, 263.47406, 26.53901, 276.09253);
      ctx.bezierCurveTo(31.76575, 288.711, 40.96458, 298.25336, 52.05534, 303.858);
      ctx.bezierCurveTo(52.98909, 316.24933, 58.28485, 328.39963, 67.9426, 338.0574);
      ctx.bezierCurveTo(77.60036, 347.71515, 89.75069, 353.01089, 102.142, 353.94467);
      ctx.bezierCurveTo(107.74664, 365.0354, 117.289, 374.23425, 129.90747, 379.461);
      ctx.bezierCurveTo(142.52592, 384.68774, 155.77797, 384.93063, 167.58337, 381.05136);
      ctx.bezierCurveTo(177.00563, 389.15308, 189.34187, 394, 203, 394);
      ctx.bezierCurveTo(216.65813, 394, 228.99437, 389.15308, 238.41663, 381.05136);
      ctx.bezierCurveTo(250.22203, 384.93063, 263.47406, 384.68774, 276.09253, 379.461);
      ctx.bezierCurveTo(288.711, 374.23425, 298.25336, 365.0354, 303.858, 353.94467);
      ctx.bezierCurveTo(316.24933, 353.01089, 328.39963, 347.71515, 338.0574, 338.0574);
      ctx.bezierCurveTo(347.71515, 328.39963, 353.01089, 316.24933, 353.94467, 303.858);
      ctx.bezierCurveTo(365.0354, 298.25336, 374.23425, 288.711, 379.461, 276.09253);
      ctx.bezierCurveTo(384.68774, 263.47406, 384.93063, 250.22203, 381.05136, 238.41663);
      ctx.bezierCurveTo(389.15308, 228.99437, 394, 216.65813, 394, 203);
      ctx.bezierCurveTo(394, 189.34187, 389.15308, 177.00563, 381.05136, 167.58337);
      ctx.bezierCurveTo(384.93063, 155.77797, 384.68774, 142.52592, 379.461, 129.90747);
      ctx.bezierCurveTo(374.23425, 117.289, 365.0354, 107.74664, 353.94467, 102.142);
      ctx.bezierCurveTo(353.01089, 89.75069, 347.71515, 77.60036, 338.0574, 67.9426);
      ctx.bezierCurveTo(328.39963, 58.28485, 316.24933, 52.98909, 303.858, 52.05534);
      ctx.bezierCurveTo(298.25336, 40.96458, 288.711, 31.76575, 276.09253, 26.53901);
      ctx.bezierCurveTo(263.47406, 21.31227, 250.22203, 21.06936, 238.41663, 24.94863);
      ctx.bezierCurveTo(228.99437, 16.84691, 216.65813, 12, 203, 12);
      ctx.bezierCurveTo(189.34187, 12, 177.00563, 16.84691, 167.58337, 24.94863);
      ctx.bezierCurveTo(155.77797, 21.06936, 142.52592, 21.31227, 129.90747, 26.53901);
      ctx.bezierCurveTo(117.289, 31.76575, 107.74664, 40.96458, 102.142, 52.05534);
      ctx.bezierCurveTo(89.75069, 52.98909, 77.60036, 58.28485, 67.9426, 67.9426);
      ctx.closePath();

      // 内圈花瓣路径
      ctx.moveTo(23, 203);
      ctx.bezierCurveTo(23, 190.00795, 27.9339, 178.36151, 36.06736, 169.79503);
      ctx.bezierCurveTo(31.83128, 158.7681, 31.72984, 146.12007, 36.70168, 134.11699);
      ctx.bezierCurveTo(41.67353, 122.11389, 50.68876, 113.24211, 61.48134, 108.44025);
      ctx.bezierCurveTo(61.78754, 96.63161, 66.53401, 84.90755, 75.72078, 75.72078);
      ctx.bezierCurveTo(84.90755, 66.53401, 96.63161, 61.78754, 108.44025, 61.48134);
      ctx.bezierCurveTo(113.24211, 50.68876, 122.11389, 41.67353, 134.11699, 36.70168);
      ctx.bezierCurveTo(146.12007, 31.72984, 158.7681, 31.83128, 169.79503, 36.06736);
      ctx.bezierCurveTo(178.36151, 27.9339, 190.00795, 23, 203, 23);
      ctx.bezierCurveTo(215.99205, 23, 227.63849, 27.9339, 236.20497, 36.06736);
      ctx.bezierCurveTo(247.2319, 31.83128, 259.87994, 31.72984, 271.88303, 36.70168);
      ctx.bezierCurveTo(283.88611, 41.67353, 292.75787, 50.68876, 297.55975, 61.48134);
      ctx.bezierCurveTo(309.36838, 61.78754, 321.09247, 66.53401, 330.27924, 75.72078);
      ctx.bezierCurveTo(339.46597, 84.90755, 344.21246, 96.63161, 344.51865, 108.44025);
      ctx.bezierCurveTo(355.31125, 113.24211, 364.32648, 122.11389, 369.29831, 134.11699);
      ctx.bezierCurveTo(374.27017, 146.12007, 374.16873, 158.7681, 369.93265, 169.79503);
      ctx.bezierCurveTo(378.0661, 178.36151, 383, 190.00795, 383, 203);
      ctx.bezierCurveTo(383, 215.99205, 378.0661, 227.63849, 369.93265, 236.20497);
      ctx.bezierCurveTo(374.16873, 247.2319, 374.27017, 259.87994, 369.29831, 271.88303);
      ctx.bezierCurveTo(364.32648, 283.88611, 355.31125, 292.75787, 344.51865, 297.55975);
      ctx.bezierCurveTo(344.21246, 309.36838, 339.46597, 321.09247, 330.27924, 330.27924);
      ctx.bezierCurveTo(321.09247, 339.46597, 309.36838, 344.21246, 297.55975, 344.51865);
      ctx.bezierCurveTo(292.75787, 355.31125, 283.88611, 364.32648, 271.88303, 369.29831);
      ctx.bezierCurveTo(259.87994, 374.27017, 247.2319, 374.16873, 236.20497, 369.93265);
      ctx.bezierCurveTo(227.63849, 378.0661, 215.99205, 383, 203, 383);
      ctx.bezierCurveTo(190.00795, 383, 178.36151, 378.0661, 169.79503, 369.93265);
      ctx.bezierCurveTo(158.7681, 374.16873, 146.12007, 374.27017, 134.11699, 369.29831);
      ctx.bezierCurveTo(122.11389, 364.32648, 113.24211, 355.31125, 108.44025, 344.51865);
      ctx.bezierCurveTo(96.63161, 344.21246, 84.90755, 339.46597, 75.72078, 330.27924);
      ctx.bezierCurveTo(66.53401, 321.09247, 61.78754, 309.36838, 61.48134, 297.55975);
      ctx.bezierCurveTo(50.68876, 292.75787, 41.67353, 283.88611, 36.70168, 271.88303);
      ctx.bezierCurveTo(31.72984, 259.87994, 31.83128, 247.2319, 36.06736, 236.20497);
      ctx.bezierCurveTo(27.9339, 227.63849, 23, 215.99205, 23, 203);
      ctx.closePath();

      ctx.fill();
    },

    // 绘制圆形形状路径 - 基于04B.svg.html完整路径
    drawCirclePath(ctx) {
      ctx.beginPath();
      // 上方花瓣
      ctx.moveTo(101, 80);
      ctx.bezierCurveTo(92.05381, 59.91061, 79, 49.17206, 79, 29.17206);
      ctx.bezierCurveTo(79, 18.77206, 83.4, 0, 101, 0);
      ctx.bezierCurveTo(118.6, 0, 123, 18.77206, 123, 29.17206);
      ctx.bezierCurveTo(123, 49.17206, 109.76714, 59.83183, 101, 80);
      ctx.closePath();

      // 下方花瓣
      ctx.moveTo(101, 122);
      ctx.bezierCurveTo(109.94619, 142.0894, 123, 152.82794, 123, 172.82794);
      ctx.bezierCurveTo(123, 183.22794, 118.6, 202, 101, 202);
      ctx.bezierCurveTo(83.4, 202, 79, 183.22794, 79, 172.82794);
      ctx.bezierCurveTo(79, 152.82794, 92.23286, 142.16818, 101, 122);
      ctx.closePath();

      // 右方花瓣
      ctx.moveTo(172.82794, 79);
      ctx.bezierCurveTo(183.22794, 79, 202, 83.4, 202, 101);
      ctx.bezierCurveTo(202, 118.6, 183.22794, 123, 172.82794, 123);
      ctx.bezierCurveTo(152.82794, 123, 142.16818, 109.76714, 122, 101);
      ctx.bezierCurveTo(142.0894, 92.05381, 152.82794, 79, 172.82794, 79);
      ctx.closePath();

      // 左方花瓣
      ctx.moveTo(29.17206, 123);
      ctx.bezierCurveTo(18.77206, 123, 0, 118.6, 0, 101);
      ctx.bezierCurveTo(0, 83.4, 18.77206, 79, 29.17206, 79);
      ctx.bezierCurveTo(49.17206, 79, 59.83183, 92.23286, 80, 101);
      ctx.bezierCurveTo(59.91061, 109.94619, 49.17206, 123, 29.17206, 123);
      ctx.closePath();

      // 对角线花瓣 - 右上
      ctx.moveTo(115.84924, 86.15076);
      ctx.bezierCurveTo(123.72868, 65.6195, 122.09154, 48.79576, 136.23367, 34.65363);
      ctx.bezierCurveTo(143.58759, 27.29972, 159.9727, 17.13714, 172.41779, 29.58221);
      ctx.bezierCurveTo(184.86287, 42.02729, 174.70029, 58.41241, 167.34637, 65.76633);
      ctx.bezierCurveTo(153.20424, 79.90846, 136.3096, 78.08901, 115.84924, 86.15076);
      ctx.closePath();

      // 对角线花瓣 - 左下
      ctx.moveTo(86.15076, 115.84924);
      ctx.bezierCurveTo(78.27132, 136.38051, 79.90846, 153.20424, 65.76633, 167.34637);
      ctx.bezierCurveTo(58.41241, 174.70029, 42.02729, 184.86287, 29.58221, 172.41779);
      ctx.bezierCurveTo(17.13714, 159.9727, 27.29972, 143.58759, 34.65363, 136.23367);
      ctx.bezierCurveTo(48.79576, 122.09154, 65.6904, 123.91099, 86.15076, 115.84924);
      ctx.closePath();

      // 对角线花瓣 - 右下
      ctx.moveTo(172.41779, 172.41779);
      ctx.bezierCurveTo(159.9727, 184.86287, 143.58759, 174.70029, 136.23367, 167.34637);
      ctx.bezierCurveTo(122.09154, 153.20424, 123.91099, 136.3096, 115.84924, 115.84924);
      ctx.bezierCurveTo(136.38051, 123.72868, 153.20424, 122.09154, 167.34637, 136.23367);
      ctx.bezierCurveTo(174.70029, 143.58759, 184.86287, 159.9727, 172.41779, 172.41779);
      ctx.closePath();

      // 对角线花瓣 - 左上
      ctx.moveTo(29.58221, 29.58221);
      ctx.bezierCurveTo(42.02729, 17.13714, 58.41241, 27.29972, 65.76633, 34.65363);
      ctx.bezierCurveTo(79.90846, 48.79576, 78.08901, 65.6904, 86.15076, 86.15076);
      ctx.bezierCurveTo(65.6195, 78.27132, 48.79576, 79.90846, 34.65363, 65.76633);
      ctx.bezierCurveTo(27.29972, 58.41241, 17.13714, 42.02729, 29.58221, 29.58221);
      ctx.closePath();

      // 中心星形
      ctx.moveTo(85.29404, 107.50562);
      ctx.lineTo(91.21365, 101.00019);
      ctx.lineTo(85.29404, 94.49438);
      ctx.lineTo(94.07988, 94.07988);
      ctx.lineTo(94.49438, 85.29404);
      ctx.lineTo(100.99998, 91.21349);
      ctx.lineTo(107.50562, 85.29404);
      ctx.lineTo(107.92012, 94.07988);
      ctx.lineTo(116.70596, 94.49438);
      ctx.lineTo(110.78654, 101.00002);
      ctx.lineTo(116.70596, 107.50562);
      ctx.lineTo(107.92012, 107.92012);
      ctx.lineTo(107.50562, 116.70596);
      ctx.lineTo(101.00002, 110.78651);
      ctx.lineTo(94.49438, 116.70596);
      ctx.lineTo(94.07988, 107.92012);
      ctx.lineTo(85.29404, 107.50562);
      ctx.closePath();

      ctx.fill();
    },

    // 获取SVG内容 - 直接使用嵌入的SVG模板
    async loadLocalSVG(shapeType, color) {
      try {
        console.log(`获取${shapeType}的SVG内容，颜色: ${color}`);

        const svgTemplates = {
          petal: `<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="406px" height="406px" xmlns="http://www.w3.org/2000/svg">
  <g transform="matrix(1 0 0 1 -132 -847 )">
    <path d="M 0 203  C 0 188.429306530165  5.31923330473523 175.309313052531  14.1602760606074 165.43744353446  C 9.77001855299724 152.933704667457  9.87649189803466 138.776828733027  15.4524549002092 125.315263229886  C 21.0284178598501 111.853697829431  30.963552700174 101.76798688267  42.9094121632125 96.0308891821323  C 43.6383168135026 82.7988619464505  49.154287232142 69.7603596061195  59.4573234191306 59.4573234191306  C 69.7603596061194 49.1542872321419  82.7988619464504 43.6383168135024  96.0308891718327 42.9094119762043  C 101.76798688267 30.9635527001733  111.853697829431 21.0284178598501  125.315263229886 15.4524549002088  C 138.776828733452 9.8764918978581  152.933704668352 9.77001855299029  165.437443466647 14.1602760160363  C 175.309313064259 5.31923329997985  188.429306536678 0  203 0  C 217.570693469835 0  230.690686947469 5.31923330473485  240.56255646554 14.1602760606061  C 253.066295332542 9.77001855299681  267.223171266973 9.87649189803423  280.684736770113 15.4524549002085  C 294.146302170569 21.0284178598497  304.232013117329 30.9635527001737  309.969110817868 42.9094121632121  C 323.201138053549 43.6383168135028  336.23964039388 49.1542872321424  346.542676580869 59.4573234191312  C 356.845712767857 69.7603596061198  362.361683186497 82.7988619464507  363.090588023796 96.030889171833  C 375.036447299826 101.76798688267  384.971582140149 111.853697829432  390.54754509979 125.315263229887  C 396.123508102141 138.776828733453  396.229981447009 152.933704668352  391.839723983963 165.437443466647  C 400.68076670002 175.309313064259  406 188.429306536678  406 203  C 406 217.570693469835  400.680766695265 230.69068694747  391.839723939393 240.56255646554  C 396.229981447003 253.066295332543  396.123508101965 267.223171266973  390.547545099791 280.684736770113  C 384.97158214015 294.146302170568  375.036447299826 304.23201311733  363.090587836787 309.969110817867  C 362.361683186497 323.201138053549  356.845712767858 336.23964039388  346.542676580869 346.542676580869  C 336.23964039388 356.845712767858  323.201138053548 362.361683186498  309.969110828166 363.090588023795  C 304.232013117329 375.036447299826  294.146302170568 384.97158214015  280.684736770112 390.547545099791  C 267.223171266547 396.123508102142  253.066295331648 396.22998144701  240.562556533353 391.839723983964  C 230.690686935741 400.68076670002  217.570693463323 406  203 406  C 188.429306530165 406  175.309313052531 400.680766695265  165.43744353446 391.839723939393  C 152.933704667457 396.229981447002  138.776828733027 396.123508101965  125.315263229887 390.547545099791  C 111.853697829432 384.971582140149  101.767986882671 375.036447299826  96.0308891821331 363.090587836787  C 82.798861946451 362.361683186497  69.7603596061197 356.845712767857  59.4573234191311 346.542676580868  C 49.1542872321423 336.23964039388  43.6383168135029 323.201138053549  42.909411976205 309.969110828167  C 30.9635527001738 304.232013117329  21.0284178598504 294.146302170568  15.4524549002092 280.684736770113  C 9.87649189785846 267.223171266547  9.77001855299056 253.066295331647  14.1602760160365 240.562556533352  C 5.31923329998002 230.69068693574  0 217.570693463322  0 203  Z M 67.9426047933698 67.9426047933698  C 58.284850685179 77.6003589015603  52.9890949439605 89.7506835051641  52.0553375697142 102.142001620804  C 40.9645859701463 107.746632740142  31.7657480332908 117.289000858066  26.5390092903448 129.907464418268  C 21.3122704599433 142.525928189605  21.069358018694 155.777962310892  24.9486307727669 167.583380926084  C 16.8469118177733 177.005632222373  12.0000000000003 189.341873590581  12.0000000000005 203  C 12.0000000000005 216.658126409418  16.8469118177732 228.994367777627  24.9486302512401 238.416618902545  C 21.0693580186939 250.222037689108  21.3122704599429 263.474071810395  26.5390092903445 276.092535581732  C 31.7657478919946 288.710998800814  40.9645854727947 298.253366743932  52.0553364498241 303.857998330389  C 52.9890949137765 316.249316425583  58.2848506576559 328.399641070917  67.9426047933698 338.05739520663  C 77.6003589015605 347.715149314821  89.7506835051633 353.01090505604  102.142001620803 353.944662430286  C 107.746632740143 365.035414029853  117.289000858066 374.234251966709  129.907464418268 379.460990709655  C 142.525928189605 384.687729540057  155.777962310892 384.930641981306  167.583380926084 381.051369227233  C 177.005632222374 389.153088182227  189.341873590582 393.999999999999  203 393.999999999999  C 216.658126409419 393.999999999999  228.994367777627 389.153088182227  238.416618902545 381.05136974876  C 250.222037689108 384.930641981306  263.474071810394 384.687729540057  276.092535581731 379.460990709656  C 288.710998800814 374.234252108005  298.253366743932 365.035414527205  303.857998330388 353.944663550176  C 316.249316425583 353.010905086224  328.399641070916 347.715149342344  338.05739520663 338.05739520663  C 347.715149314821 328.39964109844  353.010905056039 316.249316494836  353.944662430286 303.857998379196  C 365.035414029853 298.253367259857  374.234251966709 288.710999141933  379.460990709656 276.092535581732  C 384.687729540057 263.474071810394  384.930641981306 250.222037689108  381.051369227233 238.416619073916  C 389.153088182227 228.994367777627  394 216.658126409419  394 203  C 394 189.341873590581  389.153088182227 177.005632222374  381.051369748761 167.583381097456  C 384.930641981306 155.777962310892  384.687729540057 142.525928189605  379.460990709656 129.907464418268  C 374.234252108006 117.289001199186  365.035414527205 107.746633256067  353.944663550176 102.142001669611  C 353.010905086224 89.7506835744162  347.715149342344 77.6003589290833  338.057395206631 67.9426047933696  C 328.39964109844 58.2848506851788  316.249316494836 52.9890949439605  303.857998379196 52.0553375697141  C 298.253367259858 40.9645859701458  288.710999141934 31.7657480332905  276.092535581732 26.5390092903442  C 263.474071810395 21.3122704599428  250.222037689108 21.0693580186936  238.416619073915 24.9486307727665  C 228.994367777627 16.8469118177731  216.658126409419 12.0000000000003  203 12.0000000000002  C 189.341873590581 12.0000000000001  177.005632222373 16.8469118177732  167.583381097455 24.9486302512398  C 155.777962310892 21.0693580186943  142.525928189605 21.3122704599432  129.907464418268 26.5390092903448  C 117.289001199186 31.7657478919944  107.746633256068 40.9645854727954  102.142001669612 52.0553364498249  C 89.7506835744163 52.9890949137765  77.6003589290835 58.2848506576559  67.9426047933698 67.9426047933698  Z M 22.9999999999998 203  C 23.0000000000002 190.007951900347  27.9339031995659 178.361513764212  36.0673615156222 169.795033715179  C 31.8312758491108 158.768098413648  31.7298425714879 146.120069538193  36.7016841479682 134.116982174284  C 41.6735257227076 122.113894814578  50.688756863058 113.242111988555  61.4813424661438 108.440256182542  C 61.7875350754418 96.6316165553152  66.5340140488676 84.9075447239745  75.7207793864211 75.7207793864211  C 84.9075447299175 66.5340140429246  96.6316165704844 61.7875350693008  108.440256240928 61.4813423050381  C 113.242112087686 50.6887567623235  122.113894881637 41.6735256949308  134.116982174283 36.7016841479684  C 146.120069538192 31.7298425714883  158.768098413647 31.8312758491115  169.795033743321 36.0673614469722  C 178.361513764212 27.9339031995664  190.007951900347 23.0000000000005  202.999999999999 23.0000000000007  C 215.992048099652 23.0000000000005  227.638486235787 27.9339031995662  236.20496628482 36.0673615156229  C 247.231901586352 31.8312758491114  259.879930461806 31.7298425714888  271.883017825715 36.7016841479691  C 283.886105185421 41.6735257227086  292.757888011444 50.6887568630588  297.559743817457 61.4813424661442  C 309.368383444684 61.7875350754425  321.092455276024 66.534014048868  330.279220613577 75.7207793864218  C 339.465985957074 84.9075447299179  344.212464930698 96.6316165704849  344.51865769496 108.440256240928  C 355.311243237676 113.242112087687  364.326474305068 122.113894881637  369.29831585203 134.116982174284  C 374.270157428511 146.120069538192  374.168724150888 158.768098413647  369.932638553027 169.79503374332  C 378.066096800433 178.361513764211  382.999999999999 190.007951900347  382.999999999999 203  C 382.999999999999 215.992048099653  378.066096800433 227.638486235788  369.932638484376 236.20496628482  C 374.168724150888 247.231901586352  374.270157428511 259.879930461806  369.29831585203 271.883017825716  C 364.326474277291 283.886105185423  355.31124313694 292.757888011445  344.518657533855 297.559743817457  C 344.212464924556 309.368383444684  339.465985951131 321.092455276024  330.279220613577 330.279220613577  C 321.092455270081 339.465985957074  309.368383429514 344.212464930698  297.559743759071 344.518657694961  C 292.757887912312 355.311243237675  283.886105118361 364.326474305068  271.883017825715 369.29831585203  C 259.879930461806 374.27015742851  247.231901586352 374.168724150887  236.204966256678 369.932638553027  C 227.638486235788 378.066096800433  215.992048099652 382.999999999999  203 382.999999999999  C 190.007951900347 382.999999999999  178.361513764212 378.066096800433  169.795033715179 369.932638484376  C 158.768098413647 374.168724150888  146.120069538193 374.270157428511  134.116982174283 369.298315852031  C 122.113894814577 364.326474277291  113.242111988554 355.31124313694  108.440256182542 344.518657533854  C 96.6316165553148 344.212464924556  84.9075447239746 339.465985951131  75.720779386421 330.279220613577  C 66.5340140429246 321.092455270081  61.7875350693009 309.368383429514  61.481342305038 297.559743759071  C 50.6887567623232 292.757887912312  41.6735256949303 283.886105118361  36.7016841479682 271.883017825715  C 31.7298425714879 259.879930461806  31.831275849111 247.231901586351  36.0673614469713 236.204966256678  C 27.9339031995657 227.638486235787  23.0000000000002 215.992048099652  22.9999999999998 203  Z " fill-rule="nonzero" fill="PLACEHOLDER_COLOR" stroke="none" transform="matrix(1 0 0 1 132 847 )" />
  </g>
</svg>`,
          circle: `<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="202px" height="202px" xmlns="http://www.w3.org/2000/svg">
  <g transform="matrix(1 0 0 1 -231 -1546 )">
    <path d="M 101 80  C 92.0538100572608 59.9106055929164  79 49.1720569210868  79 29.1720569210868  C 79 18.7720569210867  83.4000000000001 0  101 0  C 118.6 0  123 18.7720569210867  123 29.1720569210868  C 123 49.1720569210868  109.767141009056 59.8318240620956  101 80  Z M 101 122  C 109.946189942739 142.089394407084  123 152.827943078913  123 172.827943078913  C 123 183.227943078913  118.6 202  101 202  C 83.4000000000001 202  79 183.227943078913  79 172.827943078913  C 79 152.827943078913  92.2328589909444 142.168175937904  101 122  Z M 172.827943078913 79  C 183.227943078913 79  202 83.4000000000001  202 101  C 202 118.6  183.227943078913 123  172.827943078913 123  C 152.827943078913 123  142.168175937904 109.767141009056  122 101  C 142.089394407084 92.0538100572608  152.827943078913 79  172.827943078913 79  Z M 29.1720569210866 123  C 18.7720569210869 123  0 118.6  0 101  C 0 83.4000000000001  18.7720569210869 79  29.1720569210866 79  C 49.1720569210866 79  59.8318240620956 92.2328589909444  80 101  C 59.9106055929164 109.946189942739  49.1720569210866 123  29.1720569210866 123  Z M 115.849242404918 86.150757595083  C 123.728677845803 65.6194990056092  122.091540819946 48.7957608078464  136.233676443677 34.6536251841153  C 143.587586968017 27.2997146597752  159.972705550958 17.1371357512758  172.417784899842 29.5822151001591  C 184.862864248725 42.0272944490425  174.700285340225 58.4124130319835  167.346374815885 65.7663235563236  C 153.204239192154 79.9084591800545  136.309601233895 78.0890084843493  115.849242404918 86.150757595083  Z M 86.1507575950823 115.849242404918  C 78.2713221541962 136.380500994391  79.9084591800538 153.204239192154  65.7663235563227 167.346374815885  C 58.4124130319829 174.700285340225  42.0272944490416 184.862864248725  29.5822151001585 172.417784899842  C 17.1371357512753 159.972705550958  27.299714659775 143.587586968017  34.6536251841148 136.233676443677  C 48.7957608078459 122.091540819946  65.6903987661049 123.910991515651  86.1507575950823 115.849242404918  Z M 172.417784899841 172.417784899841  C 159.972705550957 184.862864248724  143.587586968016 174.700285340225  136.233676443676 167.346374815885  C 122.091540819945 153.204239192154  123.91099151565 136.309601233895  115.849242404917 115.849242404917  C 136.380500994391 123.728677845803  153.204239192154 122.091540819946  167.346374815885 136.233676443677  C 174.700285340225 143.587586968017  184.862864248724 159.972705550958  172.417784899841 172.417784899841  Z M 29.5822151001585 29.5822151001585  C 42.0272944490416 17.1371357512753  58.4124130319829 27.2997146597747  65.7663235563227 34.6536251841148  C 79.9084591800538 48.7957608078459  78.0890084843486 65.6903987661049  86.1507575950823 86.1507575950823  C 65.6194990056083 78.2713221541965  48.7957608078459 79.908459180054  34.6536251841148 65.766323556323  C 27.2997146597745 58.4124130319829  17.1371357512753 42.0272944490418  29.5822151001585 29.5822151001585  Z M 85.2940479473082 107.505618350207  L 91.213648257039 101.000193755086  L 85.2940479473082 94.4943816497935  L 94.0798797148418 94.0798784740903  L 94.4943816497935 85.2940479473079  L 100.999988315724 91.2134825918763  L 107.505618350207 85.2940479473082  L 107.920120318673 94.0798796578863  L 116.705952052692 94.4943816497937  L 110.786546851643 101.000020675004  L 116.705952052692 107.505618350207  L 107.920120285158 107.92012152591  L 107.505618350206 116.705952052692  L 101.000011684276 110.786517408124  L 94.4943816497935 116.705952052692  L 94.0798796813269 107.920120342114  L 85.2940479473082 107.505618350207  Z " fill-rule="nonzero" fill="PLACEHOLDER_COLOR" stroke="none" transform="matrix(1 0 0 1 231 1546 )" />
  </g>
</svg>`
        };

        const template = svgTemplates[shapeType];
        if (!template) {
          throw new Error(`SVG模板未找到: ${shapeType}`);
        }

        // 替换颜色占位符
        const svgContent = template.replace(/PLACEHOLDER_COLOR/g, color);
        console.log(`SVG内容生成成功，长度: ${svgContent.length}`);
        return svgContent;

      } catch (err) {
        console.error('获取SVG内容失败:', err);
        throw err;
      }
    },

    // 备用绘制方法
    drawFallbackShape(ctx, x, y, color, canvasWidth, canvasHeight, shapeType) {
      console.log(`使用备用方法绘制${shapeType}形状`);

      ctx.save();
      ctx.fillStyle = color;

      // 转换坐标到Canvas比例
      const canvasX = Math.floor(canvasWidth * (x / 1600)) + Math.floor(canvasWidth * (203 / 1600));
      const canvasY = Math.floor(canvasHeight * (y / 2132)) + Math.floor(canvasHeight * (203 / 2132));

      ctx.translate(canvasX, canvasY);

      if (shapeType === 'petal') {
        ctx.rotate(Math.PI / 4); // 45度旋转
        // 绘制简化的花瓣形状
        const radius = Math.floor(canvasWidth * (203 / 1600));
        ctx.beginPath();
        ctx.arc(0, 0, radius * 0.6, 0, 2 * Math.PI);
        ctx.fill();
      } else {
        // 绘制简化的圆形
        const radius = Math.floor(canvasWidth * (101 / 1600));
        ctx.beginPath();
        ctx.arc(0, 0, radius, 0, 2 * Math.PI);
        ctx.fill();
      }

      ctx.restore();
    },



    // 绘制圆角矩形
    drawRoundedRect(ctx, x, y, width, height, radius) {
      ctx.beginPath();
      ctx.moveTo(x + radius, y);
      ctx.lineTo(x + width - radius, y);
      ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
      ctx.lineTo(x + width, y + height - radius);
      ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
      ctx.lineTo(x + radius, y + height);
      ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
      ctx.lineTo(x, y + radius);
      ctx.quadraticCurveTo(x, y, x + radius, y);
      ctx.closePath();
      ctx.fill();
    },

    // 备用绘制方法 - 当SVG加载失败时使用
    drawFallbackCard(ctx, width, height) {
      const { colors, title, subTitle, topBackgroundColor, bottomBackgroundColor, fontColor } = this.properties;

      // 绘制背景
      ctx.fillStyle = '#FFFFFF';
      ctx.fillRect(0, 0, width, height);

      // 绘制上半部分背景
      const topHeight = Math.floor(height * (526 / 1066));
      ctx.fillStyle = topBackgroundColor;
      ctx.fillRect(0, 0, width, topHeight);

      // 绘制下半部分背景
      ctx.fillStyle = bottomBackgroundColor;
      ctx.fillRect(0, topHeight, width, height - topHeight);

      // 绘制标题
      const titleX = Math.floor(width * (212 / 800));
      const titleY = Math.floor(height * (188 / 1066));
      const titleFontSize = Math.floor(width * (94 / 800));

      ctx.fillStyle = fontColor;
      ctx.font = `bold ${titleFontSize}px Arial, sans-serif`;
      ctx.textAlign = 'left';
      ctx.textBaseline = 'top';
      ctx.fillText(title, titleX, titleY);

      // 绘制简化的色块
      this.drawSimpleShapes(ctx, colors, width, height);
    },

    // 绘制简化的形状
    drawSimpleShapes(ctx, colors, width, height) {
      if (!colors || colors.length === 0) return;

      // 上半部分简化色块
      const chipSize = Math.floor(width * (203 / 800));
      const chipY = Math.floor(height * (424 / 1066));
      const chipSpacing = Math.floor(width * (155 / 800));

      let startX = colors.length === 4 ?
        Math.floor(width * (66 / 800)) + chipSize / 2 :
        width / 2 - (colors.length - 1) * chipSpacing / 2;

      colors.forEach((color, index) => {
        const x = startX + index * chipSpacing;

        // 绘制旋转的圆角矩形
        ctx.save();
        ctx.fillStyle = color;
        ctx.translate(x, chipY);
        ctx.rotate(Math.PI / 4);
        ctx.fillRect(-chipSize/2, -chipSize/2, chipSize, chipSize);
        ctx.restore();
      });

      // 下半部分圆形色块和颜色代码
      const circleRadius = Math.floor(width * (50 / 800));
      const circleY = Math.floor(height * (774 / 1066));
      const codeY = Math.floor(height * (670 / 1066));
      const codeFontSize = Math.floor(width * (21 / 800));

      colors.forEach((color, index) => {
        const x = startX + index * chipSpacing;

        // 绘制圆形
        ctx.fillStyle = color;
        ctx.beginPath();
        ctx.arc(x, circleY, circleRadius, 0, 2 * Math.PI);
        ctx.fill();

        // 绘制颜色代码
        ctx.fillStyle = '#7D7D7D';
        ctx.font = `bold ${codeFontSize}px Arial, sans-serif`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'top';
        ctx.fillText(color.toUpperCase(), x, codeY);
      });
    },



    // 保存Canvas - 使用与P02、P03相同的方式
    async saveCanvas(canvasNode) {
      try {
        const { canvasWidth, canvasHeight } = this.data;

        const res = await new Promise((resolve, reject) => {
          wx.canvasToTempFilePath({
            canvas: canvasNode,
            x: 0,
            y: 0,
            width: canvasWidth,
            height: canvasHeight,
            destWidth: canvasWidth,
            destHeight: canvasHeight,
            fileType: 'png',
            quality: 1,
            success: resolve,
            fail: reject
          }, this);
        });

        console.log('P04色卡生成成功:', res.tempFilePath);
        this.triggerEvent('generated', { path: res.tempFilePath });
      } catch (err) {
        console.error('保存P04样式自定义色卡失败:', err);
        this.triggerEvent('generated', { path: '' });
      }
    },

    // 清理Canvas上下文，释放内存
    cleanupCanvas(canvasNode, ctx) {
      try {
        if (ctx && typeof ctx.clearRect === 'function') {
          // 清除Canvas内容
          ctx.clearRect(0, 0, canvasNode.width, canvasNode.height);
          // 重置变换矩阵
          if (typeof ctx.setTransform === 'function') {
            ctx.setTransform(1, 0, 0, 1, 0, 0);
          }
        }
        // 将Canvas尺寸设为最小，释放内存
        canvasNode.width = 1;
        canvasNode.height = 1;
      } catch (err) {
        console.warn('Canvas清理失败:', err);
      }
    }
  }
});
