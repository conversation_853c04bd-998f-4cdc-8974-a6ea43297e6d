// card-package/pages/customImageColorPicker/customImageColorPicker.js
// 自定义色卡编辑的独立一键图片取色页面
// 参考图片主题色提取页面的设计风格，但作为独立功能实现

const { SafeNavigation } = require('../../../utils/safeNavigation');
const { systemInfoCache } = require('../../utils/systemInfoCache');

Page({
  data: {
    imagePath: '',
    colors: [],
    colorsLoading: false,
    imageLoading: false,
    selectedColorIndex: -1,
    maxColors: 5,
    titleMarginTop: '0rpx',
    selectedCount: 0, // 选中颜色数量

    // 取色相关状态
    pickingMode: false,
    pickingIndex: -1,
    imageRect: null,

    // 放大镜相关
    magnifierVisible: false,
    currentColor: '#ffffff',
    imageInfo: null,

    // 图片缩放相关
    scale: 1,
    minScale: 1,
    maxScale: 5,
    translateX: 0,
    translateY: 0,

    // 取色点位置
    pickingX: 0,
    pickingY: 0,
    showCrosshair: false,

    // 放大镜位置
    targetX: 0,
    targetY: 0,
    magnifierX: 0,
    magnifierY: 0,
    magnificationRatio: 3,
    magnifierOffsetX: 0,
    magnifierOffsetY: 0,

    // 连接线样式（预计算的值）
    connectorLineWidth: 0,
    connectorLineRotation: 0,

    // 放大镜图片尺寸（预计算的值）
    magnifiedImageWidth: 0,
    magnifiedImageHeight: 0,

    // Canvas相关
    magnifierCanvas: null,
    magnifierCtx: null,
    offscreenCanvas: null,
    offscreenCtx: null,

    // 触摸交互状态
    lastTapTime: 0,
    isPanning: false,
    isScaling: false,
    isDragging: false,
    isZooming: false,
    showPanningTip: false, // 显示平移提示

    // 颜色过渡状态
    colorsTransitioning: false,

    // 拖拽相关
    dragIndex: -1,
    dragOffsetX: 0,
    dragStartX: 0,
    isDragging: false,
    dragStyle: '', // 预计算的拖拽样式
    // 颜色项位置信息
    colorItemRects: [],

    // 页面滚动控制
    pageScrollEnabled: true,

    // 触摸状态 - 参考图片主题色提取页面
    touchStartTime: 0,
    currentTouchPosition: null,
    isLongPress: false
  },

  onLoad(options) {
    // 页面加载标记
    this.isPageLoaded = true;

    // 取色状态标记
    this.isColorPicking = false;

    // 获取传入的参数
    const maxColors = parseInt(options.maxColors) || 5;

    // 从全局变量中获取图片路径，避免URL过长导致的跳转超时
    const app = getApp();
    const imagePath = (app.globalData && app.globalData.selectedImagePath) || '';

    console.log('onLoad options:', options);
    console.log('maxColors:', maxColors);
    console.log('imagePath from globalData:', imagePath);

    // 清理全局变量
    if (app.globalData) {
      delete app.globalData.selectedImagePath;
    }

    // 如果没有图片路径，直接返回上一页
    if (!imagePath) {
      console.warn('没有图片路径，返回上一页');
      wx.showToast({
        title: '没有选择图片',
        icon: 'none',
        duration: 1500
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    // 安全的setData调用
    this.safeSetData({
      maxColors,
      imagePath
    });

    // 如果有图片路径，标记需要提取颜色，但等待Canvas初始化完成
    if (imagePath) {
      this.safeSetData({
        imageLoading: true,
        colorsLoading: true
      });

      // 标记需要提取颜色，等待Canvas初始化完成后再执行
      this.needExtractColors = true;
    }

    // 获取导航栏高度
    this.getNavBarHeight();
  },

  onReady() {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '一键图片取色'
    });

    // 延迟初始化canvas，确保DOM完全渲染
    setTimeout(() => {
      this.initCanvases();
    }, 200);
  },

  onUnload() {
    // 页面卸载时清理资源
    this.isPageLoaded = false;
    this.cleanup();
  },

  // 安全的setData调用
  safeSetData(data, callback) {
    if (this.isPageValid()) {
      this.setData(data, callback);
    }
  },

  // 检查页面实例是否有效
  isPageValid() {
    return this.isPageLoaded && this.route;
  },

  // 清理资源
  cleanup() {
    console.log('清理页面资源');

    // 清理所有定时器
    if (this.touchTimer) {
      clearTimeout(this.touchTimer);
      this.touchTimer = null;
    }

    if (this.throttleTimer) {
      clearTimeout(this.throttleTimer);
      this.throttleTimer = null;
    }

    if (this.dragTimer) {
      clearTimeout(this.dragTimer);
      this.dragTimer = null;
    }

    // 清理Canvas资源
    this.magnifierCanvas = null;
    this.magnifierCtx = null;
    this.offscreenCanvas = null;
    this.offscreenCtx = null;
    this.colorAnalysisCanvas = null;
    this.colorAnalysisCtx = null;
    this.tempCanvas = null;
    this.tempCtx = null;

    // 清理触摸状态
    this.isColorPicking = false;
    this.isPanning = false;
    this.lastTouchX = null;
    this.lastTouchY = null;
    this.lastDistance = null;
    this.initialScale = null;

    console.log('页面资源清理完成');
  },

  // 获取导航栏高度（使用缓存）
  async getNavBarHeight() {
    try {
      // 使用异步方式获取系统信息
      const systemInfo = await systemInfoCache.initSystemInfoAsync();
      const statusBarHeight = systemInfo.statusBarHeight || 0;
      const titleBarHeight = 44; // 默认导航栏高度
      const totalHeight = statusBarHeight + titleBarHeight;

      // 进一步减少顶部间隙，让内容区域更紧贴导航栏
      this.safeSetData({
        titleMarginTop: `${Math.max(0, totalHeight - 60)}rpx` // 减少60rpx的间隙，让标题更靠近顶部
      });
    } catch (error) {
      console.error('获取导航栏高度失败:', error);
      // 使用默认值
      this.safeSetData({
        titleMarginTop: '0rpx'
      });
    }
  },

  // 初始化Canvas
  initCanvases() {
    if (!this.isPageValid()) {
      console.warn('页面实例无效，跳过Canvas初始化');
      return;
    }

    let canvasInitCount = 0;
    const totalCanvases = 2;

    const checkAllCanvasesReady = () => {
      canvasInitCount++;
      if (canvasInitCount === totalCanvases) {
        console.log('所有Canvas初始化完成');
        this.canvasReady = true;

        // 如果需要提取颜色，现在开始提取
        if (this.needExtractColors && this.data.imagePath) {
          console.log('Canvas准备就绪，开始提取颜色');
          this.needExtractColors = false;
          setTimeout(() => {
            this.extractColorsFromImage(this.data.imagePath);
          }, 100);
        }
      }
    };

    // 初始化颜色分析Canvas
    const query = wx.createSelectorQuery();
    query.select('#colorAnalysisCanvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (res && res[0] && res[0].node) {
          const canvas = res[0].node;
          const ctx = canvas.getContext('2d');

          // 设置Canvas尺寸（使用缓存的设备信息）
          const deviceInfo = systemInfoCache.getField('pixelRatio') || 2;
          canvas.width = 200 * deviceInfo;
          canvas.height = 200 * deviceInfo;
          ctx.scale(deviceInfo, deviceInfo);

          this.colorAnalysisCanvas = canvas;
          this.colorAnalysisCtx = ctx;

          console.log('颜色分析Canvas初始化成功');
          checkAllCanvasesReady();
        } else {
          console.error('颜色分析Canvas初始化失败');
          checkAllCanvasesReady();
        }
      });

    // 初始化临时Canvas
    query.select('#tempCanvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (res && res[0] && res[0].node) {
          const canvas = res[0].node;
          const ctx = canvas.getContext('2d');

          const deviceInfo = systemInfoCache.getField('pixelRatio') || 2;
          canvas.width = 200 * deviceInfo;
          canvas.height = 200 * deviceInfo;
          ctx.scale(deviceInfo, deviceInfo);

          this.tempCanvas = canvas;
          this.tempCtx = ctx;

          console.log('临时Canvas初始化成功');
          checkAllCanvasesReady();
        } else {
          console.error('临时Canvas初始化失败');
          checkAllCanvasesReady();
        }
      });
  },

  // 从图片中提取颜色
  extractColorsFromImage(imagePath) {
    if (!imagePath) {
      console.error('图片路径为空');
      this.handleColorExtractionError();
      return;
    }

    console.log('开始提取颜色，图片路径:', imagePath);

    this.safeSetData({
      colorsLoading: true
    });

    // 获取图片信息
    wx.getImageInfo({
      src: imagePath,
      success: (imageInfo) => {
        console.log('图片信息获取成功:', imageInfo);

        this.safeSetData({
          imageInfo: imageInfo
        });

        // 延迟创建canvas，避免阻塞
        setTimeout(() => {
          this.createCanvasForColorAnalysis(imageInfo);
        }, 100);
      },
      fail: (err) => {
        console.error('获取图片信息失败:', err);
        this.handleColorExtractionError();
      }
    });
  },

  // 创建Canvas进行颜色分析
  createCanvasForColorAnalysis(imageInfo) {
    if (!this.isPageValid()) {
      console.warn('页面实例无效，跳过颜色分析');
      return;
    }

    // 使用当前选择的算法提取颜色
    this.extractColors();
  },

  // 提取颜色的主方法
  async extractColors() {
    const { imagePath } = this.data;

    if (!imagePath) {
      console.error('图片路径为空');
      this.handleColorExtractionError();
      return;
    }

    console.log('开始提取颜色，使用原生算法');

    try {
      // 只使用原生算法提取颜色
      const colors = await this.extractColorsNative(imagePath);

      console.log('颜色提取完成:', colors);

      // 确保始终有5个颜色
      const finalColors = this.ensureFiveColors(colors);
      console.log('最终颜色:', finalColors);

      // 为每个颜色添加索引号和选中状态，避免在WXML中计算
      const colorsWithIndex = finalColors.map((color, index) => ({
        color: color,
        displayIndex: index + 1,
        selected: true // 默认全选
      }));

      this.safeSetData({
        colors: colorsWithIndex,
        colorsLoading: false
      });

      // 计算选中颜色数量
      this.updateSelectedCount();

      // 延迟获取颜色项位置信息
      setTimeout(() => {
        this.getColorItemRects();
      }, 100);
    } catch (error) {
      console.error('颜色提取失败:', error);
      this.handleColorExtractionError();
    }
  },

  // 原生算法提取颜色 - 参考图片主题色提取页面的实现
  async extractColorsNative(imagePath) {
    try {
      console.log('开始使用原生算法提取颜色，图片路径:', imagePath);

      const { canvas, ctx, imageData, smallImageInfo } = await this.prepareImageData(imagePath);
      if (!canvas || !ctx || !imageData || !smallImageInfo) {
        console.warn('Canvas方法失败，使用备用颜色提取方法');
        return this.extractColorsWithoutCanvas(imagePath);
      }

      console.log('图像数据准备成功，尺寸:', smallImageInfo.width, 'x', smallImageInfo.height);

      // 采样颜色数组
      const sampledColors = [];

      // 计算采样步长
      const samplePoints = 100;
      const stepX = Math.max(1, Math.floor(smallImageInfo.width / Math.sqrt(samplePoints)));
      const stepY = Math.max(1, Math.floor(smallImageInfo.height / Math.sqrt(samplePoints)));

      // 采样颜色
      for (let y = 0; y < smallImageInfo.height; y += stepY) {
        for (let x = 0; x < smallImageInfo.width; x += stepX) {
          const i = (y * smallImageInfo.width + x) * 4;

          // 获取RGB值
          const r = imageData.data[i];
          const g = imageData.data[i + 1];
          const b = imageData.data[i + 2];
          const a = imageData.data[i + 3];

          // 忽略透明像素
          if (a < 128) continue;

          // 转换为HSV
          const hsv = this.rgbToHsv(r, g, b);

          // 判断是否为背景区域（图像边缘）
          const isBackground =
            x < smallImageInfo.width * 0.1 ||
            x >= smallImageInfo.width * 0.9 ||
            y < smallImageInfo.height * 0.1 ||
            y >= smallImageInfo.height * 0.9;

          // 创建颜色对象
          sampledColors.push({
            r, g, b,
            h: hsv.h,
            s: hsv.s,
            v: hsv.v,
            isBackground
          });
        }
      }

      // 使用渐进式颜色提取算法
      let selectedColors = [];

      // 定义多个过滤级别，从严格到宽松
      const filterLevels = [
        { name: '严格', satMin: 20, valMin: 15, valMax: 90, threshold: 40 },
        { name: '中等', satMin: 10, valMin: 10, valMax: 95, threshold: 30 },
        { name: '宽松', satMin: 5, valMin: 5, valMax: 98, threshold: 25 },
        { name: '极宽松', satMin: 0, valMin: 3, valMax: 99, threshold: 20 }
      ];

      // 逐级尝试不同的过滤条件
      for (const level of filterLevels) {
        if ((selectedColors || []).length >= 5) break;

        console.log(`extractColorsNative: 尝试${level.name}过滤条件`);

        // 按色相分组
        const hueGroups = {};
        for (const color of (sampledColors || [])) {
          // 应用当前级别的过滤条件
          if (color.s < level.satMin || color.v < level.valMin || color.v > level.valMax) continue;

          // 计算色相组
          const hueGroup = Math.floor(color.h / 30);
          if (!hueGroups[hueGroup]) {
            hueGroups[hueGroup] = [];
          }
          hueGroups[hueGroup].push(color);
        }

        // 找出主要色相组
        const sortedHueGroups = Object.keys(hueGroups)
          .sort((a, b) => hueGroups[b].length - hueGroups[a].length);

        // 从每个主要色相组中选择一个代表色
        for (const hueGroup of sortedHueGroups) {
          if ((selectedColors || []).length >= 5) break;

          const colors = hueGroups[hueGroup];
          if (colors.length < 1) continue;

          // 计算平均RGB
          let sumR = 0, sumG = 0, sumB = 0;
          for (const color of colors) {
            sumR += color.r;
            sumG += color.g;
            sumB += color.b;
          }

          const avgColor = {
            r: Math.round(sumR / colors.length),
            g: Math.round(sumG / colors.length),
            b: Math.round(sumB / colors.length)
          };

          // 检查是否与已选颜色过于相似
          let isTooSimilar = false;
          for (const selected of (selectedColors || [])) {
            const distance = Math.sqrt(
              Math.pow(avgColor.r - selected.r, 2) +
              Math.pow(avgColor.g - selected.g, 2) +
              Math.pow(avgColor.b - selected.b, 2)
            );

            if (distance < level.threshold) {
              isTooSimilar = true;
              break;
            }
          }

          if (!isTooSimilar) {
            selectedColors.push(avgColor);
          }
        }

        console.log(`extractColorsNative: ${level.name}过滤后得到${selectedColors.length}个颜色`);
      }

      // 如果颜色不足5个，从图片中随机选取补足颜色
      if (selectedColors && selectedColors.length < 5) {
        selectedColors = this.fillColorsFromImage(selectedColors, sampledColors, 5);
      } else if (!selectedColors) {
        selectedColors = this.fillColorsFromImage([], sampledColors, 5);
      }

      // 转换为十六进制颜色代码，过滤无效颜色
      const validColors = (selectedColors || []).filter(color =>
        color && typeof color.r === 'number' && typeof color.g === 'number' && typeof color.b === 'number' &&
        !isNaN(color.r) && !isNaN(color.g) && !isNaN(color.b)
      );

      console.log('extractColorsNative: 有效颜色数量:', validColors.length, '/', selectedColors.length);

      if (validColors.length === 0) {
        console.warn('extractColorsNative: 没有有效颜色，返回默认颜色');
        return [
          '#D8DDE1', '#7F96B3', '#231A12', '#3A4250', '#6A8CAF'
        ];
      }

      return validColors.map(color => this.rgbToHex(color.r, color.g, color.b));
    } catch (error) {
      console.error('原生算法失败:', error);
      // 使用备用方法
      return this.extractColorsWithoutCanvas(imagePath);
    }
  },

  // 不使用Canvas的备用颜色提取方法
  extractColorsWithoutCanvas(imagePath) {
    console.log('使用备用颜色提取方法');

    // 基于图片路径生成一些伪随机但一致的颜色
    const colors = [];
    const baseColors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
      '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2'
    ];

    // 使用图片路径的哈希值来选择颜色，确保同一张图片总是得到相同的颜色
    let hash = 0;
    for (let i = 0; i < imagePath.length; i++) {
      const char = imagePath.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }

    // 基于哈希值选择5个不同的颜色
    for (let i = 0; i < 5; i++) {
      const index = Math.abs(hash + i * 7) % baseColors.length;
      colors.push(baseColors[index]);
    }

    return colors;
  },

  // HEX转RGB
  hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  },



  // 准备图像数据 - 参考图片主题色提取页面的实现
  async prepareImageData(imagePath) {
    console.log('prepareImageData 开始，图片路径:', imagePath);

    try {
      // 使用微信的图片信息API获取图片信息
      const imageInfo = await new Promise((resolve, reject) => {
        wx.getImageInfo({
          src: imagePath,
          success: resolve,
          fail: reject
        });
      });

      console.log('图片信息获取成功:', imageInfo);

      // 增加延迟确保页面稳定
      await new Promise(resolve => setTimeout(resolve, 100));

      // 检查Canvas是否已初始化
      if (!this.colorAnalysisCanvas || !this.colorAnalysisCtx) {
        console.error('Canvas未初始化，等待初始化完成...');

        // 如果Canvas未初始化，等待一段时间后重试
        if (!this.canvasReady) {
          let retryCount = 0;
          const maxRetries = 10;
          const retryInterval = 200;

          await new Promise((resolve) => {
            const retryCheck = () => {
              retryCount++;
              if (this.canvasReady && this.colorAnalysisCanvas && this.colorAnalysisCtx) {
                console.log('Canvas初始化完成，继续处理图像数据');
                resolve();
              } else if (retryCount < maxRetries) {
                setTimeout(retryCheck, retryInterval);
              } else {
                console.error('Canvas初始化超时');
                throw new Error('Canvas初始化超时');
              }
            };
            setTimeout(retryCheck, retryInterval);
          });
        } else {
          throw new Error('Canvas未初始化');
        }
      }

      console.log('Canvas节点获取成功');

      const canvas = this.colorAnalysisCanvas;
      const ctx = this.colorAnalysisCtx;

      // 验证上下文是否可用
      if (!ctx) {
        console.error('Canvas上下文获取失败');
        throw new Error('Canvas上下文获取失败');
      }

      // 设置更小的尺寸以提高性能
      const maxSize = 200;
      const scale = Math.min(maxSize / imageInfo.width, maxSize / imageInfo.height);
      const width = Math.round(imageInfo.width * scale);
      const height = Math.round(imageInfo.height * scale);

      // 设置Canvas尺寸
      const dpr = wx.getSystemInfoSync().pixelRatio;
      canvas.width = width * dpr;
      canvas.height = height * dpr;
      ctx.scale(dpr, dpr);

      // 创建图片对象
      const img = canvas.createImage();
      await new Promise((resolve, reject) => {
        img.onload = () => {
          console.log('图片加载到Canvas成功');
          resolve();
        };
        img.onerror = (error) => {
          console.error('图片加载到Canvas失败:', error);
          reject(error);
        };
        img.src = imagePath;
      });

      // 绘制图片到canvas
      ctx.drawImage(img, 0, 0, width, height);

      // 获取图片数据
      const imageData = ctx.getImageData(0, 0, width, height);
      console.log('图像数据获取成功，尺寸:', width, 'x', height, '数据长度:', imageData.data.length);

      return {
        canvas: { node: canvas },
        ctx,
        imageData,
        smallImageInfo: { width, height }
      };
    } catch (err) {
      console.error('准备图像数据失败:', err);
      // 抛出异常让上层处理，而不是直接返回空对象
      throw err;
    }
  },



  // 处理颜色提取错误
  handleColorExtractionError() {
    console.error('颜色提取失败，使用默认颜色');

    const defaultColors = ['#4A90E2', '#7ED321', '#F5A623', '#D0021B', '#9013FE'];

    // 为默认颜色添加索引号
    const colorsWithIndex = defaultColors.map((color, index) => ({
      color: color,
      displayIndex: index + 1
    }));

    this.safeSetData({
      colors: colorsWithIndex,
      colorsLoading: false
    });
  },



  // RGB转HEX - 支持多种输入格式
  rgbToHex(r, g, b) {
    // 如果第一个参数是字符串，解析rgb()格式
    if (typeof r === 'string') {
      const match = r.match(/rgb\((\d+),(\d+),(\d+)\)/);
      if (!match) return '#000000';

      r = parseInt(match[1]);
      g = parseInt(match[2]);
      b = parseInt(match[3]);
    }

    // 检查输入值是否有效
    if (typeof r !== 'number' || typeof g !== 'number' || typeof b !== 'number' ||
        isNaN(r) || isNaN(g) || isNaN(b)) {
      console.error('rgbToHex: 无效的RGB值', { r, g, b });
      // 返回默认颜色（灰色）
      return '#808080';
    }

    r = Math.max(0, Math.min(255, Math.round(r))).toString(16).padStart(2, '0');
    g = Math.max(0, Math.min(255, Math.round(g))).toString(16).padStart(2, '0');
    b = Math.max(0, Math.min(255, Math.round(b))).toString(16).padStart(2, '0');
    return `#${r}${g}${b}`.toUpperCase();
  },

  // 将RGB转换为HSV
  rgbToHsv(r, g, b) {
    // 检查输入值是否有效
    if (typeof r !== 'number' || typeof g !== 'number' || typeof b !== 'number' ||
        isNaN(r) || isNaN(g) || isNaN(b)) {
      console.error('rgbToHsv: 无效的RGB值', { r, g, b });
      // 返回默认HSV值（灰色）
      return { h: 0, s: 0, v: 50 };
    }

    r /= 255;
    g /= 255;
    b /= 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h, s, v = max;

    const d = max - min;
    s = max === 0 ? 0 : d / max;

    if (max === min) {
      h = 0; // 灰色
    } else {
      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
        case g: h = (b - r) / d + 2; break;
        case b: h = (r - g) / d + 4; break;
      }
      h /= 6;
    }

    return { h: h * 360, s: s * 100, v: v * 100 };
  },

  // 从图片中随机选取颜色来补足到指定数量
  fillColorsFromImage(selectedColors, sampledColors, targetCount) {
    if (!sampledColors || sampledColors.length === 0) {
      // 如果没有采样颜色，使用默认颜色
      return this.addDefaultColors(selectedColors);
    }

    // 创建一个副本，避免修改原数组
    const result = [...(selectedColors || [])];

    // 使用渐进式过滤策略来补足颜色
    const filterLevels = [
      { name: '严格', satMin: 20, valMin: 15, valMax: 90, threshold: 30 },
      { name: '中等', satMin: 10, valMin: 10, valMax: 95, threshold: 25 },
      { name: '宽松', satMin: 5, valMin: 5, valMax: 98, threshold: 20 },
      { name: '极宽松', satMin: 0, valMin: 3, valMax: 99, threshold: 15 },
      { name: '无过滤', satMin: 0, valMin: 0, valMax: 100, threshold: 10 }
    ];

    // 逐级尝试不同的过滤条件
    for (const level of filterLevels) {
      if (result.length >= targetCount) break;

      console.log(`fillColorsFromImage: 尝试${level.name}过滤条件，当前已有${result.length}个颜色`);

      // 过滤出当前级别的有效颜色
      const validColors = sampledColors.filter(color => {
        // 首先检查颜色对象是否有效
        if (!color || typeof color.r !== 'number' || typeof color.g !== 'number' || typeof color.b !== 'number' ||
            isNaN(color.r) || isNaN(color.g) || isNaN(color.b)) {
          return false;
        }

        const hsv = this.rgbToHsv(color.r, color.g, color.b);
        return hsv.s >= level.satMin && hsv.v >= level.valMin && hsv.v <= level.valMax;
      });

      if (validColors.length === 0) continue;

      // 随机选择颜色补足到目标数量
      const maxAttempts = Math.min(validColors.length * 2, 100); // 防止无限循环
      let attempts = 0;

      while (result.length < targetCount && attempts < maxAttempts) {
        attempts++;

        // 随机选择一个颜色
        const randomIndex = Math.floor(Math.random() * validColors.length);
        const candidateColor = validColors[randomIndex];

        // 检查是否与已选颜色过于相似
        if (!this.isColorTooSimilar(candidateColor, result, level.threshold)) {
          result.push({
            r: candidateColor.r,
            g: candidateColor.g,
            b: candidateColor.b
          });
        }
      }

      console.log(`fillColorsFromImage: ${level.name}过滤后补足到${result.length}个颜色`);
    }

    // 如果仍然不足，使用默认颜色补足
    if (result.length < targetCount) {
      return this.addDefaultColors(result);
    }

    return result;
  },

  // 检查颜色是否与已选颜色过于相似
  isColorTooSimilar(color, selectedColors, threshold = 30) {
    for (const selected of (selectedColors || [])) {
      const distance = this.calculateColorDistance(color, selected);
      if (distance < threshold) {
        return true;
      }
    }
    return false;
  },

  // 计算两个RGB颜色之间的欧几里得距离
  calculateColorDistance(color1, color2) {
    // 检查颜色对象是否有效
    if (!color1 || !color2 ||
        typeof color1.r !== 'number' || typeof color1.g !== 'number' || typeof color1.b !== 'number' ||
        typeof color2.r !== 'number' || typeof color2.g !== 'number' || typeof color2.b !== 'number' ||
        isNaN(color1.r) || isNaN(color1.g) || isNaN(color1.b) ||
        isNaN(color2.r) || isNaN(color2.g) || isNaN(color2.b)) {
      return Infinity; // 返回无穷大表示颜色无效
    }

    return Math.sqrt(
      Math.pow(color1.r - color2.r, 2) +
      Math.pow(color1.g - color2.g, 2) +
      Math.pow(color1.b - color2.b, 2)
    );
  },

  // 添加默认颜色（作为最后的备选方案）
  addDefaultColors(selectedColors) {
    const defaultColors = [
      { r: 216, g: 221, b: 225 },   // 浅灰色
      { r: 127, g: 150, b: 179 },   // 蓝灰色
      { r: 35, g: 26, b: 18 },      // 深棕色
      { r: 58, g: 66, b: 80 },      // 深灰蓝色
      { r: 106, g: 140, b: 175 }    // 中蓝色
    ];

    // 创建一个副本，避免修改原数组
    const result = [...(selectedColors || [])];

    // 使用渐进式相似度阈值添加默认颜色
    const thresholds = [20, 15, 10, 5, 0]; // 逐步降低相似度阈值

    for (const threshold of thresholds) {
      if (result.length >= 5) break;

      console.log(`addDefaultColors: 尝试相似度阈值${threshold}，当前已有${result.length}个颜色`);

      for (const color of defaultColors) {
        if (result.length >= 5) break;

        // 使用当前阈值检查颜色相似度
        if (!this.isColorTooSimilar(color, result, threshold)) {
          result.push(color);
          console.log(`addDefaultColors: 添加默认颜色 RGB(${color.r}, ${color.g}, ${color.b})`);
        }
      }
    }

    // 如果还是不够5个，强制添加剩余的默认颜色（不检查相似度）
    if (result.length < 5) {
      console.log(`addDefaultColors: 强制添加剩余默认颜色，当前${result.length}个`);

      for (const color of defaultColors) {
        if (result.length >= 5) break;

        // 检查是否已经存在
        const exists = result.some(existing =>
          existing.r === color.r && existing.g === color.g && existing.b === color.b
        );

        if (!exists) {
          result.push(color);
          console.log(`addDefaultColors: 强制添加默认颜色 RGB(${color.r}, ${color.g}, ${color.b})`);
        }
      }
    }

    // 转换为十六进制颜色代码
    return result.map(color => this.rgbToHex(color.r, color.g, color.b));
  },

  // 确保颜色数组有5个元素 - 参考图片主题色提取页面的实现
  ensureFiveColors(colors) {
    if (!colors || !Array.isArray(colors)) {
      console.log('ensureFiveColors: 输入无效，返回默认颜色');
      return [
        '#D8DDE1', '#7F96B3', '#231A12', '#3A4250', '#6A8CAF'
      ];
    }

    // 过滤无效颜色
    const validColors = colors.filter(color => {
      if (typeof color === 'string') {
        return /^#[0-9A-F]{6}$/i.test(color);
      }
      return false;
    });

    console.log(`ensureFiveColors: 有效颜色数量 ${validColors.length}/${colors.length}`);

    const result = [...(validColors || [])];

    // 如果颜色不足5个，使用默认颜色补充
    const defaultColors = [
      '#D8DDE1', '#7F96B3', '#231A12', '#3A4250', '#6A8CAF',
      '#4A90E2', '#7ED321', '#F5A623', '#D0021B', '#9013FE'
    ];

    // 使用渐进式相似度阈值添加默认颜色
    const thresholds = [30, 25, 20, 15, 10, 5, 0]; // 逐步降低相似度阈值

    for (const threshold of thresholds) {
      if (result.length >= 5) break;

      console.log(`ensureFiveColors: 尝试相似度阈值${threshold}，当前已有${result.length}个颜色`);

      for (const color of defaultColors) {
        if (result.length >= 5) break;

        // 检查是否与已有颜色过于相似
        let isTooSimilar = false;
        for (const existing of result) {
          const distance = this.calculateHexColorDistance(color, existing);
          if (distance < threshold) {
            isTooSimilar = true;
            break;
          }
        }

        if (!isTooSimilar) {
          result.push(color);
          console.log(`ensureFiveColors: 添加默认颜色 ${color}`);
        }
      }
    }

    // 如果还是不够5个，强制添加剩余的默认颜色（不检查相似度）
    if (result.length < 5) {
      console.log(`ensureFiveColors: 强制添加剩余默认颜色，当前${result.length}个`);

      for (const color of defaultColors) {
        if (result.length >= 5) break;

        if (!result.includes(color)) {
          result.push(color);
          console.log(`ensureFiveColors: 强制添加默认颜色 ${color}`);
        }
      }
    }

    // 如果超过5个，只取前5个
    const finalResult = result.slice(0, 5);
    console.log('ensureFiveColors: 最终颜色数组:', finalResult);

    return finalResult;
  },

  // 计算两个十六进制颜色之间的距离
  calculateHexColorDistance(color1, color2) {
    const rgb1 = this.hexToRgb(color1);
    const rgb2 = this.hexToRgb(color2);

    if (!rgb1 || !rgb2) return Infinity;

    return Math.sqrt(
      Math.pow(rgb1.r - rgb2.r, 2) +
      Math.pow(rgb1.g - rgb2.g, 2) +
      Math.pow(rgb1.b - rgb2.b, 2)
    );
  },

  // 选择颜色
  selectColor(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    this.safeSetData({
      selectedColorIndex: index
    });
  },

  // 复制颜色代码
  copyColorCode(e) {
    const color = e.currentTarget.dataset.color;
    wx.setClipboardData({
      data: color,
      success: () => {
        wx.showToast({
          title: '颜色代码已复制',
          icon: 'success',
          duration: 1000
        });
      }
    });
  },

  // 切换颜色选中状态
  toggleColorSelection(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    const colors = [...this.data.colors];

    if (colors[index]) {
      colors[index].selected = !colors[index].selected;
      this.safeSetData({
        colors: colors
      });

      // 更新选中颜色数量
      this.updateSelectedCount();
    }
  },

  // 更新选中颜色数量
  updateSelectedCount() {
    const selectedCount = this.data.colors.filter(item => item.selected).length;
    this.safeSetData({
      selectedCount: selectedCount
    });
  },



  // 图片加载完成
  handleImageLoad() {
    console.log('图片加载完成');

    this.safeSetData({
      imageLoading: false
    });

    // 获取图片区域信息，增加延迟确保图片完全渲染
    setTimeout(() => {
      this.getImageRect();
    }, 500);

    // 再次延迟获取，确保在不同设备上都能正确获取
    setTimeout(() => {
      if (!this.imageRect || this.imageRect.width === 0) {
        console.log('重新获取图片区域信息');
        this.getImageRect();
      }
    }, 1000);
  },

  // 图片加载错误
  handleImageError() {
    wx.showToast({
      title: '图片加载失败',
      icon: 'none'
    });
    this.safeSetData({
      imageLoading: false
    });
  },

  // 获取图片区域信息
  getImageRect() {
    if (!this.isPageValid()) {
      console.warn('页面实例无效，跳过获取图片区域');
      return;
    }

    const query = wx.createSelectorQuery().in(this);
    query.select('.preview-image').boundingClientRect();
    query.exec((res) => {
      if (res && res[0] && res[0].width > 0 && res[0].height > 0) {
        const containerRect = res[0];
        console.log('图片容器区域信息:', containerRect);

        // 计算图片在aspectFit模式下的实际显示区域
        const actualImageRect = this.calculateActualImageRect(containerRect);

        this.imageRect = actualImageRect;
        console.log('图片实际显示区域:', actualImageRect);

        // 同时设置到data中，供WXML使用
        this.safeSetData({
          imageRect: actualImageRect
        });

        // 验证图片区域是否有效
        if (actualImageRect.width > 0 && actualImageRect.height > 0) {
          console.log('图片区域获取成功，可以进行取色操作');
        } else {
          console.warn('图片区域无效，可能影响取色功能');
        }
      } else {
        console.error('无法获取图片区域信息，重试中...', res);
        // 重试获取图片区域
        setTimeout(() => {
          this.getImageRect();
        }, 300);
      }
    });
  },

  // 计算aspectFit模式下图片的实际显示区域
  calculateActualImageRect(containerRect) {
    const { imageInfo } = this.data;
    if (!imageInfo) {
      console.warn('图片信息不存在，无法计算图片区域');
      // 返回一个空的区域，避免后续操作出错
      return {
        left: 0,
        top: 0,
        right: 0,
        bottom: 0,
        width: 0,
        height: 0
      };
    }

    const containerWidth = containerRect.width;
    const containerHeight = containerRect.height;
    const imageWidth = imageInfo.width;
    const imageHeight = imageInfo.height;

    // 计算图片的宽高比
    const imageAspectRatio = imageWidth / imageHeight;
    const containerAspectRatio = containerWidth / containerHeight;

    let actualWidth, actualHeight, offsetX, offsetY;

    if (imageAspectRatio > containerAspectRatio) {
      // 图片较宽，以容器宽度为准
      actualWidth = containerWidth;
      actualHeight = containerWidth / imageAspectRatio;
      offsetX = 0;
      offsetY = (containerHeight - actualHeight) / 2;
    } else {
      // 图片较高，以容器高度为准
      actualHeight = containerHeight;
      actualWidth = containerHeight * imageAspectRatio;
      offsetX = (containerWidth - actualWidth) / 2;
      offsetY = 0;
    }

    return {
      left: containerRect.left + offsetX,
      top: containerRect.top + offsetY,
      right: containerRect.left + offsetX + actualWidth,
      bottom: containerRect.top + offsetY + actualHeight,
      width: actualWidth,
      height: actualHeight
    };
  },

  // 容器触摸开始 - 完全参考图片主题色提取页面的实现
  handleContainerTouchStart: async function(e) {
    // 如果没有图片信息或没有选中颜色，则不进行操作
    if (!this.data.imageInfo || this.data.selectedColorIndex < 0) {
      // 如果没有选中颜色，提示用户先选择一个颜色
      if (this.data.imageInfo && this.data.selectedColorIndex < 0) {
        wx.showToast({
          title: '请先选择要编辑的颜色',
          icon: 'none',
          duration: 1500
        });
      }
      return;
    }

    try {
      // 确保触摸事件有效
      if (!e.touches || !e.touches[0]) {
        console.error('无效的触摸事件');
        return;
      }

      const touch = e.touches[0];
      const x = touch.pageX || touch.clientX || 0;
      const y = touch.pageY || touch.clientY || 0;

      // 获取图片容器信息
      const query = wx.createSelectorQuery();
      const imageRect = await new Promise((resolve) => {
        query.select('.preview-image').boundingClientRect().exec((res) => {
          if (!res || !res[0]) {
            resolve(null);
          } else {
            resolve(res[0]);
          }
        });
      });

      if (!imageRect) {
        console.error('获取图片位置信息失败');
        return;
      }

      // 检查点击是否在图片内
      if (x < imageRect.left || x > imageRect.right || y < imageRect.top || y > imageRect.bottom) {
        console.log('点击位置在图片外部', x, y, imageRect);
        return;
      }

      // 获取图片的原始尺寸
      const { imageInfo } = this.data;
      const originalWidth = imageInfo.width;
      const originalHeight = imageInfo.height;

      // 计算图片在容器中的实际显示尺寸和位置（考虑aspectFit模式）
      let displayWidth, displayHeight, offsetX, offsetY;

      // 计算图片的宽高比
      const imageRatio = originalWidth / originalHeight;
      // 计算容器的宽高比
      const containerRatio = imageRect.width / imageRect.height;

      if (imageRatio > containerRatio) {
        // 图片较宽，宽度填满容器，高度居中
        displayWidth = imageRect.width;
        displayHeight = displayWidth / imageRatio;
        offsetX = 0;
        offsetY = (imageRect.height - displayHeight) / 2;
      } else {
        // 图片较高，高度填满容器，宽度居中
        displayHeight = imageRect.height;
        displayWidth = displayHeight * imageRatio;
        offsetX = (imageRect.width - displayWidth) / 2;
        offsetY = 0;
      }

      // 计算图片的实际显示区域
      const actualImageRect = {
        left: imageRect.left + offsetX,
        top: imageRect.top + offsetY,
        right: imageRect.left + offsetX + displayWidth,
        bottom: imageRect.top + offsetY + displayHeight,
        width: displayWidth,
        height: displayHeight
      };

      // 检查点击是否在图片的实际显示区域内
      if (x < actualImageRect.left || x > actualImageRect.right ||
          y < actualImageRect.top || y > actualImageRect.bottom) {
        console.log('点击位置在图片实际显示区域外部', x, y, actualImageRect);
        return;
      }

      // 保存图片实际显示区域信息和当前触摸位置
      this.safeSetData({
        imageRect: actualImageRect,
        touchStartTime: Date.now(),
        currentTouchPosition: { x, y }
      });

      // 延迟显示放大镜，模拟长按效果
      // 减少延迟时间，让用户更容易触发放大镜
      this.touchTimer = setTimeout(() => {
        // 显示放大镜
        this.showMagnifier(x, y, actualImageRect);

        // 设置长按状态，禁用页面滚动
        this.safeSetData({
          isLongPress: true,
          pageScrollEnabled: false
        });

        // 震动反馈
        if (wx.vibrateShort) {
          wx.vibrateShort({
            type: 'light'
          });
        }
      }, 100); // 减少到100ms延迟，让触摸移动更容易触发放大镜
    } catch (err) {
      console.error('触摸开始处理失败:', err);
    }
  },

  // 计算触摸位置并更新放大镜 - 参考图片主题色提取页面的实现
  calculateTouchPosition(touch, shouldPickColor = true) {
    const { imageInfo } = this.data;
    if (!imageInfo) {
      console.log('缺少图片信息');
      return;
    }

    try {
      // 获取图片节点信息和容器信息
      const query = wx.createSelectorQuery().in(this);
      query.select('.preview-image').boundingClientRect();
      query.select('.image-container').boundingClientRect();
      query.exec((res) => {
        if (!res || !res[0] || !res[1]) {
          console.error('获取图片或容器节点信息失败');
          return;
        }

        const imageNode = res[0];
        const containerNode = res[1];

        // 计算触摸点相对于容器的位置
        const touchX = touch.pageX - containerNode.left;
        const touchY = touch.pageY - containerNode.top;

        console.log('触摸点位置:', {
          touchX, touchY,
          containerWidth: containerNode.width,
          containerHeight: containerNode.height,
          imageWidth: imageNode.width,
          imageHeight: imageNode.height
        });

        // 计算图片的实际显示尺寸和偏移量
        const { width: originalWidth, height: originalHeight } = imageInfo;
        const containerWidth = containerNode.width;
        const containerHeight = containerNode.height;

        // 计算图片在aspectFit模式下的实际显示区域
        const imageAspectRatio = originalWidth / originalHeight;
        const containerAspectRatio = containerWidth / containerHeight;

        let displayWidth, displayHeight, baseOffsetX, baseOffsetY;

        if (imageAspectRatio > containerAspectRatio) {
          // 图片较宽，以容器宽度为准
          displayWidth = containerWidth;
          displayHeight = containerWidth / imageAspectRatio;
          baseOffsetX = 0;
          baseOffsetY = (containerHeight - displayHeight) / 2;
        } else {
          // 图片较高，以容器高度为准
          displayHeight = containerHeight;
          displayWidth = containerHeight * imageAspectRatio;
          baseOffsetX = (containerWidth - displayWidth) / 2;
          baseOffsetY = 0;
        }

        // 计算触摸点相对于图片显示区域的坐标
        let relativeX = touchX - baseOffsetX;
        let relativeY = touchY - baseOffsetY;

        // 首先限制触摸点在图片显示区域内
        relativeX = Math.max(0, Math.min(relativeX, displayWidth));
        relativeY = Math.max(0, Math.min(relativeY, displayHeight));

        console.log('图片显示计算:', {
          originalSize: { width: originalWidth, height: originalHeight },
          containerSize: { width: containerWidth, height: containerHeight },
          displaySize: { width: displayWidth, height: displayHeight },
          baseOffset: { x: baseOffsetX, y: baseOffsetY },
          relativeCoords: { x: relativeX, y: relativeY },
          boundedRelativeCoords: { x: relativeX, y: relativeY }
        });

        // 使用比例映射，确保精确性
        const ratioX = relativeX / displayWidth;
        const ratioY = relativeY / displayHeight;

        // 确保比例值在有效范围内
        const boundedRatioX = Math.max(0, Math.min(1, ratioX));
        const boundedRatioY = Math.max(0, Math.min(1, ratioY));

        // 映射到原始图片坐标
        let mappedX = boundedRatioX * originalWidth;
        let mappedY = boundedRatioY * originalHeight;

        // 处理边界情况，确保坐标在图片范围内
        let x, y;
        if (mappedX < 0) {
          x = 0;
        } else if (mappedX >= originalWidth) {
          x = originalWidth - 1;
        } else {
          x = mappedX;
        }

        if (mappedY < 0) {
          y = 0;
        } else if (mappedY >= originalHeight) {
          y = originalHeight - 1;
        } else {
          y = mappedY;
        }

        // 确保坐标是整数，避免小数点导致的精度问题
        x = Math.round(x);
        y = Math.round(y);

        console.log('计算后的图片坐标:', {
          x, y,
          originalWidth, originalHeight,
          ratioX, ratioY,
          boundedRatioX, boundedRatioY,
          mappedX, mappedY
        });

        // 计算屏幕坐标用于放大镜显示
        const screenX = touch.pageX;
        const screenY = touch.pageY;

        // 计算目标点位置（相对于页面的绝对坐标）
        // 使用限制后的相对坐标，确保目标点始终在图片内
        const targetX = containerNode.left + baseOffsetX + relativeX;
        const targetY = containerNode.top + baseOffsetY + relativeY;

        // 显示放大镜和目标点
        this.showMagnifierAtPosition(screenX, screenY, targetX, targetY);

        // 如果需要取色，则获取颜色
        if (shouldPickColor) {
          // 使用限制后的比例值进行取色
          this.getColorAtPosition(boundedRatioX, boundedRatioY).then(hexColor => {
            if (hexColor) {
              this.safeSetData({
                currentColor: hexColor
              });
            }
          });
        }
      });
    } catch (error) {
      console.error('计算触摸位置失败:', error);
      wx.showToast({
        title: '取色失败，请重试',
        icon: 'none'
      });
    }
  },

  // 容器触摸移动 - 完全参考图片主题色提取页面的实现
  handleContainerTouchMove(e) {
    // 阻止默认滚动行为，防止页面滚动干扰取色
    e.preventDefault && e.preventDefault();
    e.stopPropagation && e.stopPropagation();

    // 检查是否有选中的颜色
    if (this.data.selectedColorIndex < 0) return;

    try {
      // 确保触摸事件有效
      if (!e.touches || !e.touches[0]) return;

      const touch = e.touches[0];

      // 清除长按定时器，因为用户正在移动
      if (this.touchTimer) {
        clearTimeout(this.touchTimer);
        this.touchTimer = null;
      }

      // 如果放大镜还未显示，立即显示
      if (!this.data.magnifierVisible) {
        // 设置长按状态，禁用页面滚动
        this.safeSetData({
          isLongPress: true,
          pageScrollEnabled: false
        });

        // 震动反馈
        if (wx.vibrateShort) {
          wx.vibrateShort({
            type: 'light'
          });
        }
      }

      // 使用图片主题色提取页面的触摸位置计算方法
      this.calculateTouchPosition(touch, true);

    } catch (err) {
      console.error('触摸移动处理失败:', err);
    }
  },

  // 容器触摸结束 - 优化放大镜隐藏逻辑
  handleContainerTouchEnd: async function(e) {
    console.log('触摸结束，当前放大镜状态:', this.data.magnifierVisible);

    // 立即隐藏放大镜，防止显示残留
    this.hideMagnifier();

    // 清除定时器
    if (this.touchTimer) {
      clearTimeout(this.touchTimer);
      this.touchTimer = null;
    }

    // 立即恢复页面滚动状态
    this.safeSetData({
      isLongPress: false,
      pageScrollEnabled: true
    });

    try {
      // 检查是否有选中的颜色进行取色操作
      if (this.data.selectedColorIndex >= 0) {
        let hexColor = null;

        // 如果有当前颜色（从放大镜获取的），使用它
        if (this.data.currentColor && this.data.currentColor !== '#ffffff') {
          hexColor = this.data.currentColor;
          console.log('使用放大镜获取的颜色:', hexColor);
        }
        // 如果是短按且有触摸位置，进行快速取色
        else if (Date.now() - this.data.touchStartTime < 200 && this.data.currentTouchPosition && this.data.imageRect) {
          const { x, y } = this.data.currentTouchPosition;
          const imageRect = this.data.imageRect;

          // 检查点击是否在图片内
          if (x >= imageRect.left && x <= imageRect.right && y >= imageRect.top && y <= imageRect.bottom) {
            // 计算点击在图片上的相对位置
            const relativeX = x - imageRect.left;
            const relativeY = y - imageRect.top;

            // 计算点击位置在原始图片上的比例
            const ratioX = Math.max(0, Math.min(1, relativeX / imageRect.width));
            const ratioY = Math.max(0, Math.min(1, relativeY / imageRect.height));

            console.log('快速点击取色位置比例:', ratioX, ratioY);

            hexColor = await this.getColorAtPosition(ratioX, ratioY);
            console.log('快速取色获取的颜色:', hexColor);
          }
        }

        // 如果获取到了颜色，更新选中的颜色
        if (hexColor && hexColor !== '#ffffff') {
          const colors = [...(this.data.colors || [])];
          const selectedIndex = this.data.selectedColorIndex;
          colors[selectedIndex] = {
            color: hexColor.toUpperCase(),
            displayIndex: selectedIndex + 1,
            selected: true // 将当前编辑的色块复选框状态置为选中
          };

          // 更新颜色并取消选中状态
          this.safeSetData({
            colors,
            selectedColorIndex: -1, // 取消选中状态
            currentColor: '#ffffff' // 重置当前颜色
          });

          // 更新选中颜色数量
          this.updateSelectedCount();

          // 显示成功提示
          wx.showToast({
            title: '颜色已更新',
            icon: 'success',
            duration: 1500
          });

          console.log('颜色更新成功:', hexColor);
        } else {
          // 取色失败，只取消选中状态
          this.safeSetData({
            selectedColorIndex: -1,
            currentColor: '#ffffff'
          });

          wx.showToast({
            title: '取色失败',
            icon: 'none'
          });

          console.log('取色失败');
        }
      }
    } catch (err) {
      console.error('触摸结束处理失败:', err);

      // 发生错误时也要确保状态清理
      this.safeSetData({
        selectedColorIndex: -1,
        currentColor: '#ffffff',
        magnifierVisible: false,
        isLongPress: false,
        pageScrollEnabled: true
      });
    }

    // 最后再次确保放大镜被隐藏
    setTimeout(() => {
      if (this.data.magnifierVisible) {
        console.log('延迟隐藏放大镜');
        this.hideMagnifier();
      }
    }, 100);
  },

  // 容器触摸取消 - 优化状态清理
  handleContainerTouchCancel() {
    console.log('触摸取消，清理所有状态');

    // 立即隐藏放大镜
    this.hideMagnifier();

    // 清除定时器
    if (this.touchTimer) {
      clearTimeout(this.touchTimer);
      this.touchTimer = null;
    }

    // 恢复页面滚动和清理状态
    this.safeSetData({
      isLongPress: false,
      pageScrollEnabled: true,
      selectedColorIndex: -1, // 取消选中状态
      currentColor: '#ffffff' // 重置当前颜色
    });

    console.log('触摸取消状态清理完成');
  },

  // 在指定位置显示放大镜 - 新增方法，确保目标点在图片内
  showMagnifierAtPosition(screenX, screenY, targetX, targetY) {
    // 获取图片区域信息
    const imageRect = this.data.imageRect;
    if (!imageRect) return;

    // 确保目标点在图片区域内
    const boundedTargetX = Math.max(imageRect.left, Math.min(targetX, imageRect.right));
    const boundedTargetY = Math.max(imageRect.top, Math.min(targetY, imageRect.bottom));

    // 计算放大镜偏移位置（左上方，但不要太远）
    const magnifierSize = 45; // 放大镜半径，单位为px
    const offsetX = screenX - magnifierSize * 0.8; // 向左偏移0.8倍放大镜半径
    const offsetY = screenY - magnifierSize * 1.5; // 向上偏移1.5倍放大镜半径

    // 确保放大镜不会超出屏幕边界
    const windowInfo = wx.getWindowInfo(); // 使用新API
    const screenWidth = windowInfo.windowWidth;
    const screenHeight = windowInfo.windowHeight;
    const adjustedX = Math.max(magnifierSize, Math.min(offsetX, screenWidth - magnifierSize));
    const adjustedY = Math.max(magnifierSize, Math.min(offsetY, screenHeight - magnifierSize));

    // 计算放大镜中图片的偏移量
    const magnifierDiameter = magnifierSize * 2; // 放大镜直径
    const magnificationRatio = this.data.magnificationRatio; // 放大倍率（3倍）

    // 计算触摸点在图片中的比例（使用限制后的目标点）
    const ratioX = (boundedTargetX - imageRect.left) / imageRect.width;
    const ratioY = (boundedTargetY - imageRect.top) / imageRect.height;

    // 确保比例值在有效范围内
    const boundedRatioX = Math.max(0, Math.min(1, ratioX));
    const boundedRatioY = Math.max(0, Math.min(1, ratioY));

    // 计算放大后的图片尺寸
    const magnifiedImageWidth = imageRect.width * magnificationRatio;
    const magnifiedImageHeight = imageRect.height * magnificationRatio;

    // 计算触摸点在放大后图片中的位置
    const touchPointInMagnifiedImageX = boundedRatioX * magnifiedImageWidth;
    const touchPointInMagnifiedImageY = boundedRatioY * magnifiedImageHeight;

    // 计算图片在放大镜中的偏移量，使触摸点在放大镜中居中显示
    const magnifierOffsetX = touchPointInMagnifiedImageX - magnifierDiameter / 2;
    const magnifierOffsetY = touchPointInMagnifiedImageY - magnifierDiameter / 2;

    console.log('放大镜显示计算:', {
      originalTarget: { x: targetX, y: targetY },
      boundedTarget: { x: boundedTargetX, y: boundedTargetY },
      imageRect,
      ratios: { x: ratioX, y: ratioY },
      boundedRatios: { x: boundedRatioX, y: boundedRatioY },
      magnifierOffset: { x: magnifierOffsetX, y: magnifierOffsetY }
    });

    this.safeSetData({
      magnifierVisible: true,
      magnifierX: adjustedX,
      magnifierY: adjustedY,
      targetX: boundedTargetX, // 保存限制后的目标位置
      targetY: boundedTargetY, // 保存限制后的目标位置
      magnifierOffsetX: Math.max(0, magnifierOffsetX),
      magnifierOffsetY: Math.max(0, magnifierOffsetY),
      magnifiedImageWidth: magnifiedImageWidth,
      magnifiedImageHeight: magnifiedImageHeight
    });
  },

  // 显示放大镜 - 完全参考图片主题色提取页面的实现
  showMagnifier: async function(x, y, imageRect) {
    if (!imageRect) return;

    // 检查点击是否在图片内
    if (x < imageRect.left || x > imageRect.right || y < imageRect.top || y > imageRect.bottom) {
      return;
    }

    // 计算放大镜位置
    const boundedX = Math.max(imageRect.left, Math.min(x, imageRect.right));
    const boundedY = Math.max(imageRect.top, Math.min(y, imageRect.bottom));

    // 计算放大镜偏移位置（左上方，但不要太远）
    const magnifierSize = 45; // 放大镜半径，单位为px
    const offsetX = boundedX - magnifierSize * 0.8; // 向左偏移0.8倍放大镜半径
    const offsetY = boundedY - magnifierSize * 1.5; // 向上偏移1.5倍放大镜半径

    // 确保放大镜不会超出屏幕边界
    const windowInfo = wx.getWindowInfo(); // 使用新API
    const screenWidth = windowInfo.windowWidth;
    const screenHeight = windowInfo.windowHeight;
    const adjustedX = Math.max(magnifierSize, Math.min(offsetX, screenWidth - magnifierSize));
    const adjustedY = Math.max(magnifierSize, Math.min(offsetY, screenHeight - magnifierSize));

    // 计算点击位置在原始图片上的比例
    const ratioX = (boundedX - imageRect.left) / imageRect.width;
    const ratioY = (boundedY - imageRect.top) / imageRect.height;

    // 获取该位置的颜色
    const hexColor = await this.getColorAtPosition(ratioX, ratioY);

    // 计算放大镜中图片的偏移量
    // 这里的计算逻辑是：
    // 1. 获取放大镜的尺寸（直径）
    // 2. 计算放大后的图片尺寸（原图尺寸 * 放大倍率）
    // 3. 计算触摸点在放大后图片中的位置
    // 4. 调整偏移量，使触摸点在放大镜中居中显示
    const magnifierDiameter = magnifierSize * 2; // 放大镜直径
    const magnificationRatio = this.data.magnificationRatio; // 放大倍率（2倍）

    // 计算放大后的图片尺寸
    const magnifiedImageWidth = imageRect.width * magnificationRatio;
    const magnifiedImageHeight = imageRect.height * magnificationRatio;

    // 计算触摸点在放大后图片中的位置
    const touchPointInMagnifiedImageX = ratioX * magnifiedImageWidth;
    const touchPointInMagnifiedImageY = ratioY * magnifiedImageHeight;

    // 计算图片在放大镜中的偏移量，使触摸点在放大镜中居中显示
    const magnifierOffsetX = touchPointInMagnifiedImageX - magnifierDiameter / 2;
    const magnifierOffsetY = touchPointInMagnifiedImageY - magnifierDiameter / 2;

    this.safeSetData({
      magnifierVisible: true,
      magnifierX: adjustedX,
      magnifierY: adjustedY,
      targetX: boundedX, // 保存实际目标位置
      targetY: boundedY, // 保存实际目标位置
      currentColor: hexColor || '#ffffff', // 设置当前颜色
      magnifierOffsetX: Math.max(0, magnifierOffsetX),
      magnifierOffsetY: Math.max(0, magnifierOffsetY)
    });
  },

  // 更新放大镜位置 - 完全参考图片主题色提取页面的实现
  updateMagnifier: async function(x, y) {
    const imageRect = this.data.imageRect;
    if (!imageRect) return;

    // 限制放大镜在图片范围内
    const boundedX = Math.max(imageRect.left, Math.min(x, imageRect.right));
    const boundedY = Math.max(imageRect.top, Math.min(y, imageRect.bottom));

    // 计算放大镜偏移位置（左上方，但不要太远）
    const magnifierSize = 45; // 放大镜半径，单位为px
    const offsetX = boundedX - magnifierSize * 0.8; // 向左偏移0.8倍放大镜半径
    const offsetY = boundedY - magnifierSize * 1.5; // 向上偏移1.5倍放大镜半径

    // 确保放大镜不会超出屏幕边界
    const windowInfo = wx.getWindowInfo(); // 使用新API
    const screenWidth = windowInfo.windowWidth;
    const screenHeight = windowInfo.windowHeight;
    const adjustedX = Math.max(magnifierSize, Math.min(offsetX, screenWidth - magnifierSize));
    const adjustedY = Math.max(magnifierSize, Math.min(offsetY, screenHeight - magnifierSize));

    // 计算点击位置在原始图片上的比例
    const ratioX = (boundedX - imageRect.left) / imageRect.width;
    const ratioY = (boundedY - imageRect.top) / imageRect.height;

    // 获取该位置的颜色
    const hexColor = await this.getColorAtPosition(ratioX, ratioY);

    // 计算放大镜中图片的偏移量
    // 使用与showMagnifier相同的计算逻辑
    const magnifierDiameter = magnifierSize * 2; // 放大镜直径
    const magnificationRatio = this.data.magnificationRatio; // 放大倍率（2倍）

    // 计算放大后的图片尺寸
    const magnifiedImageWidth = imageRect.width * magnificationRatio;
    const magnifiedImageHeight = imageRect.height * magnificationRatio;

    // 计算触摸点在放大后图片中的位置
    const touchPointInMagnifiedImageX = ratioX * magnifiedImageWidth;
    const touchPointInMagnifiedImageY = ratioY * magnifiedImageHeight;

    // 计算图片在放大镜中的偏移量，使触摸点在放大镜中居中显示
    const magnifierOffsetX = touchPointInMagnifiedImageX - magnifierDiameter / 2;
    const magnifierOffsetY = touchPointInMagnifiedImageY - magnifierDiameter / 2;

    this.safeSetData({
      magnifierX: adjustedX,
      magnifierY: adjustedY,
      targetX: boundedX, // 保存实际目标位置
      targetY: boundedY, // 保存实际目标位置
      currentColor: hexColor || '#ffffff', // 设置当前颜色
      magnifierOffsetX: Math.max(0, magnifierOffsetX),
      magnifierOffsetY: Math.max(0, magnifierOffsetY)
    });
  },

  // 隐藏放大镜 - 优化状态清理
  hideMagnifier() {
    console.log('隐藏放大镜');

    // 清除所有相关定时器
    if (this.touchTimer) {
      clearTimeout(this.touchTimer);
      this.touchTimer = null;
    }

    // 彻底清理放大镜相关状态
    this.safeSetData({
      magnifierVisible: false,
      isLongPress: false,
      pageScrollEnabled: true,
      targetX: 0,
      targetY: 0,
      magnifierX: 0,
      magnifierY: 0,
      magnifierOffsetX: 0,
      magnifierOffsetY: 0
    });

    console.log('放大镜状态已清理');
  },

  // 获取指定位置的颜色 - 完全参考图片主题色提取页面的实现
  getColorAtPosition: async function(ratioX, ratioY) {
    try {
      // 确保比例值在有效范围内
      if (isNaN(ratioX) || isNaN(ratioY) || ratioX < 0 || ratioX > 1 || ratioY < 0 || ratioY > 1) {
        console.error('无效的坐标比例', { x: ratioX, y: ratioY });
        return null;
      }

      // 使用图片路径
      const { imagePath } = this.data;
      const imgSrc = imagePath;

      console.log('取色使用图片', imgSrc);

      // 使用微信的图片信息API获取图片信息
      const imageInfo = await new Promise((resolve, reject) => {
        wx.getImageInfo({
          src: imgSrc,
          success: resolve,
          fail: reject
        });
      });

      // 使用带重试机制的Canvas获取方法
      const canvas = await this.getCanvasWithRetry('tempCanvas');

      if (!canvas || !canvas.node) {
        console.error('getColorAtPosition: Canvas节点获取失败');
        return null;
      }

      const ctx = canvas.node.getContext('2d');

      // 设置Canvas的实际尺寸为图片的实际尺寸，以提高取色精度
      const canvasWidth = imageInfo.width;
      const canvasHeight = imageInfo.height;
      canvas.node.width = canvasWidth;
      canvas.node.height = canvasHeight;

      // 创建图片对象
      const img = canvas.node.createImage();
      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
        img.src = imgSrc;
      });

      // 绘制完整图片到canvas，保持原始尺寸
      ctx.drawImage(img, 0, 0, canvasWidth, canvasHeight);

      // 计算在canvas上的坐标，确保是整数
      const canvasX = Math.max(0, Math.min(canvasWidth - 1, Math.floor(ratioX * canvasWidth)));
      const canvasY = Math.max(0, Math.min(canvasHeight - 1, Math.floor(ratioY * canvasHeight)));

      // 获取像素数据
      const width = 1;
      const height = 1;
      const imageData = ctx.getImageData(canvasX, canvasY, width, height);

      // 获取RGB值
      const r = imageData.data[0];
      const g = imageData.data[1];
      const b = imageData.data[2];

      // 转换为十六进制颜色代码
      return this.rgbToHex(r, g, b);
    } catch (err) {
      console.error('获取颜色失败:', err);
      return null;
    }
  },

  // 带重试机制的Canvas获取方法 - 完全参考图片主题色提取页面的实现
  getCanvasWithRetry: function(canvasId, maxRetries = 8, retryInterval = 400) {
    return new Promise((resolve) => {
      let retryCount = 0;

      const tryGetCanvas = () => {
        retryCount++;

        const query = wx.createSelectorQuery();
        query.select('#' + canvasId)
          .fields({ node: true, size: true })
          .exec((res) => {
            if (res && res[0] && res[0].node) {
              // Canvas节点获取成功，验证上下文
              try {
                const ctx = res[0].node.getContext('2d');
                if (ctx) {
                  console.log(`Canvas节点 ${canvasId} 获取成功，重试次数:`, retryCount);
                  resolve(res[0]);
                } else {
                  console.log(`Canvas节点 ${canvasId} 上下文未准备好，第 ${retryCount} 次重试`);
                  if (retryCount >= maxRetries) {
                    console.warn(`Canvas节点 ${canvasId} 上下文获取失败，已达最大重试次数:`, maxRetries);
                    resolve(null);
                  } else {
                    setTimeout(tryGetCanvas, retryInterval);
                  }
                }
              } catch (ctxError) {
                console.log(`Canvas节点 ${canvasId} 上下文获取异常，第 ${retryCount} 次重试:`, ctxError);
                if (retryCount >= maxRetries) {
                  console.warn(`Canvas节点 ${canvasId} 上下文异常，已达最大重试次数:`, maxRetries);
                  resolve(null);
                } else {
                  setTimeout(tryGetCanvas, retryInterval);
                }
              }
            } else if (retryCount >= maxRetries) {
              // 达到最大重试次数，返回null
              console.warn(`Canvas节点 ${canvasId} 获取失败，已达最大重试次数:`, maxRetries);
              resolve(null);
            } else {
              // 继续重试
              console.log(`Canvas节点 ${canvasId} 获取失败，第 ${retryCount} 次重试`);
              setTimeout(tryGetCanvas, retryInterval);
            }
          });
      };

      // 增加初始延迟，确保DOM稳定
      setTimeout(() => {
        console.log(`开始尝试获取Canvas节点: ${canvasId}`);
        tryGetCanvas();
      }, 50); // 初始延迟50ms
    });
  },

  // 开始取色 - 优化放大镜位置计算
  startColorPicking(imageX, imageY, screenX, screenY) {
    console.log('开始取色:', { imageX, imageY, screenX, screenY });
    console.log('图片区域信息:', this.data.imageRect);

    // 获取系统信息用于优化放大镜位置
    const systemInfo = wx.getSystemInfoSync();
    const screenWidth = systemInfo.windowWidth;
    const screenHeight = systemInfo.windowHeight;

    // 放大镜尺寸
    const magnifierSize = 90;
    const magnifierRadius = magnifierSize / 2;

    // 智能计算放大镜位置，避免超出屏幕边界
    let magnifierX = screenX - magnifierRadius;
    let magnifierY = screenY - magnifierSize - 20; // 默认在触摸点上方

    // 边界检测和调整
    if (magnifierX < 10) {
      magnifierX = 10;
    } else if (magnifierX + magnifierSize > screenWidth - 10) {
      magnifierX = screenWidth - magnifierSize - 10;
    }

    if (magnifierY < 10) {
      // 如果上方空间不足，放在触摸点下方
      magnifierY = screenY + 20;
    }

    // 如果下方也不足，强制放在屏幕中央
    if (magnifierY + magnifierSize > screenHeight - 10) {
      magnifierY = Math.max(10, screenHeight / 2 - magnifierRadius);
    }

    console.log('优化后的放大镜位置:', {
      magnifierX, magnifierY,
      屏幕尺寸: { width: screenWidth, height: screenHeight },
      触摸点: { x: screenX, y: screenY }
    });

    this.safeSetData({
      magnifierVisible: true,
      targetX: screenX,
      targetY: screenY,
      magnifierX: magnifierX,
      magnifierY: magnifierY
    });

    console.log('放大镜已设置为可见');

    // 更新放大镜内容
    this.updateMagnifier(imageX, imageY);
  },

  // 更新取色 - 优化放大镜位置计算
  updateColorPicking(imageX, imageY, screenX, screenY) {
    // 获取系统信息用于优化放大镜位置
    const systemInfo = wx.getSystemInfoSync();
    const screenWidth = systemInfo.windowWidth;
    const screenHeight = systemInfo.windowHeight;

    // 放大镜尺寸
    const magnifierSize = 90;
    const magnifierRadius = magnifierSize / 2;

    // 智能计算放大镜位置，避免超出屏幕边界
    let magnifierX = screenX - magnifierRadius;
    let magnifierY = screenY - magnifierSize - 20; // 默认在触摸点上方

    // 边界检测和调整
    if (magnifierX < 10) {
      magnifierX = 10;
    } else if (magnifierX + magnifierSize > screenWidth - 10) {
      magnifierX = screenWidth - magnifierSize - 10;
    }

    if (magnifierY < 10) {
      // 如果上方空间不足，放在触摸点下方
      magnifierY = screenY + 20;
    }

    // 如果下方也不足，强制放在屏幕中央
    if (magnifierY + magnifierSize > screenHeight - 10) {
      magnifierY = Math.max(10, screenHeight / 2 - magnifierRadius);
    }

    this.safeSetData({
      targetX: screenX,
      targetY: screenY,
      magnifierX: magnifierX,
      magnifierY: magnifierY
    });

    // 更新放大镜内容
    this.updateMagnifier(imageX, imageY);
  },

  // 结束取色
  endColorPicking() {
    console.log('结束取色');

    // 获取当前选中的颜色
    const currentColor = this.data.currentColor;
    if (currentColor && currentColor !== '#FFFFFF') {
      console.log('添加选中的颜色到色块:', currentColor);
      this.addColorToBlocks(currentColor);
    }

    this.safeSetData({
      magnifierVisible: false
    });
  },

  // 选择颜色块 - 参考图片主题色提取页面的实现
  selectColor(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    console.log('选择颜色块:', index);

    // 如果点击的是已选中的颜色，则取消选中
    if (this.data.selectedColorIndex === index) {
      this.safeSetData({
        selectedColorIndex: -1
      });
    } else {
      // 否则选中该颜色
      this.safeSetData({
        selectedColorIndex: index
      });
    }
  },

  // 从图片获取颜色 - 参考图片主题色提取页面的实现
  getColorFromImage(x, y) {
    const { imagePath, imageInfo } = this.data;
    if (!imagePath || !imageInfo) {
      console.error('缺少图片信息');
      return;
    }

    try {
      // 检查坐标是否在图片范围内
      const isOutOfBounds = x < 0 || y < 0 || x >= imageInfo.width || y >= imageInfo.height;

      // 如果坐标超出图片范围，使用最近的边缘像素
      const safeX = Math.max(0, Math.min(x, imageInfo.width - 1));
      const safeY = Math.max(0, Math.min(y, imageInfo.height - 1));

      console.log('取色坐标:', {
        originalX: x,
        originalY: y,
        safeX: safeX,
        safeY: safeY,
        imageWidth: imageInfo.width,
        imageHeight: imageInfo.height,
        isOutOfBounds: isOutOfBounds
      });

      // 如果坐标超出图片范围，直接使用边缘像素
      if (isOutOfBounds) {
        this.getEdgePixelColor(safeX, safeY);
        return;
      }

      // 使用临时Canvas进行取色
      if (!this.tempCanvas || !this.tempCtx) {
        console.warn('临时Canvas未准备好，使用备用方案');
        this.getEdgePixelColor(safeX, safeY);
        return;
      }

      const canvas = this.tempCanvas;
      const ctx = this.tempCtx;

      // 清除Canvas
      ctx.clearRect(0, 0, 10, 10);

      // 计算源区域，确保不会超出图片边界
      let sourceX = safeX - 1;
      let sourceY = safeY - 1;
      let sourceWidth = 3;
      let sourceHeight = 3;
      let destX = 0;
      let destY = 0;

      // 处理左边界
      if (sourceX < 0) {
        destX = Math.abs(sourceX);
        sourceWidth -= destX;
        sourceX = 0;
      }

      // 处理上边界
      if (sourceY < 0) {
        destY = Math.abs(sourceY);
        sourceHeight -= destY;
        sourceY = 0;
      }

      // 处理右边界
      if (sourceX + sourceWidth > imageInfo.width) {
        sourceWidth = imageInfo.width - sourceX;
      }

      // 处理下边界
      if (sourceY + sourceHeight > imageInfo.height) {
        sourceHeight = imageInfo.height - sourceY;
      }

      // 创建图片对象用于Canvas 2D API
      const img = canvas.createImage();
      img.onload = () => {
        // 绘制图片的指定区域
        if (sourceWidth > 0 && sourceHeight > 0) {
          try {
            ctx.drawImage(
              img,
              sourceX,
              sourceY,
              sourceWidth,
              sourceHeight,
              destX,
              destY,
              sourceWidth,
              sourceHeight
            );

            // 获取像素数据 - 直接获取中心点的颜色
            try {
              const imageData = ctx.getImageData(1, 1, 1, 1);
              const data = imageData.data;

              // 提取RGB值
              const r = data[0];
              const g = data[1];
              const b = data[2];

              // 转换为HEX格式
              const hexColor = this.rgbToHex(r, g, b);

              // 更新颜色值
              this.safeSetData({
                currentColor: hexColor
              });

              console.log('取色成功:', { hexColor, rgb: `rgb(${r},${g},${b})` });
            } catch (error) {
              console.error('获取像素数据失败:', error);
              this.getEdgePixelColor(safeX, safeY);
            }
          } catch (err) {
            console.error('绘制图片区域失败:', err);
            this.getEdgePixelColor(safeX, safeY);
          }
        } else {
          console.error('无效的源区域尺寸');
          this.getEdgePixelColor(safeX, safeY);
        }
      };
      img.onerror = () => {
        console.error('图片加载失败');
        this.getEdgePixelColor(safeX, safeY);
      };
      img.src = imagePath;
    } catch (error) {
      // 取色失败，使用备用方案
      this.getEdgePixelColor(x, y);

      // 显示轻量级错误提示
      wx.showToast({
        title: '取色遇到问题，已使用备用颜色',
        icon: 'none',
        duration: 1500
      });
    }
  },

  // 获取边缘像素颜色 - 备用方法
  getEdgePixelColor(x, y) {
    const { imagePath, imageInfo } = this.data;
    if (!imagePath || !imageInfo) return;

    try {
      // 使用临时Canvas进行边缘取色
      if (!this.tempCanvas || !this.tempCtx) {
        // Canvas未准备好，使用固定颜色
        this.safeSetData({ currentColor: '#FFFFFF' });
        return;
      }

      const canvas = this.tempCanvas;
      const ctx = this.tempCtx;
      ctx.clearRect(0, 0, 10, 10);

      // 计算源区域 - 只绘制边缘附近的小区域
      const size = 5;
      const halfSize = Math.floor(size / 2);

      // 计算源区域的左上角坐标，确保不会超出图片边界
      const sourceX = Math.max(0, x - halfSize);
      const sourceY = Math.max(0, y - halfSize);

      // 计算源区域的宽高，确保不会超出图片边界
      const sourceWidth = Math.min(size, imageInfo.width - sourceX);
      const sourceHeight = Math.min(size, imageInfo.height - sourceY);

      // 计算目标区域的左上角坐标
      const destX = x - sourceX;
      const destY = y - sourceY;

      console.log('边缘取色区域:', {
        sourceX, sourceY,
        sourceWidth, sourceHeight,
        destX, destY,
        x, y
      });

      // 创建图片对象
      const img = canvas.createImage();
      img.onload = () => {
        if (sourceWidth > 0 && sourceHeight > 0) {
          try {
            ctx.drawImage(
              img,
              sourceX,
              sourceY,
              sourceWidth,
              sourceHeight,
              0,
              0,
              sourceWidth,
              sourceHeight
            );

            // 获取目标像素的颜色
            const imageData = ctx.getImageData(destX, destY, 1, 1);
            const data = imageData.data;

            const r = data[0];
            const g = data[1];
            const b = data[2];

            const hexColor = this.rgbToHex(r, g, b);

            this.safeSetData({
              currentColor: hexColor
            });

            console.log('边缘取色成功:', { hexColor, rgb: `rgb(${r},${g},${b})` });
          } catch (error) {
            console.error('边缘取色失败:', error);
            this.safeSetData({ currentColor: '#FFFFFF' });
          }
        } else {
          this.safeSetData({ currentColor: '#FFFFFF' });
        }
      };
      img.onerror = () => {
        this.safeSetData({ currentColor: '#FFFFFF' });
      };
      img.src = imagePath;
    } catch (error) {
      console.error('边缘取色异常:', error);
      this.safeSetData({ currentColor: '#FFFFFF' });
    }
  },

  // 添加颜色到色块
  addColorToBlocks(color) {
    const { colors, maxColors, selectedColorIndex } = this.data;

    // 确保colors数组存在
    if (!colors || !Array.isArray(colors)) {
      console.warn('colors数组不存在，无法添加颜色');
      return;
    }

    // 检查颜色是否已存在
    const existingIndex = colors.findIndex(item => item.color.toLowerCase() === color.toLowerCase());
    if (existingIndex !== -1) {
      console.log('颜色已存在，不重复添加:', color);
      wx.showToast({
        title: '颜色已存在',
        icon: 'none',
        duration: 1000
      });
      return;
    }

    let newColors = [...(colors || [])];
    let targetIndex = -1;
    let actionMessage = '';

    // 如果有选中的颜色块，替换选中的颜色
    if (selectedColorIndex >= 0 && selectedColorIndex < newColors.length) {
      targetIndex = selectedColorIndex;
      newColors[targetIndex] = {
        color: color,
        displayIndex: targetIndex + 1,
        selected: true // 将当前编辑的色块复选框状态置为选中
      };
      actionMessage = `已替换颜色 ${targetIndex + 1}`;

      // 取消选中状态
      this.safeSetData({
        selectedColorIndex: -1
      });
    } else if (newColors.length < maxColors) {
      // 如果没有选中颜色且还有空位，直接添加
      targetIndex = newColors.length;
      newColors.push({
        color: color,
        displayIndex: newColors.length + 1,
        selected: true // 新添加的色块复选框状态置为选中
      });
      actionMessage = `已添加颜色 ${newColors.length}`;
    } else {
      // 如果没有选中颜色且已满，替换最后一个
      targetIndex = maxColors - 1;
      newColors[targetIndex] = {
        color: color,
        displayIndex: maxColors,
        selected: true // 替换的色块复选框状态置为选中
      };
      actionMessage = `已替换颜色 ${maxColors}`;
    }

    console.log('更新颜色列表:', newColors, '目标位置:', targetIndex);

    wx.showToast({
      title: actionMessage,
      icon: 'success',
      duration: 1000
    });

    this.safeSetData({
      colors: newColors
    });

    // 更新选中颜色数量
    this.updateSelectedCount();
  },

  // 更新放大镜 - 参考图片主题色提取页面的实现
  updateMagnifier(relativeX, relativeY) {
    const { imageInfo } = this.data;
    if (!imageInfo) {
      console.warn('更新放大镜失败: imageInfo为空');
      return;
    }

    // 计算放大镜的显示参数
    const magnifierDiameter = 90; // 放大镜直径
    const magnificationRatio = 3; // 放大倍率

    // 计算图片在容器中的实际显示尺寸
    const query = wx.createSelectorQuery().in(this);
    query.select('.image-container').boundingClientRect();
    query.exec((res) => {
      if (!res || !res[0]) return;

      const containerNode = res[0];
      const containerWidth = containerNode.width;
      const containerHeight = containerNode.height;

      // 计算图片在aspectFit模式下的实际显示区域
      const imageAspectRatio = imageInfo.width / imageInfo.height;
      const containerAspectRatio = containerWidth / containerHeight;

      let displayWidth, displayHeight;

      if (imageAspectRatio > containerAspectRatio) {
        displayWidth = containerWidth;
        displayHeight = containerWidth / imageAspectRatio;
      } else {
        displayHeight = containerHeight;
        displayWidth = containerHeight * imageAspectRatio;
      }

      // 计算放大后的图片尺寸
      const magnifiedImageWidth = displayWidth * magnificationRatio;
      const magnifiedImageHeight = displayHeight * magnificationRatio;

      // 计算触摸点在放大后图片中的位置
      const touchPointInMagnifiedImageX = (relativeX / displayWidth) * magnifiedImageWidth;
      const touchPointInMagnifiedImageY = (relativeY / displayHeight) * magnifiedImageHeight;

      // 计算图片在放大镜中的偏移量，使触摸点在放大镜中居中显示
      const magnifierOffsetX = touchPointInMagnifiedImageX - magnifierDiameter / 2;
      const magnifierOffsetY = touchPointInMagnifiedImageY - magnifierDiameter / 2;

      console.log('放大镜更新:', {
        relativeCoords: { x: relativeX, y: relativeY },
        displaySize: { width: displayWidth, height: displayHeight },
        magnifiedSize: { width: magnifiedImageWidth, height: magnifiedImageHeight },
        touchPoint: { x: touchPointInMagnifiedImageX, y: touchPointInMagnifiedImageY },
        offset: { x: magnifierOffsetX, y: magnifierOffsetY }
      });

      this.safeSetData({
        magnificationRatio: magnificationRatio,
        magnifierOffsetX: Math.max(0, magnifierOffsetX),
        magnifierOffsetY: Math.max(0, magnifierOffsetY),
        magnifiedImageWidth: magnifiedImageWidth,
        magnifiedImageHeight: magnifiedImageHeight
      });
    });
  },



  // 低精度取色（备用方法）
  getColorAtPositionLowRes(ratioX, ratioY) {
    if (!this.colorAnalysisCanvas || !this.colorAnalysisCtx) {
      console.warn('Canvas未准备好，无法获取颜色');
      this.safeSetData({ currentColor: '#FFFFFF' });
      return '#FFFFFF';
    }

    try {
      // 将比例转换为Canvas坐标（Canvas大小是200x200）
      const canvasX = Math.floor(ratioX * 200);
      const canvasY = Math.floor(ratioY * 200);

      // 确保坐标在Canvas范围内
      const x = Math.max(0, Math.min(canvasX, 199));
      const y = Math.max(0, Math.min(canvasY, 199));

      // 从Canvas中获取像素颜色
      const imageData = this.colorAnalysisCtx.getImageData(x, y, 1, 1);

      const r = imageData.data[0];
      const g = imageData.data[1];
      const b = imageData.data[2];
      const alpha = imageData.data[3];

      // 如果像素是透明的，使用白色
      if (alpha < 128) {
        this.safeSetData({ currentColor: '#FFFFFF' });
        return '#FFFFFF';
      }

      const hexColor = this.rgbToHex(`rgb(${r},${g},${b})`);

      this.safeSetData({
        currentColor: hexColor
      });

      console.log(`低精度取色: 比例(${ratioX.toFixed(3)}, ${ratioY.toFixed(3)}) -> Canvas(${x}, ${y}) -> RGB(${r}, ${g}, ${b}) -> ${hexColor}`);
      return hexColor;
    } catch (error) {
      console.error('低精度取色失败:', error);
      this.safeSetData({ currentColor: '#FFFFFF' });
      return '#FFFFFF';
    }
  },

  // 获取颜色项位置信息
  getColorItemRects() {
    if (!this.isPageValid()) {
      return;
    }

    const query = wx.createSelectorQuery();
    query.selectAll('.color-item').boundingClientRect((rects) => {
      if (rects && rects.length > 0) {
        this.safeSetData({
          colorItemRects: rects
        });
      }
    }).exec();
  },

  // 颜色拖拽开始
  handleColorTouchStart(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    const touch = e.touches[0];

    this.dragStartTime = Date.now();
    this.dragStartX = touch.clientX;
    this.dragStartY = touch.clientY;

    // 延迟启动拖拽，避免与点击选择冲突
    this.dragTimer = setTimeout(() => {
      // 获取最新的位置信息
      this.getColorItemRects();

      this.safeSetData({
        dragIndex: index,
        dragStartX: touch.clientX,
        dragOffsetX: 0,
        isDragging: true,
        dragStyle: '' // 初始化拖拽样式
      });
    }, 150); // 150ms后启动拖拽
  },

  // 颜色拖拽移动
  handleColorTouchMove(e) {
    // 如果还没有启动拖拽，检查是否应该取消拖拽启动
    if (this.data.dragIndex === -1 || !this.data.isDragging) {
      if (this.dragTimer) {
        const touch = e.touches[0];
        const moveDistance = Math.sqrt(
          Math.pow(touch.clientX - this.dragStartX, 2) +
          Math.pow(touch.clientY - this.dragStartY, 2)
        );

        // 如果移动距离太大，取消拖拽启动
        if (moveDistance > 20) {
          clearTimeout(this.dragTimer);
          this.dragTimer = null;
        }
      }
      return;
    }

    const touch = e.touches[0];
    const currentX = touch.clientX;
    const offsetX = currentX - this.data.dragStartX;

    // 计算拖拽样式，添加阴影效果
    const dragStyle = `z-index: 100; position: relative; left: ${offsetX}px; transform: scale(1.05); box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.15);`;

    // 更新拖拽偏移量
    this.safeSetData({
      dragOffsetX: offsetX,
      dragStyle: dragStyle
    });

    // 检查是否需要交换位置
    this.checkSwapPosition(currentX);
  },

  // 检查是否需要交换位置
  checkSwapPosition(currentX) {
    const { dragIndex, colorItemRects } = this.data;

    // 如果没有位置信息，直接返回
    if (!colorItemRects || colorItemRects.length === 0) return;

    // 获取当前拖拽项的中心位置
    const dragItemRect = colorItemRects[dragIndex];
    const dragItemCenter = dragItemRect.left + this.data.dragOffsetX + (dragItemRect.width / 2);

    // 检查是否与其他颜色项重叠，并决定是否交换位置
    for (let i = 0; i < colorItemRects.length; i++) {
      // 跳过自己
      if (i === dragIndex) continue;

      const rect = colorItemRects[i];

      // 如果拖拽项的中心位置在另一个颜色项的范围内，交换位置
      if (dragItemCenter > rect.left && dragItemCenter < rect.right) {
        // 确定交换方向
        const isRightSwap = i > dragIndex;
        const isLeftSwap = i < dragIndex;

        // 只有当拖拽方向与交换方向一致时才交换
        if ((isRightSwap && this.data.dragOffsetX > 0) ||
            (isLeftSwap && this.data.dragOffsetX < 0)) {
          this.swapColors(dragIndex, i);
          break;
        }
      }
    }
  },

  // 交换颜色位置
  swapColors(fromIndex, toIndex) {
    // 获取当前颜色数组
    const colors = [...(this.data.colors || [])];

    // 交换颜色
    const temp = colors[fromIndex];
    colors[fromIndex] = colors[toIndex];
    colors[toIndex] = temp;

    // 重新计算显示索引，保持选中状态
    const updatedColors = colors.map((item, index) => ({
      color: item.color,
      displayIndex: index + 1,
      selected: item.selected // 保持选中状态
    }));

    // 更新颜色数组和拖拽索引
    this.safeSetData({
      colors: updatedColors,
      dragIndex: toIndex,
      dragStartX: this.data.dragStartX + (toIndex - fromIndex) * this.data.colorItemRects[0].width,
      dragOffsetX: 0
    });

    // 重新获取位置信息
    this.getColorItemRects();

    // 更新选中颜色数量
    this.updateSelectedCount();

    // 震动反馈
    if (wx.vibrateShort) {
      wx.vibrateShort({
        type: 'light'
      });
    }
  },

  // 颜色拖拽结束
  handleColorTouchEnd(e) {
    // 清理拖拽启动定时器
    if (this.dragTimer) {
      clearTimeout(this.dragTimer);
      this.dragTimer = null;
    }

    const { dragIndex, dragOffsetX } = this.data;

    // 如果没有启动拖拽，可能是点击操作
    if (dragIndex === -1) {
      return;
    }

    // 重置拖拽状态
    this.safeSetData({
      dragIndex: -1,
      dragOffsetX: 0,
      isDragging: false,
      dragStyle: '' // 重置拖拽样式
    });

    // 如果拖拽距离很小，可能是点击操作
    if (Math.abs(dragOffsetX) < 20) {
      // 触发颜色选择
      this.selectColor(e);
    }
  },

  // 颜色拖拽取消
  handleColorTouchCancel(e) {
    // 清理拖拽启动定时器
    if (this.dragTimer) {
      clearTimeout(this.dragTimer);
      this.dragTimer = null;
    }

    this.safeSetData({
      dragIndex: -1,
      dragOffsetX: 0,
      isDragging: false,
      dragStyle: '' // 重置拖拽样式
    });
  },

  // 确认选择
  confirmSelection() {
    console.log('确认选择颜色');

    const colorsData = this.data.colors;

    if (!colorsData || colorsData.length === 0) {
      wx.showToast({
        title: '没有可用的颜色',
        icon: 'none'
      });
      return;
    }

    // 只提取选中的颜色值
    const selectedColors = colorsData.filter(item => item.selected).map(item => item.color);
    console.log('准备传递的选中颜色:', selectedColors);

    if (selectedColors.length === 0) {
      wx.showToast({
        title: '请至少选择一个颜色',
        icon: 'none'
      });
      return;
    }

    // 通过事件通信返回选中的颜色
    const eventChannel = this.getOpenerEventChannel();
    if (eventChannel) {
      // 始终使用按顺序替换的事件，因为现在只传递选中的颜色
      console.log('发送 acceptSelectedColors 事件');
      eventChannel.emit('acceptSelectedColors', selectedColors);
    } else {
      console.error('无法获取事件通道');
      wx.showToast({
        title: '返回失败，请重试',
        icon: 'none'
      });
      return;
    }

    // 显示成功提示
    wx.showToast({
      title: `已选择${selectedColors.length}个颜色`,
      icon: 'success',
      duration: 1500
    });

    // 延迟返回上一页，确保事件传递完成
    setTimeout(() => {
      SafeNavigation.navigateBack();
    }, 500);
  }
});
