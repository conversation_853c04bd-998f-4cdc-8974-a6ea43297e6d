/**
 * 页面基类
 * 提供安全的页面生命周期和错误处理
 */

const { initSafeSetData, safeSetData } = require('./safeSetData');
const { addErrorHandling, isWxInternalError, safeConsoleWarn } = require('./errorInterceptor');

/**
 * 创建安全的页面配置
 * @param {Object} pageConfig - 页面配置对象
 * @returns {Object} 增强后的页面配置
 */
function createSafePage(pageConfig) {
  const originalOnLoad = pageConfig.onLoad;
  const originalOnReady = pageConfig.onReady;
  const originalOnShow = pageConfig.onShow;
  
  return {
    ...pageConfig,
    
    // 页面加载时的安全处理
    onLoad(options) {
      try {
        // 添加错误处理
        addErrorHandling(this);
        
        // 初始化安全的 setData 方法
        this.safeSetData = (data, callback) => safeSetData(this, data, callback);
        this.initSafeSetData = (data, callback) => initSafeSetData(this, data, callback);
        
        // 调用原始的 onLoad
        if (originalOnLoad) {
          // 延迟执行，避免过早调用
          setTimeout(() => {
            try {
              originalOnLoad.call(this, options);
            } catch (error) {
              if (!isWxInternalError(error)) {
                throw error;
              }
              safeConsoleWarn('页面 onLoad 中捕获微信内部错误:', error.message || error);
            }
          }, 50);
        }
      } catch (error) {
        if (!isWxInternalError(error)) {
          throw error;
        }
        safeConsoleWarn('页面初始化中捕获微信内部错误:', error.message || error);
      }
    },
    
    // 页面准备就绪时的安全处理
    onReady() {
      try {
        if (originalOnReady) {
          originalOnReady.call(this);
        }
      } catch (error) {
        if (!isWxInternalError(error)) {
          throw error;
        }
        safeConsoleWarn('页面 onReady 中捕获微信内部错误:', error.message || error);
      }
    },
    
    // 页面显示时的安全处理
    onShow() {
      try {
        if (originalOnShow) {
          originalOnShow.call(this);
        }
      } catch (error) {
        if (!isWxInternalError(error)) {
          throw error;
        }
        safeConsoleWarn('页面 onShow 中捕获微信内部错误:', error.message || error);
      }
    }
  };
}

/**
 * 创建安全的组件配置
 * @param {Object} componentConfig - 组件配置对象
 * @returns {Object} 增强后的组件配置
 */
function createSafeComponent(componentConfig) {
  const originalLifetimes = componentConfig.lifetimes || {};
  const originalAttached = originalLifetimes.attached;
  const originalReady = originalLifetimes.ready;
  const originalDetached = originalLifetimes.detached;
  
  return {
    ...componentConfig,
    
    lifetimes: {
      ...originalLifetimes,
      
      // 组件附加时的安全处理
      attached() {
        try {
          // 添加错误处理
          addErrorHandling(this);
          
          // 初始化安全的 setData 方法
          this.safeSetData = (data, callback) => safeSetData(this, data, callback);
          this.initSafeSetData = (data, callback) => initSafeSetData(this, data, callback);
          
          // 调用原始的 attached
          if (originalAttached) {
            // 延迟执行，避免过早调用
            setTimeout(() => {
              try {
                originalAttached.call(this);
              } catch (error) {
                if (!isWxInternalError(error)) {
                  throw error;
                }
                safeConsoleWarn('组件 attached 中捕获微信内部错误:', error.message || error);
              }
            }, 50);
          }
        } catch (error) {
          if (!isWxInternalError(error)) {
            throw error;
          }
          safeConsoleWarn('组件初始化中捕获微信内部错误:', error.message || error);
        }
      },
      
      // 组件准备就绪时的安全处理
      ready() {
        try {
          if (originalReady) {
            originalReady.call(this);
          }
        } catch (error) {
          if (!isWxInternalError(error)) {
            throw error;
          }
          safeConsoleWarn('组件 ready 中捕获微信内部错误:', error.message || error);
        }
      },
      
      // 组件分离时的安全处理
      detached() {
        try {
          if (originalDetached) {
            originalDetached.call(this);
          }
        } catch (error) {
          if (!isWxInternalError(error)) {
            throw error;
          }
          safeConsoleWarn('组件 detached 中捕获微信内部错误:', error.message || error);
        }
      }
    }
  };
}

/**
 * 安全的页面工厂函数
 * 使用方式：Page(SafePage({ ... }))
 */
function SafePage(pageConfig) {
  return createSafePage(pageConfig);
}

/**
 * 安全的组件工厂函数
 * 使用方式：Component(SafeComponent({ ... }))
 */
function SafeComponent(componentConfig) {
  return createSafeComponent(componentConfig);
}

module.exports = {
  createSafePage,
  createSafeComponent,
  SafePage,
  SafeComponent
};
