// pages/gradientGenerator/gradientGenerator.js
const colorUtils = require('../../utils/colorUtils');
Page({
  data: {
    gradientType: 'linear', // 渐变类型：linear-线性渐变，radial-径向渐变
    direction: 'to right', // 线性渐变方向
    directionIndex: 2, // 方向选择器的索引
    currentTextColor: '#FFFFFF', // 当前颜色文本的颜色
    showSaveSuccess: false, // 是否显示保存成功提示
    directionOptions: [
      { value: 'to top', label: '向上' },
      { value: 'to right top', label: '向右上' },
      { value: 'to right', label: '向右' },
      { value: 'to right bottom', label: '向右下' },
      { value: 'to bottom', label: '向下' },
      { value: 'to left bottom', label: '向左下' },
      { value: 'to left', label: '向左' },
      { value: 'to left top', label: '向左上' }
    ],
    directionLabels: {
      'to top': '向上',
      'to right top': '向右上',
      'to right': '向右',
      'to right bottom': '向右下',
      'to bottom': '向下',
      'to left bottom': '向左下',
      'to left': '向左',
      'to left top': '向左上'
    },
    directionIcons: {
      'to top': 'top-arrow',
      'to right top': 'right-top-arrow',
      'to right': 'right-arrow',
      'to right bottom': 'right-bottom-arrow',
      'to bottom': 'bottom-arrow',
      'to left bottom': 'left-bottom-arrow',
      'to left': 'left-arrow',
      'to left top': 'left-top-arrow'
    },
    angle: 90, // 线性渐变角度（度数）
    shape: 'circle', // 径向渐变形状：circle-圆形，ellipse-椭圆
    position: 'center', // 径向渐变位置
    positionLabels: {
      'center': '中心',
      'top': '上',
      'right top': '右上',
      'right': '右',
      'right bottom': '右下',
      'bottom': '下',
      'left bottom': '左下',
      'left': '左',
      'left top': '左上'
    },
    colorStops: [
      { color: '#ff416c', position: 0 },
      { color: '#ff4b2b', position: 100 }
    ], // 颜色节点
    currentStopIndex: 0, // 当前选中的颜色节点索引
    showColorPicker: false, // 是否显示颜色选择器
    gradientStyle: '', // 渐变样式
    gradientCode: '', // 渐变CSS代码
    copiedItem: '', // 当前复制的项目

    presets: [
      { id: 'sunset', name: '日落', gradient: 'linear-gradient(to right, #ff416c, #ff4b2b)' },
      { id: 'ocean', name: '海洋', gradient: 'linear-gradient(to right, #4facfe, #00f2fe)' },
      { id: 'purple', name: '紫色', gradient: 'linear-gradient(to right, #8e2de2, #4a00e0)' },
      { id: 'green', name: '绿色', gradient: 'linear-gradient(to right, #11998e, #38ef7d)' },
      { id: 'pink', name: '粉色', gradient: 'linear-gradient(to right, #fc5c7d, #6a82fb)' },
      { id: 'blue', name: '蓝色', gradient: 'linear-gradient(to right, #2193b0, #6dd5ed)' },
      { id: 'yellow', name: '黄色', gradient: 'linear-gradient(to right, #f7971e, #ffd200)' },
      { id: 'red', name: '红色', gradient: 'linear-gradient(to right, #e53935, #e35d5b)' },
      { id: 'dark', name: '暗色', gradient: 'linear-gradient(to right, #232526, #414345)' },
      { id: 'rainbow', name: '彩虹', gradient: 'linear-gradient(to right, #ff0000, #ff7f00, #ffff00, #00ff00, #0000ff, #4b0082, #8b00ff)' },
      { id: 'radial1', name: '径向1', gradient: 'radial-gradient(circle at center, #ff416c, #ff4b2b)' },
      { id: 'radial2', name: '径向2', gradient: 'radial-gradient(circle at center, #4facfe, #00f2fe)' }
    ],
    savedGradients: [] // 初始化保存的渐变列表
  },

  onLoad: function (options) {
    // 生成初始渐变
    this.generateGradient();

    // 设置初始方向索引
    this.setDirectionIndex();

    // 设置初始文字颜色
    if (this.data.colorStops && this.data.colorStops.length > 0) {
      const textColor = colorUtils.getTextColorForBackground(this.data.colorStops[0].color);
      this.setData({
        currentTextColor: textColor
      });
    }

    // 异步加载保存的渐变列表，避免阻塞首屏渲染
    wx.nextTick(() => {
      this.loadSavedGradientsAsync();
    });

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '渐变生成器'
    });
  },

  // 用户点击右上角分享或使用分享按钮
  onShareAppMessage: function() {
    return {
      title: '渐变生成器 - 创建美丽的色彩渐变效果',
      path: '/pages/gradientGenerator/gradientGenerator',
      imageUrl: '/assets/images/share-gradient.png' // 分享图片
    };
  },

  // 设置方向索引
  setDirectionIndex: function() {
    const { direction, directionOptions } = this.data;
    const index = directionOptions.findIndex(option => option.value === direction);
    if (index !== -1) {
      this.setData({
        directionIndex: index
      });
    }
  },

  // 辅助函数：将方向转换为角度
  directionToAngle: function(direction) {
    let angle = 0;
    switch(direction) {
      case 'to top': angle = 0; break;
      case 'to right top': angle = 45; break;
      case 'to right': angle = 90; break;
      case 'to right bottom': angle = 135; break;
      case 'to bottom': angle = 180; break;
      case 'to left bottom': angle = 225; break;
      case 'to left': angle = 270; break;
      case 'to left top': angle = 315; break;
    }
    return angle;
  },

  // 辅助函数：将角度转换为方向
  angleToDirection: function(angle) {
    // 确保角度在0-360范围内
    angle = ((angle % 360) + 360) % 360;

    let direction = 'to right';
    if (angle >= 0 && angle < 22.5) {
      direction = 'to top';
    } else if (angle >= 22.5 && angle < 67.5) {
      direction = 'to right top';
    } else if (angle >= 67.5 && angle < 112.5) {
      direction = 'to right';
    } else if (angle >= 112.5 && angle < 157.5) {
      direction = 'to right bottom';
    } else if (angle >= 157.5 && angle < 202.5) {
      direction = 'to bottom';
    } else if (angle >= 202.5 && angle < 247.5) {
      direction = 'to left bottom';
    } else if (angle >= 247.5 && angle < 292.5) {
      direction = 'to left';
    } else if (angle >= 292.5 && angle < 337.5) {
      direction = 'to left top';
    } else {
      direction = 'to top';
    }
    return direction;
  },

  // 方向选择器变化回调
  onDirectionChange: function(e) {
    const index = e.detail.value;
    const direction = this.data.directionOptions[index].value;

    // 使用辅助函数将方向转换为角度
    const angle = this.directionToAngle(direction);

    this.setData({
      direction: direction,
      directionIndex: index,
      angle: angle
    });

    this.generateGradient();
  },


  // 切换渐变类型
  switchGradientType: function(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      gradientType: type
    });
    this.generateGradient();
  },

  // 设置线性渐变方向
  setDirection: function(e) {
    const direction = e.currentTarget.dataset.direction;

    // 使用辅助函数将方向转换为角度
    const angle = this.directionToAngle(direction);

    this.setData({
      direction: direction,
      angle: angle
    });
    this.generateGradient();
  },

  // 设置线性渐变角度
  setAngle: function(e) {
    const angle = e.detail.value;

    // 使用辅助函数将角度转换为方向
    const direction = this.angleToDirection(angle);

    // 找到对应的方向索引
    const directionIndex = this.data.directionOptions.findIndex(option => option.value === direction);

    this.setData({
      angle: angle,
      direction: direction,
      directionIndex: directionIndex !== -1 ? directionIndex : this.data.directionIndex
    });
    this.generateGradient();
  },

  // 设置径向渐变形状
  setShape: function(e) {
    const shape = e.currentTarget.dataset.shape;
    this.setData({
      shape: shape
    });
    this.generateGradient();
  },

  // 设置径向渐变位置
  setPosition: function(e) {
    const position = e.currentTarget.dataset.position;
    this.setData({
      position: position
    });
    this.generateGradient();
  },

  // 辅助函数：对颜色节点进行排序
  sortColorStops: function(colorStops) {
    return [...colorStops].sort((a, b) => a.position - b.position);
  },

  // 选择颜色节点
  selectColorStop: function(e) {
    const index = e.currentTarget.dataset.index;
    const textColor = colorUtils.getTextColorForBackground(this.data.colorStops[index].color);
    this.setData({
      currentStopIndex: index,
      currentTextColor: textColor
    });
  },

  // 添加颜色节点
  addColorStop: function() {
    const { colorStops } = this.data;

    // 最多允许10个颜色节点
    if (colorStops.length >= 10) {
      return;
    }

    // 计算新节点的位置
    const lastPosition = colorStops[colorStops.length - 1].position;
    const firstPosition = colorStops[0].position;
    let newPosition = 50; // 默认位置

    if (lastPosition > firstPosition) {
      // 如果是从左到右排列，新节点添加到最右边
      newPosition = Math.min(lastPosition + 10, 100);
    } else {
      // 如果是从右到左排列，新节点添加到最左边
      newPosition = Math.max(firstPosition - 10, 0);
    }

    // 生成随机颜色
    const randomColor = this.getRandomColor();

    // 添加新节点
    const newColorStops = this.sortColorStops([...colorStops, { color: randomColor, position: newPosition }]);

    this.setData({
      colorStops: newColorStops,
      currentStopIndex: newColorStops.findIndex(stop => stop.color === randomColor)
    });

    this.generateGradient();
  },

  // 删除颜色节点
  deleteColorStop: function(e) {
    const index = e.currentTarget.dataset.index;
    const { colorStops } = this.data;

    // 至少保留2个颜色节点
    if (colorStops.length <= 2) {
      wx.showToast({
        title: '至少需要2个颜色',
        icon: 'none'
      });
      return;
    }

    // 删除节点
    const newColorStops = colorStops.filter((_, i) => i !== index);

    // 更新当前选中的节点
    let newCurrentIndex = this.data.currentStopIndex;
    if (newCurrentIndex === index) {
      newCurrentIndex = 0;
    } else if (newCurrentIndex > index) {
      newCurrentIndex--;
    }

    this.setData({
      colorStops: newColorStops,
      currentStopIndex: newCurrentIndex
    });

    this.generateGradient();
  },

  // 设置颜色节点位置
  setColorPosition: function(e) {
    const position = e.detail.value;
    const { currentStopIndex, colorStops } = this.data;

    if (currentStopIndex === null) {
      return;
    }

    // 更新位置
    const newColorStops = [...colorStops];
    newColorStops[currentStopIndex].position = position;

    // 按位置排序
    const sortedColorStops = this.sortColorStops(newColorStops);

    // 找到当前选中的颜色在排序后的新位置
    const currentColor = colorStops[currentStopIndex].color;
    const newCurrentIndex = sortedColorStops.findIndex(stop => stop.color === currentColor);

    this.setData({
      colorStops: sortedColorStops,
      currentStopIndex: newCurrentIndex
    });

    this.generateGradient();
  },

  // 显示颜色选择器
  showColorPicker: function() {
    this.setData({
      showColorPicker: true
    });
  },

  // 隐藏颜色选择器
  hideColorPicker: function() {
    this.setData({
      showColorPicker: false
    });
  },

  // 颜色变化回调
  onColorChange: function(e) {
    const color = e.detail.color;
    const { currentStopIndex, colorStops } = this.data;

    if (currentStopIndex === null) {
      return;
    }

    // 更新颜色
    const newColorStops = [...colorStops];
    newColorStops[currentStopIndex].color = color;

    // 计算文字颜色
    const textColor = colorUtils.getTextColorForBackground(color);

    this.setData({
      colorStops: newColorStops,
      currentTextColor: textColor
    });

    this.generateGradient();
  },

  // 颜色确认回调
  onColorConfirm: function(e) {
    const color = e.detail.color;
    const { currentStopIndex, colorStops } = this.data;

    if (currentStopIndex === null) {
      return;
    }

    // 更新颜色
    const newColorStops = [...colorStops];
    newColorStops[currentStopIndex].color = color;

    // 计算文字颜色
    const textColor = colorUtils.getTextColorForBackground(color);

    this.setData({
      colorStops: newColorStops,
      currentTextColor: textColor,
      showColorPicker: false
    });

    this.generateGradient();
  },

  // 应用预设渐变
  applyPreset: function(e) {
    const presetIndex = e.currentTarget.dataset.preset;
    const preset = this.data.presets[presetIndex];

    if (!preset) {
      return;
    }

    // 解析预设渐变
    this.parseGradient(preset.gradient);
  },



  // 辅助函数：解析颜色节点
  parseColorStops: function(params) {
    const colorStops = [];
    for (let i = 1; i < params.length; i++) {
      const colorStop = params[i].trim();

      // 提取颜色和位置
      const colorMatch = colorStop.match(/(#[0-9a-f]{3,8}|rgba?\(.*?\))/i);
      const positionMatch = colorStop.match(/\s+(\d+)%/);

      if (colorMatch) {
        const color = colorMatch[1];
        let position = 0;

        if (i === 1) {
          position = 0;
        } else if (i === params.length - 1) {
          position = 100;
        } else if (positionMatch && positionMatch[1]) {
          position = parseInt(positionMatch[1]);
        } else {
          // 均匀分布
          position = Math.round((i - 1) * 100 / (params.length - 2));
        }

        colorStops.push({ color, position });
      }
    }

    // 如果没有提取到颜色节点，使用默认值
    if (colorStops.length < 2) {
      colorStops.push({ color: '#ff416c', position: 0 });
      colorStops.push({ color: '#ff4b2b', position: 100 });
    }

    return colorStops;
  },

  // 解析渐变字符串
  parseGradient: function(gradientString) {
    // 判断渐变类型
    let gradientType = 'linear';
    if (gradientString.includes('radial-gradient')) {
      gradientType = 'radial';
    }

    // 提取参数
    const paramsMatch = gradientString.match(/gradient\((.*?)\)/);
    if (!paramsMatch || !paramsMatch[1]) {
      return;
    }

    const params = paramsMatch[1].split(',');

    if (gradientType === 'linear') {
      // 解析线性渐变
      let direction = 'to right';
      let angle = 90;

      if (params[0].includes('deg')) {
        // 角度形式
        const angleMatch = params[0].match(/(\d+)deg/);
        if (angleMatch && angleMatch[1]) {
          angle = parseInt(angleMatch[1]);
          // 使用辅助函数将角度转换为方向
          direction = this.angleToDirection(angle);
        }
      } else {
        // 方向形式
        direction = params[0].trim();
        // 使用辅助函数将方向转换为角度
        angle = this.directionToAngle(direction);
      }

      // 使用辅助函数解析颜色节点
      const colorStops = this.parseColorStops(params);

      // 找到对应的方向索引
      const directionIndex = this.data.directionOptions.findIndex(option => option.value === direction);

      // 更新数据
      this.setData({
        gradientType,
        direction,
        angle,
        colorStops,
        currentStopIndex: 0,
        directionIndex: directionIndex !== -1 ? directionIndex : 0
      });
    } else {
      // 解析径向渐变
      let shape = 'circle';
      let position = 'center';

      // 提取形状和位置
      const shapePositionMatch = params[0].match(/(circle|ellipse)(?:\s+at\s+([\w\s]+))?/);
      if (shapePositionMatch) {
        if (shapePositionMatch[1]) {
          shape = shapePositionMatch[1];
        }
        if (shapePositionMatch[2]) {
          position = shapePositionMatch[2];
        }
      }

      // 使用辅助函数解析颜色节点
      const colorStops = this.parseColorStops(params);

      // 更新数据
      this.setData({
        gradientType,
        shape,
        position,
        colorStops,
        currentStopIndex: 0
      });
    }

    // 生成渐变
    this.generateGradient();
  },

  // 生成渐变
  generateGradient: function() {
    const { gradientType, direction, angle, shape, position, colorStops } = this.data;

    // 确保颜色节点按位置排序
    const sortedColorStops = this.sortColorStops(colorStops);

    // 生成颜色节点字符串
    const colorStopsString = sortedColorStops
      .map(stop => `${stop.color} ${stop.position}%`)
      .join(', ');

    let gradientStyle = '';
    let gradientCode = '';

    if (gradientType === 'linear') {
      // 线性渐变
      gradientStyle = `background: linear-gradient(${direction}, ${colorStopsString});`;
      gradientCode = `linear-gradient(${direction}, ${colorStopsString})`;

      // 添加角度形式的代码
      gradientCode += `\n/* 或使用角度形式 */\nlinear-gradient(${angle}deg, ${colorStopsString})`;
    } else {
      // 径向渐变
      gradientStyle = `background: radial-gradient(${shape} at ${position}, ${colorStopsString});`;
      gradientCode = `radial-gradient(${shape} at ${position}, ${colorStopsString})`;
    }

    this.setData({
      gradientStyle,
      gradientCode
    });
  },

  // 复制渐变代码
  copyGradientCode: function(e) {
    const { gradientCode } = this.data;
    const item = e ? e.currentTarget.dataset.item || 'code' : 'code';

    wx.setClipboardData({
      data: gradientCode,
      success: () => {
        this.setData({
          copiedItem: item
        });

        setTimeout(() => {
          this.setData({
            copiedItem: ''
          });
        }, 1500);

        wx.showToast({
          title: '代码已复制',
          icon: 'success',
          duration: 1500
        });
      }
    });
  },

  // 复制颜色值
  copyColorValue: function(e) {
    const color = e.currentTarget.dataset.color;
    const item = e.currentTarget.dataset.item;

    if (!color) return;

    wx.setClipboardData({
      data: color,
      success: () => {
        this.setData({
          copiedItem: item
        });

        setTimeout(() => {
          this.setData({
            copiedItem: ''
          });
        }, 1500);

        wx.showToast({
          title: '颜色已复制',
          icon: 'success',
          duration: 1500
        });
      }
    });
  },

  // 保存渐变
  saveGradient: function() {
    const { gradientType, direction, shape, position, colorStops } = this.data;

    // 确保颜色节点按位置排序
    const sortedColorStops = this.sortColorStops(colorStops);

    // 生成颜色节点字符串
    const colorStopsString = sortedColorStops
      .map(stop => `${stop.color} ${stop.position}%`)
      .join(', ');

    let gradientString = '';

    if (gradientType === 'linear') {
      // 线性渐变
      gradientString = `linear-gradient(${direction}, ${colorStopsString})`;
    } else {
      // 径向渐变
      gradientString = `radial-gradient(${shape} at ${position}, ${colorStopsString})`;
    }

    // 创建新的保存对象
    const newGradient = {
      id: Date.now().toString(),
      gradient: gradientString,
      createTime: new Date().toISOString()
    };

    // 添加到保存列表
    const savedGradients = this.data.savedGradients;
    const newSavedGradients = [newGradient, ...savedGradients];

    // 限制保存数量为20
    if (newSavedGradients.length > 20) {
      newSavedGradients.pop();
    }

    // 保存到本地存储
    wx.setStorageSync('savedGradients', newSavedGradients);

    this.setData({
      savedGradients: newSavedGradients
    });

    wx.showToast({
      title: '渐变已保存',
      icon: 'success',
      duration: 1500
    });
  },

  // 生成随机颜色
  getRandomColor: function() {
    const letters = '0123456789ABCDEF';
    let color = '#';
    for (let i = 0; i < 6; i++) {
      color += letters[Math.floor(Math.random() * 16)];
    }
    return color;
  },

  // 根据背景色计算文字颜色 - 使用colorUtils模块

  // 保存为壁纸
  saveAsWallpaper: function() {
    const that = this;
    wx.showLoading({
      title: '生成中...',
    });

    // 获取系统信息，用于确定壁纸尺寸
    try {
      // 使用新的API获取窗口信息
      const windowInfo = wx.getWindowInfo();
      const screenWidth = windowInfo.screenWidth;
      const screenHeight = windowInfo.screenHeight;

      // 使用手机屏幕的实际比例，确保是竖屏壁纸
      // 对于手机壁纸，我们直接使用屏幕尺寸
      let wallpaperWidth = screenWidth;
      let wallpaperHeight = screenHeight;

      // 如果需要特定比例，可以使用9:16的竖屏比例
      // const aspectRatio = 9/16; // 竖屏比例
      // wallpaperWidth = screenHeight * aspectRatio;

      console.log(`生成壁纸尺寸: ${wallpaperWidth}x${wallpaperHeight}, 屏幕尺寸: ${screenWidth}x${screenHeight}`);

      // 获取 Canvas 节点
      const query = wx.createSelectorQuery();
      query.select('#wallpaperCanvas')
        .fields({ node: true, size: true })
        .exec((res) => {
            if (!res || !res[0] || !res[0].node) {
              wx.hideLoading();
              wx.showToast({
                title: '获取画布失败',
                icon: 'none'
              });
              return;
            }

            const canvas = res[0].node;
            const ctx = canvas.getContext('2d');

            // 设置画布尺寸
            canvas.width = wallpaperWidth;
            canvas.height = wallpaperHeight;

            // 直接使用渐变样式字符串
            const gradientStyle = that.data.gradientStyle;
            console.log('使用渐变样式:', gradientStyle);

            // 从样式字符串中提取渐变部分
            // 格式: background: xxx-gradient(...);
            const gradientMatch = gradientStyle.match(/background:\s*(.*);/);
            if (!gradientMatch || !gradientMatch[1]) {
              wx.hideLoading();
              wx.showToast({
                title: '解析渐变失败',
                icon: 'none'
              });
              return;
            }

            const gradientString = gradientMatch[1];

            // 根据渐变类型创建渐变
            if (that.data.gradientType === 'linear') {
              // 线性渐变
              // 解析方向
              const directionMatch = gradientString.match(/linear-gradient\(([^,]+),/);
              if (!directionMatch || !directionMatch[1]) {
                wx.hideLoading();
                wx.showToast({
                  title: '解析渐变方向失败',
                  icon: 'none'
                });
                return;
              }

              const direction = directionMatch[1].trim();

              // 设置渐变起点和终点
              let startX = 0, startY = 0, endX = 0, endY = 0;

              switch(direction) {
                case 'to right':
                  endX = wallpaperWidth;
                  break;
                case 'to left':
                  startX = wallpaperWidth;
                  break;
                case 'to bottom':
                  endY = wallpaperHeight;
                  break;
                case 'to top':
                  startY = wallpaperHeight;
                  break;
                case 'to right bottom':
                  endX = wallpaperWidth;
                  endY = wallpaperHeight;
                  break;
                case 'to left bottom':
                  startX = wallpaperWidth;
                  endY = wallpaperHeight;
                  break;
                case 'to right top':
                  endX = wallpaperWidth;
                  startY = wallpaperHeight;
                  break;
                case 'to left top':
                  startX = wallpaperWidth;
                  startY = wallpaperHeight;
                  break;
                default:
                  // 如果是角度形式，转换为方向
                  if (direction.includes('deg')) {
                    const angle = parseInt(direction);
                    // 根据角度计算起点和终点
                    // 注意：在CSS中，0deg表示从下到上，90deg表示从左到右
                    // 而在Canvas中，0弧度表示从右到左，Math.PI/2表示从上到下
                    // 所以需要进行转换
                    const radians = ((angle + 90) % 360) * (Math.PI / 180);
                    const distance = Math.max(wallpaperWidth, wallpaperHeight) * 2; // 确保足够长
                    startX = wallpaperWidth / 2;
                    startY = wallpaperHeight / 2;
                    endX = startX + Math.cos(radians) * distance;
                    endY = startY + Math.sin(radians) * distance;
                  } else {
                    // 默认从左到右
                    endX = wallpaperWidth;
                  }
              }

              const grd = ctx.createLinearGradient(startX, startY, endX, endY);

              // 提取颜色节点
              // 格式: linear-gradient(direction, color1 position1%, color2 position2%, ...)
              const colorStopsString = gradientString.replace(/linear-gradient\([^,]+,\s*/, '').replace(/\)$/, '');
              const colorStops = colorStopsString.split(',').map(s => s.trim());

              // 添加颜色节点
              colorStops.forEach(stop => {
                const parts = stop.match(/(#[0-9A-Fa-f]{3,6}|rgba?\([^)]+\))\s+(\d+)%/);
                if (parts) {
                  const color = parts[1];
                  const position = parseInt(parts[2]) / 100;
                  console.log(`添加线性渐变颜色节点: 位置=${position}, 颜色=${color}`);
                  grd.addColorStop(position, color);
                }
              });

              // 填充渐变
              ctx.fillStyle = grd;
              ctx.fillRect(0, 0, wallpaperWidth, wallpaperHeight);
            } else {
              // 径向渐变
              // 解析形状和位置
              const shapePositionMatch = gradientString.match(/radial-gradient\(([^,]+),/);
              if (!shapePositionMatch || !shapePositionMatch[1]) {
                wx.hideLoading();
                wx.showToast({
                  title: '解析渐变形状失败',
                  icon: 'none'
                });
                return;
              }

              const shapePosition = shapePositionMatch[1].trim();
              const shape = shapePosition.includes('circle') ? 'circle' : 'ellipse';

              // 设置渐变中心点
              let centerX = wallpaperWidth / 2;
              let centerY = wallpaperHeight / 2;

              if (shapePosition.includes('at')) {
                const position = shapePosition.split('at')[1].trim();

                if (position !== 'center') {
                  if (position.includes('top')) {
                    centerY = wallpaperHeight * 0.25;
                  } else if (position.includes('bottom')) {
                    centerY = wallpaperHeight * 0.75;
                  }

                  if (position.includes('left')) {
                    centerX = wallpaperWidth * 0.25;
                  } else if (position.includes('right')) {
                    centerX = wallpaperWidth * 0.75;
                  }
                }
              }

              // 设置渐变半径
              // 对于手机竖屏壁纸，使用对角线长度作为半径，确保渐变覆盖整个屏幕
              const radius = Math.sqrt(wallpaperWidth * wallpaperWidth + wallpaperHeight * wallpaperHeight) / 2;

              const grd = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);

              // 提取颜色节点
              // 格式: radial-gradient(shape at position, color1 position1%, color2 position2%, ...)
              const colorStopsString = gradientString.replace(/radial-gradient\([^,]+,\s*/, '').replace(/\)$/, '');
              const colorStops = colorStopsString.split(',').map(s => s.trim());

              // 添加颜色节点
              colorStops.forEach(stop => {
                const parts = stop.match(/(#[0-9A-Fa-f]{3,6}|rgba?\([^)]+\))\s+(\d+)%/);
                if (parts) {
                  const color = parts[1];
                  const position = parseInt(parts[2]) / 100;
                  console.log(`添加径向渐变颜色节点: 位置=${position}, 颜色=${color}`);
                  grd.addColorStop(position, color);
                }
              });

              // 填充渐变
              ctx.fillStyle = grd;
              ctx.fillRect(0, 0, wallpaperWidth, wallpaperHeight);
            }

            // 注意：这里不需要重新定义that变量，使用外部的that变量

            // 将画布内容保存为图片
            wx.canvasToTempFilePath({
              canvas: canvas,
              x: 0,
              y: 0,
              width: wallpaperWidth,
              height: wallpaperHeight,
              destWidth: wallpaperWidth, // 使用计算后的壁纸尺寸
              destHeight: wallpaperHeight, // 使用计算后的壁纸尺寸
              success: function(res) {
                wx.hideLoading();
                // 保存图片到相册
                wx.saveImageToPhotosAlbum({
                  filePath: res.tempFilePath,
                  success: function() {
                    console.log('壁纸保存成功，显示自定义提示');
                    // 显示自定义保存成功提示，使用保存的that引用
                    that.setData({
                      showSaveSuccess: true
                    });

                    // 2秒后隐藏提示
                    setTimeout(function() {
                      that.setData({
                        showSaveSuccess: false
                      });
                    }, 2000);
                  },
                  fail: function(err) {
                    console.error('保存失败', err);
                    // 如果是因为用户拒绝授权导致的失败
                    if (err.errMsg.indexOf('auth deny') >= 0 || err.errMsg.indexOf('authorize') >= 0) {
                      wx.showModal({
                        title: '提示',
                        content: '需要您授权保存图片到相册',
                        confirmText: '去授权',
                        success: function(res) {
                          if (res.confirm) {
                            wx.openSetting({
                              success: function(settingRes) {
                                console.log('设置状态：', settingRes);
                              }
                            });
                          }
                        }
                      });
                    } else {
                      wx.showToast({
                        title: '保存失败',
                        icon: 'none'
                      });
                    }
                  }
                });
              },
              fail: function(err) {
                wx.hideLoading();
                console.error('生成图片失败', err);
                wx.showToast({
                  title: '生成壁纸失败',
                  icon: 'none'
                });
              }
            });
          });
    } catch (error) {
      // 降级处理：使用默认尺寸
      console.error('获取窗口信息失败，使用默认尺寸', error);
      const defaultWidth = 375;
      const defaultHeight = 667;
      let wallpaperWidth = defaultWidth;
      let wallpaperHeight = defaultHeight;

      console.log(`降级方案 - 生成壁纸尺寸: ${wallpaperWidth}x${wallpaperHeight}`);

      // 获取 Canvas 节点
      const query = wx.createSelectorQuery();
      query.select('#wallpaperCanvas')
        .fields({ node: true, size: true })
        .exec((res) => {
          if (!res || !res[0] || !res[0].node) {
            wx.hideLoading();
            wx.showToast({
              title: '获取画布失败',
              icon: 'none'
            });
            return;
          }

          const canvas = res[0].node;
          const ctx = canvas.getContext('2d');

          // 设置画布尺寸
          canvas.width = wallpaperWidth;
          canvas.height = wallpaperHeight;

          // 使用默认渐变（简化处理）
          const grd = ctx.createLinearGradient(0, 0, wallpaperWidth, wallpaperHeight);
          grd.addColorStop(0, '#FF6B6B');
          grd.addColorStop(1, '#4ECDC4');

          ctx.fillStyle = grd;
          ctx.fillRect(0, 0, wallpaperWidth, wallpaperHeight);

          // 保存图片
          wx.canvasToTempFilePath({
            canvas: canvas,
            x: 0,
            y: 0,
            width: wallpaperWidth,
            height: wallpaperHeight,
            destWidth: wallpaperWidth,
            destHeight: wallpaperHeight,
            success: function(res) {
              wx.hideLoading();
              wx.saveImageToPhotosAlbum({
                filePath: res.tempFilePath,
                success: function() {
                  that.setData({
                    showSaveSuccess: true
                  });
                  setTimeout(function() {
                    that.setData({
                      showSaveSuccess: false
                    });
                  }, 2000);
                },
                fail: function(err) {
                  wx.showToast({
                    title: '保存失败',
                    icon: 'none'
                  });
                }
              });
            },
            fail: function(err) {
              wx.hideLoading();
              wx.showToast({
                title: '生成壁纸失败',
                icon: 'none'
              });
            }
          });
        });
    }
  },

  // 为当前选中的颜色节点生成随机颜色
  randomColorStop: function() {
    const { currentStopIndex, colorStops } = this.data;

    if (currentStopIndex === null) {
      return;
    }

    // 生成随机颜色
    const randomColor = this.getRandomColor();

    // 计算文字颜色
    const textColor = colorUtils.getTextColorForBackground(randomColor);

    // 更新颜色
    const newColorStops = [...colorStops];
    newColorStops[currentStopIndex].color = randomColor;

    this.setData({
      colorStops: newColorStops,
      currentTextColor: textColor
    });

    this.generateGradient();
  },

  /**
   * 异步加载保存的渐变列表
   */
  async loadSavedGradientsAsync() {
    try {
      // 使用异步方式获取存储数据
      const savedGradients = await new Promise((resolve) => {
        wx.getStorage({
          key: 'savedGradients',
          success: (res) => resolve(res.data || []),
          fail: () => resolve([])
        });
      });

      this.setData({
        savedGradients: savedGradients
      });

      console.log('[GradientGenerator] 保存的渐变列表加载完成');
    } catch (error) {
      console.error('异步加载保存的渐变失败', error);
      // 出错时使用默认值
      this.setData({
        savedGradients: []
      });
    }
  }
})
