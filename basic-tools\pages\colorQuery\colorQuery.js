// pages/colorQuery/colorQuery.js
// 直接引入颜色数据
const colorData = {
  categories: [
    {
      id: 0,
      name: "红",
      colors: [
        {"id": "0-0","hex": "#ffb3a7","name": "粉红","rgb": "255,179,167","cmyk": "0,30,35,0","intro": "即浅红色。别称：妃色、杨妃色、湘妃色、妃红色。"},
        {"id": "0-1","hex": "#ed5736","name": "妃色","rgb": "237,87,54","cmyk": "0,63,77,7","intro": "妃红色。古同\"绯\"，粉红色。杨妃色湘妃色粉红皆同义。"},
        {"id": "0-2","hex": "#f00056","name": "品红","rgb": "240,0,86","cmyk": "0,100,64,6","intro": "比大红浅的红色（这里的\"品红\"估计是指的\"一品红\"，是基于大红色系的，和现在我们印刷用色的\"品红M100\"不是一个概念）"},
        {"id": "0-3","hex": "#f47983","name": "桃红","rgb": "244,121,131","cmyk": "0,50,46,4","intro": "桃花的颜色，比粉红略鲜润的颜色。（不大于M70的色彩，有时可加入适量黄色）"},
        {"id": "0-4","hex": "#db5a6b","name": "海棠红","rgb": "219,90,107","cmyk": "0,59,51,14","intro": "淡紫红色、较桃红色深一些，是非常妩媚娇艳的颜色。"},
        {"id": "0-5","hex": "#f20c00","name": "石榴红","rgb": "242,12,0","cmyk": "0,95,100,5","intro": "石榴花的颜色，高色度和纯度的红色。"},
        {"id": "0-6","hex": "#c93756","name": "樱桃色","rgb": "201,55,86","cmyk": "0,73,57,21","intro": "鲜红色"},
        {"id": "0-7","hex": "#f05654","name": "银红","rgb": "240,86,84","cmyk": "0,64,65,6","intro": "银朱和粉红色颜料配成的颜色。多用来形容有光泽的各种红色，尤指有光泽浅红。"},
        {"id": "0-8","hex": "#ff2121","name": "大红","rgb": "255,33,33","cmyk": "0,87,87,0","intro": "正红色，三原色中的红，传统的中国红，又称绛色。（RGB色中的R255系列明度）"},
        {"id": "0-9","hex": "#8c4356","name": "绛紫","rgb": "140,67,86","cmyk": "0,52,39,45","intro": "紫中略带红的颜色"},
        {"id": "0-10","hex": "#c83c23","name": "绯红","rgb": "200,60,35","cmyk": "0,70,83,22","intro": "艳丽的深红"},
        {"id": "0-11","hex": "#9d2933","name": "胭脂","rgb": "157,41,51","cmyk": "0,74,68,38","intro": "1，女子装扮时用的胭脂的颜色。2，国画暗红色颜料。"},
        {"id": "0-12","hex": "#ff4c00","name": "朱红","rgb": "255,76,0","cmyk": "0,70,100,0","intro": "朱砂的颜色，比大红活泼，也称铅朱朱色丹色。（在YM对等的情况下，适量减少红色的成分就是该色的色彩系列感觉）"},
        {"id": "0-13","hex": "#ff4e20","name": "丹","rgb": "255,78,32","cmyk": "0,69,87,0","intro": "丹砂的鲜艳红色"},
        {"id": "0-14","hex": "#f35336","name": "彤","rgb": "243,83,54","cmyk": "0,66,78,5","intro": "赤色"},
        {"id": "0-15","hex": "#cb3a56","name": "茜色","rgb": "203,58,86","cmyk": "0,71,58,20","intro": "茜草染的色彩，呈深红色"},
        {"id": "0-16","hex": "#ff2d51","name": "火红","rgb": "255,45,81","cmyk": "0,82,68,0","intro": "火焰的红色，赤色"},
        {"id": "0-17","hex": "#c91f37","name": "赫赤","rgb": "201,31,55","cmyk": "0,85,73,21","intro": "深红，火红。泛指赤色、火红色。"},
        {"id": "0-18","hex": "#ef7a82","name": "嫣红","rgb": "239,122,130","cmyk": "0,49,46,6","intro": "鲜艳的红色"},
        {"id": "0-19","hex": "#ff0097","name": "洋红","rgb": "255,0,151","cmyk": "0,100,41,0","intro": "色橘红（这个色彩方向不太对，通常洋红指的是倾向于M100系列的红色，应该削弱黄色成分。）"},
        {"id": "0-20","hex": "#ff3300","name": "炎","rgb": "255,51,0","cmyk": "0,80,100,0","intro": "引申为红色"},
        {"id": "0-21","hex": "#c3272b","name": "赤","rgb": "195,39,43","cmyk": "0,80,78,24","intro": "本义火的颜色，即红色。"},
        {"id": "0-22","hex": "#a98175","name": "绾","rgb": "169,129,117","cmyk": "0,24,31,34","intro": "绛色；浅绛色。"},
        {"id": "0-23","hex": "#c32136","name": "枣红","rgb": "195,33,54","cmyk": "0,83,72,24","intro": "即深红（色相不变，是深浅变化）"},
        {"id": "0-24","hex": "#b36d61","name": "檀","rgb": "179,109,97","cmyk": "0,39,46,30","intro": "浅红色，浅绛色。"},
        {"id": "0-25","hex": "#be002f","name": "殷红","rgb": "190,0,47","cmyk": "0,100,75,25","intro": "发黑的红色。"},
        {"id": "0-26","hex": "#dc3023","name": "酡红","rgb": "220,48,35","cmyk": "0,78,84,14","intro": "像饮酒后脸上泛现的红色，泛指脸红。"},
        {"id": "0-27","hex": "#f9906f","name": "酡颜","rgb": "249,144,111","cmyk": "0,42,55,2","intro": "饮酒脸红的样子，亦泛指脸红色。"}
      ]
    },
    {
      id: 1,
      name: "黄",
      colors: [
        {"id": "1-0","hex": "#fff143","name": "鹅黄","rgb": "255,241,67","cmyk": "0,5,74,0","intro": "淡黄色（鹅嘴的颜色，高明度微偏红黄色）"},
        {"id": "1-1","hex": "#faff72","name": "鸭黄","rgb": "250,255,114","cmyk": "2,0,55,0","intro": "小鸭毛的黄色"},
        {"id": "1-2","hex": "#eaff56","name": "樱草色","rgb": "234,255,86","cmyk": "8,0,66,0","intro": "淡黄色"},
        {"id": "1-3","hex": "#ffa631","name": "杏黄","rgb": "255,166,49","cmyk": "0,35,81,0","intro": "成熟杏子的黄色（Y100M20~30感觉的色彩，比较常用且有浓郁中国味道）"},
        {"id": "1-4","hex": "#ff8c31","name": "杏红","rgb": "255,140,49","cmyk": "0,45,81,0","intro": "成熟杏子偏红色的一种颜色"},
        {"id": "1-5","hex": "#ff8936","name": "橘黄","rgb": "255,137,54","cmyk": "0,46,79,0","intro": "柑橘的黄色"},
        {"id": "1-6","hex": "#ffa400","name": "橙黄","rgb": "255,164,0","cmyk": "0,36,100,0","intro": "同上。（Y100M50感觉的色彩，现代感比较强。广告上用得较多）"},
        {"id": "1-7","hex": "#ff7500","name": "橘红","rgb": "255,117,0","cmyk": "0,54,100,0","intro": "柑橘皮所呈现的红色"},
        {"id": "1-8","hex": "#ffc773","name": "姜黄","rgb": "255,199,115","cmyk": "0,22,55,0","intro": "中药名。别名黄姜。为姜科植物姜黄的根茎。又指人脸色不正,呈黄白色。"},
        {"id": "1-9","hex": "#f0c239","name": "缃色","rgb": "240,194,57","cmyk": "0,19,76,6","intro": "浅黄色"},
        {"id": "1-10","hex": "#fa8c35","name": "橙色","rgb": "250,140,53","cmyk": "0,44,79,2","intro": "界于红色和黄色之间的混合色"},
        {"id": "1-11","hex": "#b35c44","name": "茶色","rgb": "179,92,68","cmyk": "0,49,62,30","intro": "一种比栗色稍红的棕橙色至浅棕色"},
        {"id": "1-12","hex": "#a88462","name": "驼色","rgb": "168,132,98","cmyk": "0,21,42,34","intro": "一种比咔叽色稍红而微淡、比肉桂色黄而稍淡和比核桃棕色黄而暗的浅黄棕色。"},
        {"id": "1-13","hex": "#c89b40","name": "昏黄","rgb": "200,155,64","cmyk": "0,23,68,22","intro": "形容天色、灯光等呈幽暗的黄色。"},
        {"id": "1-14","hex": "#60281e","name": "栗色","rgb": "96,40,30","cmyk": "0,58,69,62","intro": "栗壳的颜色，即紫黑色。"},
        {"id": "1-15","hex": "#b25d25","name": "棕色","rgb": "178,93,37","cmyk": "0,48,79,30","intro": "棕毛的颜色,即褐色。1，在红色和黄色之间的任何一种颜色。2，适中的暗淡和适度的浅黑。"},
        {"id": "1-16","hex": "#827100","name": "棕绿","rgb": "130,113,0","cmyk": "0,13,100,49","intro": "绿中泛棕色的一种颜色"},
        {"id": "1-17","hex": "#7c4b00","name": "棕黑","rgb": "124,75,0","cmyk": "0,40,100,51","intro": "深棕色"},
        {"id": "1-18","hex": "#9b4400","name": "棕红","rgb": "155,68,0","cmyk": "0,56,100,39","intro": "红褐色"},
        {"id": "1-19","hex": "#ae7000","name": "棕黄","rgb": "174,112,0","cmyk": "0,36,100,32","intro": "浅褐色"},
        {"id": "1-20","hex": "#9c5333","name": "赭","rgb": "156,83,51","cmyk": "0,47,67,39","intro": "赤红如赭土的颜料,古人用以饰面。"},
        {"id": "1-21","hex": "#955539","name": "赭色","rgb": "149,85,57","cmyk": "0,43,62,42","intro": "红色、赤红色。"},
        {"id": "1-22","hex": "#ca6924","name": "琥珀","rgb": "202,105,36","cmyk": "0,48,82,21","intro": ""},
        {"id": "1-23","hex": "#6e511e","name": "褐色","rgb": "110,81,30","cmyk": "0,26,73,57","intro": "黄黑色"},
        {"id": "1-24","hex": "#d3b17d","name": "枯黄","rgb": "211,177,125","cmyk": "0,16,41,17","intro": "干枯焦黄"},
        {"id": "1-25","hex": "#e29c45","name": "黄栌","rgb": "226,156,69","cmyk": "0,31,69,11","intro": "一种落叶灌木，花黄绿色,叶子秋天变成红色。木材黄色可做染料。"},
        {"id": "1-26","hex": "#896c39","name": "秋色","rgb": "137,108,57","cmyk": "0,21,58,46","intro": "1，中常橄榄棕色,它比一般橄榄棕色稍暗,且稍稍绿些。2，古以秋为金,其色白,故代指白色。"},
        {"id": "1-27","hex": "#d9b611","name": "秋香色","rgb": "217,182,17","cmyk": "0,16,92,15","intro": "浅橄榄色浅黄绿色。（直接在Y中掺入k10~30可得到不同浓淡的该类色彩）"}
      ]
    },
    {
      id: 2,
      name: "绿",
      colors: [
        {"id": "2-0","hex": "#bddd22","name": "嫩绿","rgb": "189,221,34","cmyk": "14,0,85,13","intro": "像刚长出的嫩叶的浅绿色"},
        {"id": "2-1","hex": "#c9dd22","name": "柳黄","rgb": "201,221,34","cmyk": "9,0,85,13","intro": "像柳树芽那样的浅黄色"},
        {"id": "2-2","hex": "#afdd22","name": "柳绿","rgb": "175,221,34","cmyk": "21,0,85,13","intro": "柳叶的青绿色"},
        {"id": "2-3","hex": "#789262","name": "竹青","rgb": "120,146,98","cmyk": "18,0,33,43","intro": "竹子的绿色"},
        {"id": "2-4","hex": "#a3d900","name": "葱黄","rgb": "163,217,0","cmyk": "25,0,100,15","intro": "黄绿色，嫩黄色。"},
        {"id": "2-5","hex": "#9ed900","name": "葱绿","rgb": "158,217,0","cmyk": "27,0,100,15","intro": "1，浅绿又略显微黄的颜色。2，草木青翠的样子。"},
        {"id": "2-6","hex": "#0eb83a","name": "葱青","rgb": "14,184,58","cmyk": "92,0,68,28","intro": "淡淡的青绿色"},
        {"id": "2-7","hex": "#0eb840","name": "葱倩","rgb": "14,184,64","cmyk": "92,0,65,28","intro": "青绿色"},
        {"id": "2-8","hex": "#0aa344","name": "青葱","rgb": "10,163,68","cmyk": "94,0,58,36","intro": "翠绿色,形容植物浓绿。"},
        {"id": "2-9","hex": "#00bc12","name": "油绿","rgb": "0,188,18","cmyk": "100,0,90,26","intro": "光润而浓绿的颜色。以上几种绿色都是明亮可爱的色彩。"},
        {"id": "2-10","hex": "#0c8918","name": "绿沈","rgb": "12,137,24","cmyk": "91,0,82,46","intro": "（沉）深绿"},
        {"id": "2-11","hex": "#1bd1a5","name": "碧色","rgb": "27,209,165","cmyk": "87,0,21,18","intro": "1，青绿色。2，青白色,浅蓝色。"},
        {"id": "2-12","hex": "#2add9c","name": "碧绿","rgb": "42,221,156","cmyk": "81,0,29,13","intro": "鲜艳的青绿色"},
        {"id": "2-13","hex": "#48c0a3","name": "青碧","rgb": "72,192,163","cmyk": "62,0,15,25","intro": "鲜艳的青蓝色"},
        {"id": "2-14","hex": "#3de1ad","name": "翡翠色","rgb": "61,225,173","cmyk": "73,0,23,12","intro": "1，翡翠鸟羽毛的青绿色。2，翡翠宝石的颜色。（C-Y≥30的系列色彩，多与白色配合以体现清新明丽感觉，与黑色配合效果不好该色个性柔弱，会被黑色牵制）"},
        {"id": "2-15","hex": "#40de5a","name": "草绿","rgb": "64,222,90","cmyk": "71,0,60,13","intro": "绿而略黄的颜色。"},
        {"id": "2-16","hex": "#00e09e","name": "青色","rgb": "0,224,158","cmyk": "100,0,30,12","intro": "1，一类带绿的蓝色,中等深浅,高度饱和。2，特指三补色中的一色。3，本义是蓝色。4，一般指深绿色。5，也指黑色。6，四色印刷中的一色。"},
        {"id": "2-17","hex": "#00e079","name": "青翠","rgb": "0,224,121","cmyk": "100,0,46,12","intro": "鲜绿"},
        {"id": "2-18","hex": "#c0ebd7","name": "青白","rgb": "192,235,215","cmyk": "18,0,9,8","intro": "白而发青,尤指脸没有血色。"},
        {"id": "2-19","hex": "#e0eee8","name": "鸭卵青","rgb": "224,238,232","cmyk": "6,0,3,7","intro": "淡青灰色，极淡的青绿色。"},
        {"id": "2-20","hex": "#bbcdc5","name": "蟹壳青","rgb": "187,205,197","cmyk": "9,0,4,20","intro": "深灰绿色"},
        {"id": "2-21","hex": "#424c50","name": "鸦青","rgb": "66,76,80","cmyk": "18,5,0,69","intro": "鸦羽的颜色，即黑而带有紫绿光的颜色。"},
        {"id": "2-22","hex": "#00e500","name": "绿色","rgb": "0,229,0","cmyk": "100,0,100,10","intro": "1，在光谱中介于蓝与黄之间的那种颜色。2，本义青中带黄的颜色。3，引申为黑色，如绿鬓乌黑而光亮的鬓发。代指为青春年少的容颜。（现代色彩研究中，把绿色提高到了一个重要的位置，和其它红黄兰三原色并列研究，称做\"心理原色\"之一）"},
        {"id": "2-23","hex": "#9ed048","name": "豆绿","rgb": "158,208,72","cmyk": "24,0,65,18","intro": "浅黄绿色"},
        {"id": "2-24","hex": "#96ce54","name": "豆青","rgb": "150,206,84","cmyk": "27,0,59,19","intro": "浅青绿色"},
        {"id": "2-25","hex": "#7bcfa6","name": "石青","rgb": "123,207,166","cmyk": "41,0,20,19","intro": "淡灰绿色"},
        {"id": "2-26","hex": "#2edfa3","name": "玉色","rgb": "46,223,163","cmyk": "79,0,27,13","intro": "玉的颜色，高雅的淡绿、淡青色。"},
        {"id": "2-27","hex": "#7fecad","name": "缥","rgb": "127,236,173","cmyk": "46,0,27,7","intro": "绿色而微白"},
        {"id": "2-28","hex": "#a4e2c6","name": "艾绿","rgb": "164,226,198","cmyk": "27,0,12,11","intro": "艾草的颜色，偏苍白的绿色。"},
        {"id": "2-29","hex": "#21a675","name": "松柏绿","rgb": "33,166,117","cmyk": "80,0,30,35","intro": "经冬松柏叶的深绿"},
        {"id": "2-30","hex": "#057748","name": "松花绿","rgb": "5,119,72","cmyk": "96,0,40,53","intro": "亦作\"松花\"、\"松绿\"。偏黑的深绿色,墨绿。"},
        {"id": "2-31","hex": "#bce672","name": "松花色","rgb": "188,230,114","cmyk": "18,0,50,10","intro": "浅黄绿色。（松树花粉的颜色）《红楼梦》中提及松花配桃红为娇艳。"}
      ]
    },
    {
      id: 3,
      name: "蓝",
      colors: [
        {"id": "3-0","hex": "#44cef6","name": "蓝","rgb": "68,206,246","cmyk": "73,16,0,4","intro": "三原色的一种。像晴天天空的颜色（这里的蓝色指的不是RGB色彩中的B，而是CMY色彩中的C）"},
        {"id": "3-1","hex": "#177cb0","name": "靛青","rgb": "23,124,176","cmyk": "87,30,0,31","intro": "也叫\"蓝靛\"。用蓼蓝叶泡水调和与石灰沉淀所得的蓝色染料。呈深蓝绿色（靛，发音dian四声，有些地方将蓝墨水称为\"靛水\"或者\"兰靛水\"）"},
        {"id": "3-2","hex": "#065279","name": "靛蓝","rgb": "6,82,121","cmyk": "95,32,0,53","intro": "由植物(例如靛蓝或菘蓝属植物)得到的蓝色染料"},
        {"id": "3-3","hex": "#3eede7","name": "碧蓝","rgb": "62,237,231","cmyk": "74,0,3,7","intro": "青蓝色"},
        {"id": "3-4","hex": "#70f3ff","name": "蔚蓝","rgb": "112,243,255","cmyk": "56,5,0,0","intro": "类似晴朗天空的颜色的一种蓝色"},
        {"id": "3-5","hex": "#4b5cc4","name": "宝蓝","rgb": "75,92,196","cmyk": "62,53,0,23","intro": "鲜艳明亮的蓝色（英文中为RoyalBlue即皇家蓝色，是皇室选用的色彩，多和小面积纯黄色（金色）配合使用。）"},
        {"id": "3-6","hex": "#a1afc9","name": "蓝灰色","rgb": "161,175,201","cmyk": "20,13,0,21","intro": "一种近于灰略带蓝的深灰色"},
        {"id": "3-7","hex": "#2e4e7e","name": "藏青","rgb": "46,78,126","cmyk": "63,38,0,51","intro": "蓝而近黑"},
        {"id": "3-8","hex": "#3b2e7e","name": "藏蓝","rgb": "59,46,126","cmyk": "53,63,0,51","intro": "蓝里略透红色"},
        {"id": "3-9","hex": "#4a4266","name": "黛","rgb": "74,66,102","cmyk": "27,35,0,60","intro": "别名：黛色，黛螺。青黑色的颜料。古代女子用以画眉。绘画或画眉所使用的青黑色颜料，代指女子眉妩。"},
        {"id": "3-10","hex": "#426666","name": "黛绿","rgb": "66,102,102","cmyk": "35,0,0,60","intro": "墨绿"},
        {"id": "3-11","hex": "#425066","name": "黛蓝","rgb": "66,80,102","cmyk": "35,22,0,60","intro": "深蓝色"},
        {"id": "3-12","hex": "#574266","name": "黛紫","rgb": "87,66,102","cmyk": "15,35,0,60","intro": "深紫色"},
        {"id": "3-13","hex": "#8d4bbb","name": "紫色","rgb": "141,75,187","cmyk": "25,60,0,27","intro": "蓝和红组成的颜色。古人以紫为祥瑞的颜色。代指与帝王、皇宫有关的事物"},
        {"id": "3-14","hex": "#815463","name": "紫酱","rgb": "129,84,99","cmyk": "0,35,23,49","intro": "浑浊的紫色"},
        {"id": "3-15","hex": "#815476","name": "酱紫","rgb": "129,84,118","cmyk": "0,35,9,49","intro": "紫中略带红的颜色"},
        {"id": "3-16","hex": "#4c221b","name": "紫檀","rgb": "76,34,27","cmyk": "0,55,64,70","intro": "檀木的颜色，也称乌檀色乌木色"},
        {"id": "3-17","hex": "#003371","name": "绀青绀紫","rgb": "0,51,113","cmyk": "100,55,0,56","intro": "纯度较低的深紫色"},
        {"id": "3-18","hex": "#56004f","name": "紫棠","rgb": "86,0,79","cmyk": "0,100,8,66","intro": "黑红色"},
        {"id": "3-19","hex": "#801dae","name": "青莲","rgb": "128,29,174","cmyk": "26,83,0,32","intro": "偏蓝的紫色"},
        {"id": "3-20","hex": "#4c8dae","name": "群青","rgb": "76,141,174","cmyk": "56,19,0,32","intro": "深蓝色"},
        {"id": "3-21","hex": "#b0a4e3","name": "雪青","rgb": "176,164,227","cmyk": "22,28,0,11","intro": "浅蓝紫色"},
        {"id": "3-22","hex": "#cca4e3","name": "丁香色","rgb": "204,164,227","cmyk": "10,28,0,11","intro": "紫丁香的颜色，浅浅的紫色，很娇柔淡雅的色彩"},
        {"id": "3-23","hex": "#edd1d8","name": "藕色","rgb": "237,209,216","cmyk": "0,12,9,7","intro": "浅灰而略带红的颜色"},
        {"id": "3-24","hex": "#e4c6d0","name": "藕荷色","rgb": "228,198,208","cmyk": "0,13,9,11","intro": "浅紫而略带红的颜色"}
      ]
    },
    {
      id: 4,
      name: "苍",
      colors: [
        {"id": "4-0","hex": "#75878a","name": "苍色","rgb": "117,135,138","cmyk": "15,2,0,46","intro": "即各种颜色掺入黑色后的颜色"},
        {"id": "4-1","hex": "#519a73","name": "苍翠","rgb": "81,154,115","cmyk": "47,0,25,40","intro": ""},
        {"id": "4-2","hex": "#a29b7c","name": "苍黄","rgb": "162,155,124","cmyk": "0,4,23,36","intro": ""},
        {"id": "4-3","hex": "#7397ab","name": "苍青","rgb": "115,151,171","cmyk": "33,12,0,33","intro": ""},
        {"id": "4-4","hex": "#395260","name": "苍黑","rgb": "57,82,96","cmyk": "41,15,0,62","intro": ""},
        {"id": "4-5","hex": "#d1d9e0","name": "苍白","rgb": "209,217,224","cmyk": "7,3,0,12","intro": "准确的说是掺入不同灰度级别的灰色"}
      ]
    },
    {
      id: 5,
      name: "水",
      colors: [
        {"id": "5-0","hex": "#88ada6","name": "水色","rgb": "136,173,166","cmyk": "21,0,4,32","intro": ""},
        {"id": "5-1","hex": "#f3d3e7","name": "水红","rgb": "243,211,231","cmyk": "0,13,5,5","intro": ""},
        {"id": "5-2","hex": "#d4f2e7","name": "水绿","rgb": "212,242,231","cmyk": "12,0,5,5","intro": ""},
        {"id": "5-3","hex": "#d2f0f4","name": "水蓝","rgb": "210,240,244","cmyk": "14,2,0,4","intro": ""},
        {"id": "5-4","hex": "#d3e0f3","name": "淡青","rgb": "211,224,243","cmyk": "13,8,0,5","intro": ""},
        {"id": "5-5","hex": "#30dff3","name": "湖蓝","rgb": "48,223,243","cmyk": "80,8,0,5","intro": ""},
        {"id": "5-6","hex": "#25f8cb","name": "湖绿","rgb": "37,248,203","cmyk": "85,0,18,3","intro": "皆是浅色。深色淡色，颜色深的或浅的，不再一一列出。"}
      ]
    },
    {
      id: 6,
      name: "灰白",
      colors: [
        {"id": "6-0","hex": "#ffffff","name": "精白","rgb": "255,255,255","cmyk": "0,0,0,0","intro": "纯白，洁白，净白，粉白。"},
        {"id": "6-1","hex": "#fffbf0","name": "象牙白","rgb": "255,251,240","cmyk": "0,2,6,0","intro": "乳白色"},
        {"id": "6-2","hex": "#f2fdff","name": "雪白","rgb": "242,253,255","cmyk": "5,1,0,0","intro": "如雪般洁白"},
        {"id": "6-3","hex": "#d6ecf0","name": "月白","rgb": "214,236,240","cmyk": "11,2,0,6","intro": "淡蓝色"},
        {"id": "6-4","hex": "#f2ecde","name": "缟","rgb": "242,236,222","cmyk": "0,3,8,5","intro": "白色"},
        {"id": "6-5","hex": "#e0f0e9","name": "素","rgb": "224,240,233","cmyk": "7,0,3,6","intro": "白色，无色"},
        {"id": "6-6","hex": "#f3f9f1","name": "荼白","rgb": "243,249,241","cmyk": "2,0,3,2","intro": "如荼之白色"},
        {"id": "6-7","hex": "#e9f1f6","name": "霜色","rgb": "233,241,246","cmyk": "5,2,0,4","intro": "白霜的颜色。"},
        {"id": "6-8","hex": "#c2ccd0","name": "花白","rgb": "194,204,208","cmyk": "7,2,0,18","intro": "白色和黑色混杂的。斑白的夹杂有灰色的白"},
        {"id": "6-9","hex": "#fcefe8","name": "鱼肚白","rgb": "252,239,232","cmyk": "0,5,8,1","intro": "似鱼腹部的颜色，多指黎明时东方的天色颜色（M5Y5）"},
        {"id": "6-10","hex": "#e3f9fd","name": "莹白","rgb": "227,249,253","cmyk": "10,2,0,1","intro": "晶莹洁白"},
        {"id": "6-11","hex": "#808080","name": "灰色","rgb": "128,128,128","cmyk": "0,0,0,50","intro": "黑色和白色混和成的一种颜色"},
        {"id": "6-12","hex": "#eedeb0","name": "牙色","rgb": "238,222,176","cmyk": "0,7,26,7","intro": "与象牙相似的淡黄色（暖白）"},
        {"id": "6-13","hex": "#f0f0f4","name": "铅白","rgb": "240,240,244","cmyk": "2,2,0,4","intro": "铅粉的白色。铅粉，国画颜料，日久易氧化\"返铅\"变黑。铅粉在古时用以搽脸的化妆品。（冷白）"}
      ]
    },
    {
      id: 7,
      name: "黑",
      colors: [
        {"id": "7-0","hex": "#622a1d","name": "玄色","rgb": "98,42,29","cmyk": "0,57,70,62","intro": "赤黑色，黑中带红的颜色，又泛指黑色"},
        {"id": "7-1","hex": "#3d3b4f","name": "玄青","rgb": "61,59,79","cmyk": "23,25,0,69","intro": "深黑色"},
        {"id": "7-2","hex": "#725e82","name": "乌色","rgb": "114,94,130","cmyk": "12,28,0,49","intro": "暗而呈黑的颜色"},
        {"id": "7-3","hex": "#392f41","name": "乌黑","rgb": "57,47,65","cmyk": "12,28,0,75","intro": "深黑；漆黑"},
        {"id": "7-4","hex": "#161823","name": "漆黑","rgb": "22,24,35","cmyk": "37,31,0,86","intro": "非常黑的"},
        {"id": "7-5","hex": "#50616d","name": "墨色","rgb": "80,97,109","cmyk": "27,11,0,57","intro": "即黑色"},
        {"id": "7-6","hex": "#758a99","name": "墨灰","rgb": "117,138,153","cmyk": "24,10,0,40","intro": "即黑灰"},
        {"id": "7-7","hex": "#000000","name": "黑色","rgb": "0,0,0","cmyk": "0,0,0,100","intro": "亮度最低的非彩色的或消色差的物体的颜色；最暗的灰色；与白色截然不同的消色差的颜色；被认为特别属于那些既不能反射、又不能透过能使人感觉到的微小入射光的物体,任何亮度很低的物体颜色。"},
        {"id": "7-8","hex": "#493131","name": "缁色","rgb": "73,49,49","cmyk": "0,33,33,71","intro": "帛黑色"},
        {"id": "7-9","hex": "#312520","name": "煤黑","rgb": "49,37,32","cmyk": "0,24,35,81","intro": "别称：象牙黑。都是黑，不过有冷暖之分"},
        {"id": "7-10","hex": "#5d513c","name": "黧","rgb": "93,81,60","cmyk": "0,13,35,64","intro": "黑中带黄的颜色"},
        {"id": "7-11","hex": "#75664d","name": "黎","rgb": "117,102,77","cmyk": "0,13,34,54","intro": "黑中带黄似黎草色"},
        {"id": "7-12","hex": "#6b6882","name": "黝","rgb": "107,104,130","cmyk": "18,20,0,49","intro": "本义为淡黑色或微青黑色。"},
        {"id": "7-13","hex": "#665757","name": "黝黑","rgb": "102,87,87","cmyk": "0,15,15,60","intro": "（皮肤暴露在太阳光下而晒成的）青黑色"},
        {"id": "7-14","hex": "#41555d","name": "黯","rgb": "65,85,93","cmyk": "30,9,0,64","intro": "深黑色、泛指黑色"}
      ]
    },
    {
      id: 8,
      name: "金银",
      colors: [
        {"id": "8-0","hex": "#f2be45","name": "赤金","rgb": "242,190,69","cmyk": "0,21,71,5","intro": "足金的颜色"},
        {"id": "8-1","hex": "#eacd76","name": "金色","rgb": "234,205,118","cmyk": "0,12,50,8","intro": "平均为深黄色带光泽的颜色"},
        {"id": "8-2","hex": "#e9e7ef","name": "银白","rgb": "233,231,239","cmyk": "3,3,0,6","intro": "带银光的白色"},
        {"id": "8-3","hex": "#bacac6","name": "老银","rgb": "186,202,198","cmyk": "8,0,2,21","intro": "金属氧化后的色彩"},
        {"id": "8-4","hex": "#a78e44","name": "乌金","rgb": "167,142,68","cmyk": "0,15,59,35","intro": ""},
        {"id": "8-5","hex": "#549688","name": "铜绿","rgb": "84,150,136","cmyk": "44,0,9,41","intro": ""}
      ]
    }
  ]
};

Page({
  data: {
    currentColors: [], // 当前显示的颜色列表
    navBarHeight: 0, // 导航栏高度
    categories: colorData.categories, // 使用从Excel导入的颜色数据
    activeCategory: 1,
    categoryColors: {
      0: "#c83c23", // 红 - 绯红色
      1: "#ffa631", // 黄 - 杏黄色
      2: "#39a01a", // 绿 - 草绿色（更柔和的绿色）
      3: "#4169e1", // 蓝 - 宝蓝色（更深沉、高贵的蓝色）
      4: "#75878a", // 苍 - 苍色
      5: "#88ada6", // 水 - 水色
      6: "#a7a8bd", // 灰白 - 淡紫灰色（更易于辨识）
      7: "#000000", // 黑 - 黑色
      8: "#eacd76"  // 金银 - 金色
    },
    activeCategoryColor: "#c83c23", // 默认选中红色
    touchStartX: 0, // 触摸开始位置X坐标
  },

  onLoad: function (options) {
    // 页面加载时，默认选中第一个分类
    const firstCategory = this.data.categories[0];
    const firstCategoryIndex = 0; // 第一个分类的索引
    const categoryColor = this.data.categoryColors[firstCategoryIndex] || "#4a90e2"; // 默认蓝色

    this.setData({
      activeCategory: firstCategory.id,
      currentColors: firstCategory.colors,
      activeCategoryColor: categoryColor
    });

    // 获取导航栏高度
    this.getNavBarHeight();
  },

  // 安全地获取导航栏高度
  getNavBarHeight: function() {
    const that = this;

    // 先设置默认值，确保页面能正常显示
    that.setData({
      navBarHeight: 25,
      totalTopHeight: 30
    });

    // 延迟获取精确值，避免过早调用API
    setTimeout(() => {
      try {
        // 优先使用全局缓存的系统信息
        const app = getApp();
        if (app && app.globalData && app.globalData.systemInfoReady) {
          const statusBarHeight = app.globalData.statusBarHeight || 20;
          const navBarHeight = statusBarHeight + 5;
          const categoryHeight = 5;

          that.setData({
            navBarHeight: navBarHeight,
            totalTopHeight: navBarHeight + categoryHeight
          });
          return;
        }

        // 如果全局信息不可用，尝试获取窗口信息
        const windowInfo = wx.getWindowInfo();
        const statusBarHeight = windowInfo.statusBarHeight;
        const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
        const navBarHeight = statusBarHeight + 5;
        const categoryHeight = 5;

        that.setData({
          navBarHeight: navBarHeight,
          totalTopHeight: navBarHeight + categoryHeight
        });
      } catch (error) {
        // 保持默认值，静默处理错误
      }
    }, 100);
  },

  // 切换分类
  switchCategory: function (e) {
    const categoryId = parseInt(e.currentTarget.dataset.id);

    // 查找对应分类的颜色列表
    const category = this.data.categories.find(c => c.id === categoryId);
    if (category) {
      // 获取分类对应的代表色
      const categoryIndex = this.data.categories.findIndex(c => c.id === categoryId);
      const categoryColor = this.data.categoryColors[categoryIndex] || "#4a90e2"; // 默认蓝色

      this.setData({
        activeCategory: categoryId,
        currentColors: category.colors,
        activeCategoryColor: categoryColor
      });
    }
  },



  // 跳转到颜色详情页
  goToColorDetail: function (e) {
    const colorId = e.currentTarget.dataset.id;
    const colorData = this.findColorById(colorId);

    if (colorData) {
      // 找到当前分类中的颜色索引
      const category = this.data.categories.find(c => c.id === this.data.activeCategory);
      const colorIndex = category.colors.findIndex(c => c.id === colorId);

      // 将当前分类的所有颜色数据传递给详情页
      const colorsData = encodeURIComponent(JSON.stringify(category.colors));

      wx.navigateTo({
        url: `/basic-tools/pages/colorDetail/colorDetail?id=${colorId}&name=${colorData.name}&hex=${encodeURIComponent(colorData.hex)}&rgb=${encodeURIComponent(colorData.rgb)}&cmyk=${encodeURIComponent(colorData.cmyk)}&index=${colorIndex}&total=${category.colors.length}&categoryId=${category.id}&colorsData=${colorsData}&intro=${encodeURIComponent(colorData.intro || '')}`,
      });
    }
  },

  // 根据ID查找颜色数据
  findColorById: function (colorId) {
    for (const category of this.data.categories) {
      for (const color of category.colors) {
        if (color.id === colorId) {
          return color;
        }
      }
    }
    return null;
  },

  // 用户点击右上角分享或使用分享按钮
  onShareAppMessage: function() {
    // 获取当前分类名称
    const category = this.data.categories.find(c => c.id === this.data.activeCategory);
    const categoryName = category ? category.name : '中国传统色彩';

    return {
      title: categoryName + ' - 中国传统色彩查询',
      path: '/basic-tools/pages/colorQuery/colorQuery?category=' + this.data.activeCategory,
      imageUrl: '/assets/images/share-color-query.png' // 分享图片
    };
  },

  // 设置页面标题
  onShow: function() {
    wx.setNavigationBarTitle({
      title: '中国传统色'
    });
  },

  // 处理触摸开始事件
  handleTouchStart: function(e) {
    this.setData({
      touchStartX: e.touches[0].clientX
    });
  },

  // 处理触摸结束事件
  handleTouchEnd: function(e) {
    const touchEndX = e.changedTouches[0].clientX;
    const touchStartX = this.data.touchStartX;

    // 计算滑动距离
    const distance = touchEndX - touchStartX;

    // 如果滑动距离太小，不处理
    if (Math.abs(distance) < 50) return;

    // 获取当前分类的索引
    const currentIndex = this.data.categories.findIndex(c => c.id === this.data.activeCategory);

    // 向左滑动，切换到下一个分类
    if (distance < 0 && currentIndex < this.data.categories.length - 1) {
      const nextCategory = this.data.categories[currentIndex + 1];
      this.switchCategory({ currentTarget: { dataset: { id: nextCategory.id } } });
    }
    // 向右滑动，切换到上一个分类
    else if (distance > 0 && currentIndex > 0) {
      const prevCategory = this.data.categories[currentIndex - 1];
      this.switchCategory({ currentTarget: { dataset: { id: prevCategory.id } } });
    }
  }
})
