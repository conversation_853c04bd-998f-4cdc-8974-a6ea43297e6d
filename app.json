{"pages": ["pages/index/index", "pages/template/template", "pages/preview/preview", "pages/imageColorPicker/imageColorPicker"], "subpackages": [{"root": "card-package", "name": "card-package", "pages": ["pages/colorPicker/colorPicker", "pages/customColorEditor/customColorEditor", "pages/customImageColorPicker/customImageColorPicker", "pages/quickColorPicker/quickColorPicker"]}, {"root": "basic-tools", "name": "basic-tools", "pages": ["pages/colorPalette/colorPalette", "pages/colorConverter/colorConverter", "pages/colorQuery/colorQuery", "pages/colorDetail/colorDetail", "pages/contrastChecker/contrastChecker"]}, {"root": "advanced-tools", "name": "advanced-tools", "pages": ["pages/gradientGenerator/gradientGenerator", "pages/gradientWallpaper/gradientWallpaper", "pages/colorblindSimulator/colorblindSimulator", "pages/toneGenerator/toneGenerator"]}, {"root": "clothing-package", "name": "clothing-package", "pages": ["pages/clothingColorTool/clothingColorTool", "pages/clothingPreview/clothingPreview", "pages/skinToneTest/skinToneTest", "pages/skinToneReport/skinToneReport"]}, {"root": "ambient-light", "name": "ambient-light", "independent": true, "pages": ["pages/ambientLight/ambientLight", "pages/about/about"]}], "preloadRule": {"pages/index/index": {"network": "all", "packages": ["basic-tools", "clothing-package"]}, "pages/template/template": {"network": "all", "packages": ["card-package"]}, "basic-tools/pages/colorQuery/colorQuery": {"network": "wifi", "packages": ["advanced-tools"]}}, "window": {"navigationBarTextStyle": "black", "navigationBarTitleText": "KALA配色", "navigationBarBackgroundColor": "#ffffff", "backgroundColor": "#f8f8f8", "navigationStyle": "default"}, "style": "v2", "renderer": "webview", "sitemapLocation": "sitemap.json", "lazyCodeLoading": "requiredComponents", "debug": false, "requiredPrivateInfos": [], "enablePassiveEvent": {"touchstart": true, "touchmove": false}}