/* pages/quickColorPicker/quickColorPicker.wxss */
.page-wrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  box-sizing: border-box;
  background-color: transparent;
  padding-bottom: 180rpx;
}

/* 颜色选择区域 */
.colors-section {
  margin-bottom: 10rpx;
  padding-bottom: 0;
}

.colors-title {
  font-size: 24rpx;
  color: #333;
  margin: 12rpx auto 16rpx;
  padding: 16rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f7f7f7;
  max-width: 94%;
  border-left: 2rpx solid #07c160;
  min-height: 60rpx;
}

.title-left {
  display: flex;
  align-items: flex-start;
  flex: 1;
}

.info-dot {
  width: 8rpx;
  height: 8rpx;
  background-color: #07c160;
  border-radius: 50%;
  margin-right: 12rpx;
  margin-top: 8rpx;
  flex-shrink: 0;
}

.title-text {
  flex: 1;
  line-height: 1.4;
  font-size: 24rpx;
  color: #333;
}

.colors-row {
  padding: 0 20rpx;
  margin-bottom: 20rpx;
}

.colors-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  justify-content: flex-start;
  padding: 0;
}

.color-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  transition: all 0.3s ease;
}

.color-item.picking {
  transform: scale(1.05);
}

.color-block {
  width: 100rpx;
  height: 100rpx;
  border-radius: 12rpx;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  border: 3rpx solid transparent;
}

.color-block:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.2);
}

.color-block.selected {
  border-color: #007AFF;
  box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.2);
}

.color-number {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  width: 24rpx;
  height: 24rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  border-radius: 50%;
  font-size: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}

.color-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.color-hex {
  font-size: 22rpx;
  color: #666;
  font-family: monospace;
  cursor: pointer;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  transition: all 0.2s ease;
}

.color-hex:hover {
  background-color: #f0f0f0;
  color: #333;
}

.edit-btn {
  padding: 8rpx 16rpx;
  background-color: #f8f9fa;
  border: 1rpx solid #e9ecef;
  border-radius: 6rpx;
  font-size: 20rpx;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
}

.edit-btn:hover {
  background-color: #e9ecef;
  color: #333;
}

.edit-text {
  font-size: 20rpx;
}

/* 加载状态 */
.colors-loading {
  text-align: center;
  padding: 60rpx 20rpx;
  color: #999;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 没有图片时的占位 */
.no-image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  border: 2rpx dashed #ddd;
  margin: 20rpx;
}

.placeholder-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.placeholder-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
}

/* 图片预览区域 */
.image-section {
  flex: 1;
  margin-bottom: 20rpx;
  padding: 0 20rpx;
}

.image-container {
  position: relative;
  width: 100%;
  min-height: 400rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-image {
  max-width: 100%;
  max-height: 600rpx;
  object-fit: contain;
  display: block;
  transition: transform 0.3s ease;
}

.image-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 放大镜效果 - 参考主题色提取页面 */
.magnifier-container {
  position: absolute;
  pointer-events: none;
  z-index: 1000;
}

.connector-line {
  position: absolute;
  height: 2rpx;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.8), rgba(0, 122, 255, 0.8));
  box-shadow: 0 0 4rpx rgba(0, 0, 0, 0.3);
  pointer-events: none;
  z-index: 999;
}

.target-point {
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  background-color: rgba(0, 122, 255, 0.9);
  border: 2rpx solid white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 8rpx rgba(0, 0, 0, 0.3);
  pointer-events: none;
  z-index: 1001;
}

.magnifier {
  position: absolute;
  width: 160rpx;
  height: 160rpx;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 1002;
}

.magnifier-content {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  overflow: hidden;
  background-color: white;
  border: 4rpx solid white;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.3);
}

.magnifier-canvas {
  width: 100%;
  height: 100%;
  display: block;
  border-radius: 50%;
}

.magnifier-crosshair-h,
.magnifier-crosshair-v {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 0 2rpx rgba(0, 0, 0, 0.5);
  pointer-events: none;
}

.magnifier-crosshair-h {
  width: 40rpx;
  height: 2rpx;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.magnifier-crosshair-v {
  width: 2rpx;
  height: 40rpx;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.color-indicator {
  position: absolute;
  bottom: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 3rpx solid white;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

/* 取色模式下的颜色信息显示 */
.color-info-panel {
  background-color: #f8f9fa;
  padding: 30rpx 20rpx;
  margin: 20rpx;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
}

.color-info-card {
  background-color: white;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.color-preview-block {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 3rpx solid #f0f0f0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.color-values {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  flex: 1;
}

.color-value-item {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.color-value-hex,
.color-value-rgb {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  padding: 8rpx 12rpx;
  background-color: #f8f9fa;
  border-radius: 6rpx;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  font-family: monospace;
}

.color-value-hex:active,
.color-value-rgb:active {
  background-color: #e9ecef;
  transform: scale(0.98);
}

.color-value-label {
  font-size: 20rpx;
  color: #666;
  text-align: center;
  font-weight: 500;
}

.picking-actions {
  display: flex;
  gap: 12rpx;
  margin-top: 16rpx;
}

.picking-cancel-btn,
.picking-confirm-btn {
  flex: 1;
  padding: 16rpx 0;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.picking-cancel-btn {
  background-color: #f5f5f5;
  color: #666;
  border: 1rpx solid #e9ecef;
}

.picking-cancel-btn:active {
  background-color: #e9ecef;
}

.picking-confirm-btn {
  background-color: #007AFF;
  color: white;
  border: 1rpx solid #007AFF;
}

.picking-confirm-btn:active {
  background-color: #0056CC;
}

/* 底部按钮区域 */
.btn-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.next-btn {
  width: 100%;
  padding: 24rpx 0;
  background-color: #007AFF;
  color: white;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.2s ease;
}

.next-btn:active {
  background-color: #0056CC;
  transform: scale(0.98);
}
