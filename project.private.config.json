{"setting": {"compileHotReLoad": true, "preloadBackgroundData": false, "urlCheck": true, "coverView": true, "lazyloadPlaceholderEnable": false, "skylineRenderEnable": false, "autoAudits": false, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "useStaticServer": false, "useLanDebug": false, "showES6CompileOption": false, "checkInvalidKey": true, "ignoreDevUnusedFiles": true, "bigPackageSizeSupport": false}, "libVersion": "3.8.8", "condition": {}, "projectname": "KALA", "description": "项目私有配置文件。此文件中的内容将覆盖 project.config.json 中的相同字段。项目的改动优先同步到此文件中。详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html"}