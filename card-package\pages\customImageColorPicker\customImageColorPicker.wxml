<!--card-package/pages/customImageColorPicker/customImageColorPicker.wxml-->
<!--自定义色卡编辑的独立一键图片取色页面-->
<scroll-view class="page-wrapper" scroll-y="{{pageScrollEnabled}}" enhanced="{{true}}" show-scrollbar="{{false}}" enable-flex="{{true}}">
  <!-- 颜色选择区域 -->
  <view class="colors-section">


    <!-- 操作提示 -->
    <view class="operation-tip">
      <text wx:if="{{selectedColorIndex >= 0}}" class="tip-text selected">已选中颜色{{selectedColorIndex + 1}}，取色后将替换此颜色</text>
      <text wx:elif="{{isDragging}}" class="tip-text dragging">正在拖拽排序中...</text>
      <text wx:else class="tip-text">点击颜色进行编辑取色，长按颜色进行拖拽排序</text>
    </view>

    <view class="colors-row">
      <!-- 颜色加载状态 -->
      <view wx:if="{{colorsLoading}}" class="colors-loading" style="display: flex; justify-content: center; align-items: center; height: 200rpx; flex-direction: column;">
        <view class="loading-spinner" style="width: 60rpx; height: 60rpx; border: 4rpx solid #f3f3f3; border-top: 4rpx solid #3498db; border-radius: 50%; animation: spin 1s linear infinite; margin-bottom: 20rpx;"></view>
        <view style="color: #666; font-size: 28rpx;">正在提取颜色...</view>
      </view>

      <!-- 颜色块显示容器 -->
      <view wx:elif="{{colors && colors.length > 0}}" class="colors-container {{colorsTransitioning ? 'transitioning' : ''}}">
        <!-- 颜色块显示 - 直接在colors-row中水平排列 -->
        <view
          wx:for="{{colors}}"
          wx:key="index"
          class="color-item {{dragIndex === index ? 'dragging' : ''}} {{colorsTransitioning ? 'color-transitioning' : ''}}"
          data-index="{{index}}"
          bindtouchstart="handleColorTouchStart"
          bindtouchmove="handleColorTouchMove"
          bindtouchend="handleColorTouchEnd"
          bindtouchcancel="handleColorTouchCancel"
          style="{{dragIndex === index ? dragStyle : ''}}"
        >
          <!-- 颜色块 -->
          <view
            class="color-block {{selectedColorIndex === index ? 'selected' : ''}}"
            style="background-color: {{item.color}};"
            bindtap="selectColor"
            data-index="{{index}}"
          >
            <view class="color-number">{{item.displayIndex}}</view>
          </view>

          <!-- 颜色信息 - 始终显示 -->
          <view class="color-info">
            <view class="color-hex" bindtap="copyColorCode" data-color="{{item.color}}">{{item.color}}</view>
            <!-- 复选框 -->
            <view class="color-checkbox" bindtap="toggleColorSelection" data-index="{{index}}">
              <view class="checkbox {{item.selected ? 'checked' : ''}}">
                <view class="checkmark" wx:if="{{item.selected}}">✓</view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 颜色提取失败状态 -->
      <view wx:else class="colors-error" style="text-align: center; padding: 40rpx; color: #999;">
        <view style="font-size: 32rpx; margin-bottom: 20rpx;">颜色提取中...</view>
        <view style="font-size: 24rpx;">请稍候</view>
      </view>
    </view>
  </view>

  <!-- 图片预览区域 -->
  <view class="image-section">
    <view class="image-container"
      bindtouchstart="handleContainerTouchStart"
      bindtouchmove="handleContainerTouchMove"
      bindtouchend="handleContainerTouchEnd"
      bindtouchcancel="handleContainerTouchCancel"
      catchtouchmove="handleContainerTouchMove">
      <image
        class="preview-image"
        src="{{imagePath}}"
        mode="aspectFit"
        binderror="handleImageError"
        bindload="handleImageLoad"
        style="transform: scale({{scale}}) translate({{translateX}}px, {{translateY}}px); transition: transform 0.2s ease;"
      ></image>
      <view class="image-loading" wx:if="{{imageLoading}}">
        <view class="loading-spinner"></view>
      </view>
      <!-- 平移提示 -->
      <view class="panning-tip" wx:if="{{showPanningTip}}">
        <text>进入平移模式，可拖动图片</text>
      </view>

      <!-- 放大镜效果 -->
      <view class="magnifier-container" wx:if="{{magnifierVisible}}">
        <!-- 目标点 -->
        <view class="target-point" style="left: {{targetX}}px; top: {{targetY}}px;"></view>

        <!-- 放大镜 - 使用图片裁剪实现放大效果 -->
        <view class="magnifier" style="left: {{magnifierX}}px; top: {{magnifierY}}px;">
          <view class="magnifier-content">
            <!-- 使用同一张图片，但放大并裁剪到特定位置 -->
            <image
              class="magnifier-image"
              src="{{imagePath}}"
              mode="scaleToFill"
              style="width: {{magnifiedImageWidth}}px; height: {{magnifiedImageHeight}}px; transform: translate(-{{magnifierOffsetX}}px, -{{magnifierOffsetY}}px);"
            ></image>
            <!-- 十字线 -->
            <view class="magnifier-crosshair-h"></view>
            <view class="magnifier-crosshair-v"></view>
            <!-- 当前颜色指示器 -->
            <view class="color-indicator" style="background-color: {{currentColor || '#ffffff'}};"></view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <view class="btn-container">
    <button class="next-btn" bindtap="confirmSelection">
      <text class="btn-main-text">确认选择</text>
      <text class="btn-sub-text">（已选{{selectedCount || 0}}个颜色）</text>
    </button>
  </view>

  <!-- 隐藏的canvas用于分析图片颜色 -->
  <canvas type="2d" id="colorAnalysisCanvas" style="width: 200px; height: 200px; position: absolute; left: -9999px;"></canvas>

  <!-- 隐藏的canvas用于颜色提取 -->
  <canvas type="2d" id="tempCanvas" style="width: 200px; height: 200px; position: absolute; left: -9999px;"></canvas>


</scroll-view>
