// 幸运签错误处理工具
// 专门处理幸运签组件可能出现的各种错误
const logUtils = require('./logUtils');

/**
 * 检查是否是微信内部错误
 * @param {Error|string} error - 错误对象或错误消息
 * @returns {boolean} 是否是微信内部错误
 */
function isWeChatInternalError(error) {
  const errorMsg = typeof error === 'string' ? error : (error.message || '');

  const internalErrorPatterns = [
    '_getData',
    'this._getData is not a function',
    '_getDataFromDataSet',
    'getData is not a function',
    'webviewScriptError',
    'SystemError',
    'too early',
    'too eayly',
    'createSelectorQuery',
    'updateTextView',
    'removeTextView',
    'removeBaseView',
    'removeView',
    'reportKeyValue',
    'fail no root view',
    'createTextView',
    'createBaseView',
    'jsbridge',
    'WAServiceMainContext'
  ];

  return internalErrorPatterns.some(pattern => errorMsg.includes(pattern));
}

/**
 * 安全的组件方法调用包装器
 * @param {Object} component - 组件实例
 * @param {Function} method - 要调用的方法
 * @param {Array} args - 方法参数
 * @param {string} methodName - 方法名称（用于日志）
 */
function safeComponentCall(component, method, args = [], methodName = 'unknown') {
  try {
    // 检查组件是否已销毁
    if (component._isDestroyed) {
      logUtils.warn(`组件已销毁，跳过方法调用: ${methodName}`);
      return null;
    }

    // 检查方法是否存在
    if (typeof method !== 'function') {
      logUtils.error(`方法不存在或不是函数: ${methodName}`);
      return null;
    }

    // 安全调用方法
    return method.apply(component, args);
  } catch (error) {
    logUtils.error(`方法调用失败: ${methodName}`, error);

    // 检查是否是微信内部错误
    if (isWeChatInternalError(error)) {
      logUtils.warn('检测到微信小程序内部错误，静默处理');
    }

    return null;
  }
}

/**
 * 安全的Canvas操作包装器
 * @param {Function} canvasOperation - Canvas操作函数
 * @param {string} operationName - 操作名称
 * @param {number} maxRetries - 最大重试次数
 * @param {number} retryDelay - 重试延迟（毫秒）
 */
async function safeCanvasOperation(canvasOperation, operationName = 'Canvas操作', maxRetries = 2, retryDelay = 300) {
  let lastError = null;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      if (attempt > 0) {
        logUtils.log(`${operationName} 第${attempt}次重试`);
        // 等待一段时间再重试
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      } else {
        logUtils.log(`开始执行: ${operationName}`);
      }

      const result = await canvasOperation();
      logUtils.log(`${operationName} 执行成功`);
      return result;
    } catch (error) {
      lastError = error;
      logUtils.error(`${operationName} 执行失败 (尝试 ${attempt + 1}/${maxRetries + 1}):`, error);

      // 如果是微信内部错误，不进行重试
      if (isWeChatInternalError(error)) {
        logUtils.warn('检测到微信内部错误，停止重试');
        break;
      }

      // 如果是最后一次尝试，不再重试
      if (attempt === maxRetries) {
        break;
      }
    }
  }

  logUtils.error(`${operationName} 最终失败:`, lastError);
  return null;
}

/**
 * 安全的setData操作
 * @param {Object} component - 组件实例
 * @param {Object} data - 要设置的数据
 * @param {Function} callback - 回调函数
 */
function safeSetData(component, data, callback) {
  if (!component || component._isDestroyed) {
    logUtils.warn('组件已销毁或无效，跳过setData');
    return;
  }

  try {
    component.setData(data, callback);
  } catch (error) {
    logUtils.error('setData失败:', error);

    if (isWeChatInternalError(error)) {
      logUtils.warn('检测到微信内部错误，延迟重试setData');
      setTimeout(() => {
        if (!component._isDestroyed) {
          try {
            component.setData(data, callback);
          } catch (retryError) {
            logUtils.error('setData重试仍然失败:', retryError);
          }
        }
      }, 100);
    }
  }
}



/**
 * 清理组件资源
 * @param {Object} component - 组件实例
 */
function cleanupComponent(component) {
  try {
    // 标记组件为已销毁
    component._isDestroyed = true;
    component._isReady = false;

    // 清理定时器
    const timerNames = [
      '_animationTimer',
      '_enterAnimationTimer', 
      '_buttonTimer',
      '_canvasTimer',
      '_exitTimer',
      '_flipTimer'
    ];
    
    timerNames.forEach(timerName => {
      if (component[timerName]) {
        clearTimeout(component[timerName]);
        component[timerName] = null;
      }
    });

    // 清理Canvas相关资源
    if (component._canvasContext) {
      component._canvasContext = null;
    }

    console.log('组件资源清理完成');
  } catch (error) {
    console.error('清理组件资源失败:', error);
  }
}

/**
 * 错误恢复策略
 * @param {Error} error - 错误对象
 * @param {Object} context - 上下文信息
 */
function handleError(error, context = {}) {
  const errorMessage = error.message || error.toString();
  
  console.error('幸运签组件错误:', {
    message: errorMessage,
    context: context,
    stack: error.stack
  });

  // 根据错误类型采取不同的恢复策略
  if (errorMessage.includes('_getData')) {
    console.warn('_getData错误恢复策略: 忽略并继续执行');
    return 'ignore';
  } else if (errorMessage.includes('Canvas')) {
    console.warn('Canvas错误恢复策略: 重试或降级');
    return 'retry';
  } else if (errorMessage.includes('setData')) {
    console.warn('setData错误恢复策略: 延迟重试');
    return 'delay_retry';
  } else {
    console.warn('未知错误恢复策略: 记录并继续');
    return 'log_continue';
  }
}

module.exports = {
  isWeChatInternalError,
  safeComponentCall,
  safeCanvasOperation,
  safeSetData,
  cleanupComponent,
  handleError
};
