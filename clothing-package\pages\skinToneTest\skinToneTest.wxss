/* pages/skinToneTest/skinToneTest.wxss */
.page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.container {
  flex: 1;
  padding: 30rpx 24rpx;
  padding-bottom: 180rpx; /* 增加底部内边距，确保内容不被底部按钮遮挡 */
  overflow-y: auto;
  box-sizing: border-box;
}

/* 通用区块样式 */
.section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.06);
  box-sizing: border-box;
  width: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #191919;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 28rpx;
  background-color: #07c160;
  border-radius: 3rpx;
}

.section-subtitle {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
  line-height: 1.5;
}

/* 颜色预览区域 */
.color-preview-container {
  display: flex;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
  padding: 4rpx 0 0;
  flex-wrap: nowrap;
  justify-content: space-between;
  gap: 0;
  overflow: hidden;
}

.color-preview {
  width: 63%;
  height: 134rpx;
  border-radius: 10rpx;
  margin-right: 0;
  border: none;
  flex: 0.63;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 0;
  margin-top: -2rpx;
  box-sizing: border-box;
  cursor: pointer;
}

.color-preview:active {
  transform: scale(0.98);
  opacity: 0.95;
}

.color-hex-value {
  font-size: 28rpx;
  font-family: 'Courier New', monospace;
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  text-align: center;
  max-width: 90%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  letter-spacing: 0.5rpx;
  font-weight: 500;
  background-color: rgba(0, 0, 0, 0.15);
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

/* 操作按钮 */
.color-actions {
  width: 38%;
  flex: 0.38;
  display: flex;
  flex-direction: column;
  gap: 14rpx;
  min-width: 180rpx;
  max-width: 220rpx;
  margin-left: 0;
}

.action-btn {
  width: 100%;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  font-size: 22rpx;
  padding: 0 4rpx;
  background-color: #f5f5f5;
  color: #333333;
  border-radius: 6rpx;
  border: none;
  line-height: 60rpx;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
  font-weight: 500;
  position: relative;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  letter-spacing: -0.8rpx;
  text-align: center;
  margin: 0;
  box-sizing: border-box;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.action-btn.primary {
  background-color: #07c160;
  color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.3);
}

.action-icon {
  font-size: 20rpx;
}

.action-text {
  font-size: 22rpx;
}

/* 多肤色容器 */
.multi-color-container {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.color-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.color-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 16rpx;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  border: 1rpx solid #f0f0f0;
}

.color-preview-small {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 250rpx;
  height: 90rpx;
  border-radius: 14rpx;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
  flex-shrink: 0;
}

.color-preview-small:active {
  transform: scale(0.96);
}

.color-hex-small {
  font-size: 22rpx;
  font-weight: 600;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.color-actions-small {
  display: flex;
  gap: 12rpx;
  flex: 1;
}

.action-btn-small {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12rpx 16rpx;
  border-radius: 8rpx;
  background-color: #ffffff;
  border: 1rpx solid #e0e0e0;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80rpx;
}

.action-btn-small.delete {
  background-color: #fff5f5;
  border-color: #fed7d7;
  color: #e53e3e;
}

.action-btn-small:active {
  transform: scale(0.94);
  opacity: 0.8;
}

.action-text-small {
  font-size: 20rpx;
  font-weight: 500;
}

/* 添加肤色按钮 */
.add-color-container {
  display: flex;
  justify-content: center;
}

.add-color-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  background-color: #ffffff;
  color: #333333;
  border: 2rpx dashed #d0d0d0;
  border-radius: 10rpx;
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-color-btn:hover {
  border-color: #999999;
  background-color: #fafafa;
}

.add-color-btn:active {
  transform: scale(0.98);
  border-color: #666666;
  background-color: #f0f0f0;
}

.add-color-icon {
  font-size: 24rpx;
  font-weight: 400;
  color: #666666;
}

.add-color-text {
  font-size: 22rpx;
  font-weight: 500;
  color: #666666;
}



/* 肤色色卡网格 */
.skin-tone-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12rpx;
  margin-top: 20rpx;
}

.skin-tone-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 18rpx 12rpx;
  border-radius: 12rpx;
  background-color: #f9f9f9;
  border: 1rpx solid #f0f0f0;
}

.skin-tone-color {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-bottom: 12rpx;
  box-shadow: 0 3rpx 10rpx rgba(0, 0, 0, 0.15);
  border: 2rpx solid rgba(255, 255, 255, 0.9);
}

.skin-tone-name {
  font-size: 18rpx;
  text-align: center;
  line-height: 1.2;
  font-weight: 500;
  color: #666;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 固定底部按钮容器 */
.btn-container {
  padding: 20rpx 30rpx 50rpx; /* 增加底部内边距，确保白色背景延伸到屏幕底部 */
  width: 100%;
  box-sizing: border-box;
  position: fixed;
  bottom: 0; /* 改回0，让容器紧贴屏幕底部 */
  left: 0;
  background-color: white;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 生成报告按钮 */
.generate-btn {
  background-color: #07c160;
  color: white;
  border-radius: 8rpx; /* 统一圆角 */
  font-size: 30rpx; /* 统一字体大小 */
  padding: 18rpx 0; /* 统一内边距 */
  width: 100%;
  box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.15); /* 统一阴影 */
  transition: all 0.2s ease;
  font-weight: 500;
  border: none;
}

.generate-btn:active {
  opacity: 0.9;
  transform: scale(0.98);
}

/* 色卡编辑相关样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.section-actions {
  display: flex;
  gap: 16rpx;
}

.reset-btn {
  padding: 8rpx 16rpx;
  background-color: #f5f5f5;
  color: #666;
  font-size: 24rpx;
  border-radius: 6rpx;
  border: 1rpx solid #e0e0e0;
  transition: all 0.2s;
}

.reset-btn:active {
  background-color: #e8e8e8;
}

.skin-tone-item.editable {
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
}

.skin-tone-item.editable:active {
  transform: scale(0.95);
  opacity: 0.8;
}

.edit-indicator {
  position: absolute;
  top: 4rpx;
  right: 4rpx;
  width: 24rpx;
  height: 24rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 16rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s;
}

.skin-tone-item.editable:hover .edit-indicator,
.skin-tone-item.editable:active .edit-indicator {
  opacity: 1;
}

/* 色卡编辑弹窗样式 */
.card-edit-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.3s ease;
}

.card-edit-container {
  width: 90%;
  max-width: 650rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  animation: scaleIn 0.3s ease;
}

.card-edit-header {
  padding: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #e0e0e0;
  background-color: #f8f8f8;
}

.card-edit-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  text-align: center;
}

.card-edit-close {
  font-size: 36rpx;
  color: #999;
  line-height: 1;
  padding: 10rpx;
  margin: -10rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.card-edit-close:active {
  opacity: 0.7;
}

.card-edit-content {
  padding: 30rpx;
  background-color: #ffffff;
}

.edit-field {
  margin-bottom: 24rpx;
  position: relative;
}

.edit-field:last-child {
  margin-bottom: 0;
}

.edit-label {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.edit-input {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #f9f9f9;
  box-sizing: border-box;
  transition: all 0.2s;
}

.edit-input:focus {
  border-color: #07c160;
  background-color: #ffffff;
}

/* 颜色输入行容器 */
.color-input-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

/* 颜色输入容器 */
.color-input-container {
  position: relative;
  flex: 1;
  display: flex;
  align-items: center;
}

.color-input {
  width: 100%;
  padding-right: 200rpx; /* 为图片取色按钮和颜色预览留出空间 */
  font-family: 'Courier New', monospace;
}

/* 图片取色按钮 */
.image-pick-btn {
  position: absolute;
  right: 84rpx; /* 位于色块左侧 */
  top: 50%;
  transform: translateY(-50%);
  padding: 0 16rpx;
  height: 60rpx;
  background-color: #07c160;
  color: white;
  border-radius: 6rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  white-space: nowrap;
  min-width: 100rpx;
}

.image-pick-btn:active {
  background-color: #06ad56;
  transform: translateY(-50%) scale(0.95);
}

.color-preview {
  position: absolute;
  right: 12rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 60rpx;
  height: 60rpx;
  border-radius: 6rpx;
  border: 2rpx solid #e0e0e0;
  background-color: #f0f0f0;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.color-preview:hover {
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
}

/* 外部粘贴按钮 */
.paste-btn-external {
  padding: 0 24rpx;
  height: 88rpx;
  background-color: #f8f8f8;
  color: #666;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  cursor: pointer;
  flex-shrink: 0;
  min-width: 100rpx;
  user-select: none;
  white-space: nowrap;
}

.paste-btn-external:active {
  background-color: #e8e8e8;
  transform: scale(0.95);
}

/* 颜色输入提示文字 */
.color-input-tip {
  font-size: 20rpx;
  color: #999;
  margin-top: 8rpx;
  line-height: 1.4;
}

.card-edit-footer {
  padding: 24rpx;
  display: flex;
  justify-content: space-between;
  background-color: #f8f8f8;
  border-top: 1rpx solid #e0e0e0;
}

.card-edit-cancel, .card-edit-confirm {
  flex: 1;
  padding: 16rpx 0;
  font-size: 30rpx;
  border-radius: 8rpx;
  margin: 0 10rpx;
  text-align: center;
  transition: all 0.2s;
  line-height: 1.4;
}

.card-edit-cancel {
  background-color: #f5f5f5;
  color: #666;
  border: 1rpx solid #e0e0e0;
}

.card-edit-cancel:active {
  background-color: #e8e8e8;
}

.card-edit-confirm {
  background-color: #07c160;
  color: #fff;
  border: 1rpx solid #07c160;
  box-shadow: 0 2rpx 6rpx rgba(7, 193, 96, 0.3);
}

.card-edit-confirm:active {
  background-color: #06a552;
}

/* 使用说明 */
.instruction-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.instruction-item {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  padding: 16rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
}

.instruction-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
  background-color: #07c160;
  color: white;
  border-radius: 50%;
  font-size: 20rpx;
  font-weight: 600;
  flex-shrink: 0;
}

.instruction-text {
  font-size: 24rpx;
  color: #333;
  line-height: 1.5;
  flex: 1;
}

/* 绿色高亮文字样式 */
.highlight-green {
  color: #07c160;
  font-weight: 600;
}

/* 颜色选择器弹窗 */
.color-picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.color-picker-container {
  width: 90%;
  max-width: 650rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from { transform: translateY(50rpx); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.color-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 24rpx;
  border-bottom: 1rpx solid #eeeeee;
}

.color-picker-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
}

.color-picker-close {
  font-size: 40rpx;
  color: #999999;
  line-height: 1;
  padding: 0 10rpx;
  cursor: pointer;
}


