// pages/colorConverter/colorConverter.js
const { safeSetData, createDebouncedSafeSetData } = require('../../../utils/safeSetData');

Page({
  data: {
    currentColor: '#07C160', // 默认颜色
    textColor: '#FFFFFF', // 默认文字颜色（白色）
    hexValue: '#07C160',
    rgbValue: '7, 193, 96',
    hslValue: '146, 93%, 39%',
    hsvValue: '146, 96%, 76%',
    cmykValue: '96%, 0%, 50%, 24%',
    labValue: '69, -67, 39',
    showColorPicker: false, // 是否显示颜色选择器
  },

  onLoad: function (options) {
    // 初始化防抖版本的 setData
    this.debouncedSetData = createDebouncedSafeSetData(this, 100);

    // 如果有传入的颜色参数，则使用传入的颜色
    if (options.color) {
      const color = options.color.startsWith('#') ? options.color : '#' + options.color;
      this.updateColor(color);
    } else {
      // 计算默认颜色的文字颜色
      const textColor = this.getTextColorForBackground(this.data.currentColor);
      safeSetData(this, {
        textColor: textColor
      });
    }

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '颜色转换器'
    });
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {
    // 清理可能存在的定时器
    if (this.inputTimer) {
      clearTimeout(this.inputTimer);
      this.inputTimer = null;
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {
    // 清理可能存在的定时器
    if (this.inputTimer) {
      clearTimeout(this.inputTimer);
      this.inputTimer = null;
    }
  },

  // 显示颜色选择器
  showColorPicker: function() {
    this.setData({
      showColorPicker: true
    });
  },

  // 隐藏颜色选择器
  hideColorPicker: function() {
    this.setData({
      showColorPicker: false
    });
  },

  // 颜色选择器变化
  onColorPickerChange: function(e) {
    const color = e.detail.color;
    this.updateColor(color);
  },

  // 颜色选择器确认
  onColorPickerConfirm: function(e) {
    const color = e.detail.color;
    this.updateColor(color);
    this.setData({
      showColorPicker: false
    });

    // 显示轻提示
    wx.showToast({
      title: '颜色已更新',
      icon: 'success',
      duration: 1000
    });
  },

  // 随机生成颜色
  randomColor: function() {
    // 生成更美观的随机颜色 - 使用HSL模型
    const h = Math.floor(Math.random() * 360); // 随机色相
    const s = Math.floor(Math.random() * 40) + 60; // 60-100的饱和度
    const l = Math.floor(Math.random() * 30) + 35; // 35-65的亮度

    // 转换为HEX
    const color = this.hslToHex(h, s, l);
    this.updateColor(color);
  },

  // HSL转HEX
  hslToHex: function(h, s, l) {
    h /= 360;
    s /= 100;
    l /= 100;

    let r, g, b;

    if (s === 0) {
      // 如果饱和度为0，则为灰色
      r = g = b = l;
    } else {
      const hue2rgb = (p, q, t) => {
        if (t < 0) t += 1;
        if (t > 1) t -= 1;
        if (t < 1/6) return p + (q - p) * 6 * t;
        if (t < 1/2) return q;
        if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
        return p;
      };

      const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
      const p = 2 * l - q;

      r = hue2rgb(p, q, h + 1/3);
      g = hue2rgb(p, q, h);
      b = hue2rgb(p, q, h - 1/3);
    }

    // 转换为HEX
    const toHex = x => {
      const hex = Math.round(x * 255).toString(16);
      return hex.length === 1 ? '0' + hex : hex;
    };

    return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
  },

  // 复制颜色值
  copyColorValue: function(e) {
    const value = e.currentTarget.dataset.value;

    wx.setClipboardData({
      data: value,
      success: function() {
        wx.showToast({
          title: '颜色已复制',
          icon: 'success',
          duration: 1500
        });
      }
    });
  },

  // 更新颜色和所有颜色值
  updateColor: function(color) {
    // 确保颜色是有效的HEX格式
    if (!color.startsWith('#')) {
      color = '#' + color;
    }

    // 如果是3位HEX，转换为6位
    if (color.length === 4) {
      color = '#' + color[1] + color[1] + color[2] + color[2] + color[3] + color[3];
    }

    // 验证HEX格式
    if (!/^#[0-9A-F]{6}$/i.test(color)) {
      wx.showToast({
        title: '无效的颜色值',
        icon: 'none',
        duration: 1500
      });
      return;
    }

    // 解析RGB值
    const r = parseInt(color.substring(1, 3), 16);
    const g = parseInt(color.substring(3, 5), 16);
    const b = parseInt(color.substring(5, 7), 16);

    // 计算各种颜色格式值
    const hsl = this.rgbToHsl(r, g, b);
    const hsv = this.rgbToHsv(r, g, b);
    const cmyk = this.rgbToCmyk(r, g, b);
    const lab = this.rgbToLab(r, g, b);

    // 计算文字颜色
    const textColor = this.getTextColorForBackground(color);

    // 更新所有颜色值
    this.setData({
      currentColor: color.toUpperCase(),
      textColor: textColor,
      hexValue: color.toUpperCase(),
      rgbValue: r + ', ' + g + ', ' + b,
      hslValue: hsl.h + ', ' + hsl.s + '%, ' + hsl.l + '%',
      hsvValue: hsv.h + ', ' + hsv.s + '%, ' + hsv.v + '%',
      cmykValue: cmyk.c + '%, ' + cmyk.m + '%, ' + cmyk.y + '%, ' + cmyk.k + '%',
      labValue: lab.l + ', ' + lab.a + ', ' + lab.b
    });
  },

  // HEX输入变化
  onHexInput: function(e) {
    let value = e.detail.value.toUpperCase();

    // 移除#号
    if (value.startsWith('#')) {
      value = value.substring(1);
    }

    // 验证HEX格式
    if (/^[0-9A-F]{6}$/i.test(value)) {
      // 使用setTimeout避免频繁更新
      if (this.hexInputTimer) {
        clearTimeout(this.hexInputTimer);
      }
      this.hexInputTimer = setTimeout(() => {
        this.updateColor('#' + value);
      }, 300);
    }
  },

  // RGB输入变化
  onRgbInput: function(e) {
    const channel = e.currentTarget.dataset.channel;
    let value = parseInt(e.detail.value);

    // 验证RGB值范围
    if (isNaN(value)) {
      value = 0;
    } else if (value < 0) {
      value = 0;
    } else if (value > 255) {
      value = 255;
    }

    // 更新RGB值
    const rgb = {
      r: this.data.colorValues.rgb.r,
      g: this.data.colorValues.rgb.g,
      b: this.data.colorValues.rgb.b
    };
    rgb[channel] = value;

    // 转换为HEX
    const hex = this.rgbToHex(rgb.r, rgb.g, rgb.b);

    // 使用防抖避免频繁更新
    if (this.rgbInputTimer) {
      clearTimeout(this.rgbInputTimer);
    }
    this.rgbInputTimer = setTimeout(() => {
      this.updateColor(hex);
    }, 300);
  },

  // HSL输入变化
  onHslInput: function(e) {
    const channel = e.currentTarget.dataset.channel;
    let value = parseInt(e.detail.value);

    // 验证HSL值范围
    if (isNaN(value)) {
      value = 0;
    } else if (value < 0) {
      value = 0;
    } else if (channel === 'h' && value > 360) {
      value = 360;
    } else if ((channel === 's' || channel === 'l') && value > 100) {
      value = 100;
    }

    // 更新HSL值
    const hsl = {
      h: this.data.colorValues.hsl.h,
      s: this.data.colorValues.hsl.s,
      l: this.data.colorValues.hsl.l
    };
    hsl[channel] = value;

    // 转换为RGB
    const rgb = this.hslToRgb(hsl.h, hsl.s, hsl.l);

    // 转换为HEX
    const hex = this.rgbToHex(rgb.r, rgb.g, rgb.b);

    // 更新颜色
    this.updateColor(hex);
  },

  // HSV输入变化
  onHsvInput: function(e) {
    const channel = e.currentTarget.dataset.channel;
    let value = parseInt(e.detail.value);

    // 验证HSV值范围
    if (isNaN(value)) {
      value = 0;
    } else if (value < 0) {
      value = 0;
    } else if (channel === 'h' && value > 360) {
      value = 360;
    } else if ((channel === 's' || channel === 'v') && value > 100) {
      value = 100;
    }

    // 更新HSV值
    const hsv = {
      h: this.data.colorValues.hsv.h,
      s: this.data.colorValues.hsv.s,
      v: this.data.colorValues.hsv.v
    };
    hsv[channel] = value;

    // 转换为RGB
    const rgb = this.hsvToRgb(hsv.h, hsv.s, hsv.v);

    // 转换为HEX
    const hex = this.rgbToHex(rgb.r, rgb.g, rgb.b);

    // 更新颜色
    this.updateColor(hex);
  },

  // CMYK输入变化
  onCmykInput: function(e) {
    const channel = e.currentTarget.dataset.channel;
    let value = parseInt(e.detail.value);

    // 验证CMYK值范围
    if (isNaN(value)) {
      value = 0;
    } else if (value < 0) {
      value = 0;
    } else if (value > 100) {
      value = 100;
    }

    // 更新CMYK值
    const cmyk = {
      c: this.data.colorValues.cmyk.c,
      m: this.data.colorValues.cmyk.m,
      y: this.data.colorValues.cmyk.y,
      k: this.data.colorValues.cmyk.k
    };
    cmyk[channel] = value;

    // 转换为RGB
    const rgb = this.cmykToRgb(cmyk.c, cmyk.m, cmyk.y, cmyk.k);

    // 转换为HEX
    const hex = this.rgbToHex(rgb.r, rgb.g, rgb.b);

    // 更新颜色
    this.updateColor(hex);
  },

  // LAB输入变化
  onLabInput: function(e) {
    const channel = e.currentTarget.dataset.channel;
    let value = parseFloat(e.detail.value);

    // 验证LAB值范围
    if (isNaN(value)) {
      value = 0;
    } else if (channel === 'l') {
      if (value < 0) value = 0;
      if (value > 100) value = 100;
    } else if (channel === 'a' || channel === 'b') {
      if (value < -128) value = -128;
      if (value > 127) value = 127;
    }

    // 更新LAB值
    const lab = {
      l: this.data.colorValues.lab.l,
      a: this.data.colorValues.lab.a,
      b: this.data.colorValues.lab.b
    };
    lab[channel] = value;

    // 转换为RGB
    const rgb = this.labToRgb(lab.l, lab.a, lab.b);

    // 转换为HEX
    const hex = this.rgbToHex(rgb.r, rgb.g, rgb.b);

    // 更新颜色
    this.updateColor(hex);
  },

  // RGB转HEX
  rgbToHex: function(r, g, b) {
    const toHex = function(x) {
      const hex = Math.round(x).toString(16);
      return hex.length === 1 ? '0' + hex : hex;
    };

    return '#' + toHex(r) + toHex(g) + toHex(b);
  },

  // RGB转HSL
  rgbToHsl: function(r, g, b) {
    r /= 255;
    g /= 255;
    b /= 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h, s, l = (max + min) / 2;

    if (max === min) {
      h = s = 0; // 灰色
    } else {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
        case g: h = (b - r) / d + 2; break;
        case b: h = (r - g) / d + 4; break;
      }

      h /= 6;
    }

    return {
      h: Math.round(h * 360),
      s: Math.round(s * 100),
      l: Math.round(l * 100)
    };
  },

  // HSL转RGB
  hslToRgb: function(h, s, l) {
    h /= 360;
    s /= 100;
    l /= 100;

    let r, g, b;

    if (s === 0) {
      r = g = b = l; // 灰色
    } else {
      const hue2rgb = (p, q, t) => {
        if (t < 0) t += 1;
        if (t > 1) t -= 1;
        if (t < 1/6) return p + (q - p) * 6 * t;
        if (t < 1/2) return q;
        if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
        return p;
      };

      const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
      const p = 2 * l - q;

      r = hue2rgb(p, q, h + 1/3);
      g = hue2rgb(p, q, h);
      b = hue2rgb(p, q, h - 1/3);
    }

    return {
      r: Math.round(r * 255),
      g: Math.round(g * 255),
      b: Math.round(b * 255)
    };
  },

  // RGB转HSV
  rgbToHsv: function(r, g, b) {
    r /= 255;
    g /= 255;
    b /= 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h, s, v = max;

    const d = max - min;
    s = max === 0 ? 0 : d / max;

    if (max === min) {
      h = 0; // 灰色
    } else {
      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
        case g: h = (b - r) / d + 2; break;
        case b: h = (r - g) / d + 4; break;
      }
      h /= 6;
    }

    return {
      h: Math.round(h * 360),
      s: Math.round(s * 100),
      v: Math.round(v * 100)
    };
  },

  // HSV转RGB
  hsvToRgb: function(h, s, v) {
    h /= 360;
    s /= 100;
    v /= 100;

    let r, g, b;

    const i = Math.floor(h * 6);
    const f = h * 6 - i;
    const p = v * (1 - s);
    const q = v * (1 - f * s);
    const t = v * (1 - (1 - f) * s);

    switch (i % 6) {
      case 0: r = v, g = t, b = p; break;
      case 1: r = q, g = v, b = p; break;
      case 2: r = p, g = v, b = t; break;
      case 3: r = p, g = q, b = v; break;
      case 4: r = t, g = p, b = v; break;
      case 5: r = v, g = p, b = q; break;
    }

    return {
      r: Math.round(r * 255),
      g: Math.round(g * 255),
      b: Math.round(b * 255)
    };
  },

  // RGB转CMYK
  rgbToCmyk: function(r, g, b) {
    r /= 255;
    g /= 255;
    b /= 255;

    const k = 1 - Math.max(r, g, b);

    // 避免除以零
    if (k === 1) {
      return { c: 0, m: 0, y: 0, k: 100 };
    }

    const c = (1 - r - k) / (1 - k);
    const m = (1 - g - k) / (1 - k);
    const y = (1 - b - k) / (1 - k);

    return {
      c: Math.round(c * 100),
      m: Math.round(m * 100),
      y: Math.round(y * 100),
      k: Math.round(k * 100)
    };
  },

  // CMYK转RGB
  cmykToRgb: function(c, m, y, k) {
    c /= 100;
    m /= 100;
    y /= 100;
    k /= 100;

    const r = 255 * (1 - c) * (1 - k);
    const g = 255 * (1 - m) * (1 - k);
    const b = 255 * (1 - y) * (1 - k);

    return {
      r: Math.round(r),
      g: Math.round(g),
      b: Math.round(b)
    };
  },

  // RGB转LAB
  rgbToLab: function(r, g, b) {
    // 转换为XYZ
    r /= 255;
    g /= 255;
    b /= 255;

    // 应用gamma校正
    r = r > 0.04045 ? Math.pow((r + 0.055) / 1.055, 2.4) : r / 12.92;
    g = g > 0.04045 ? Math.pow((g + 0.055) / 1.055, 2.4) : g / 12.92;
    b = b > 0.04045 ? Math.pow((b + 0.055) / 1.055, 2.4) : b / 12.92;

    // 转换为XYZ
    const x = (r * 0.4124 + g * 0.3576 + b * 0.1805) * 100;
    const y = (r * 0.2126 + g * 0.7152 + b * 0.0722) * 100;
    const z = (r * 0.0193 + g * 0.1192 + b * 0.9505) * 100;

    // 转换为LAB
    const xn = 95.047;
    const yn = 100.000;
    const zn = 108.883;

    const fx = x / xn > 0.008856 ? Math.pow(x / xn, 1/3) : (7.787 * x / xn) + 16/116;
    const fy = y / yn > 0.008856 ? Math.pow(y / yn, 1/3) : (7.787 * y / yn) + 16/116;
    const fz = z / zn > 0.008856 ? Math.pow(z / zn, 1/3) : (7.787 * z / zn) + 16/116;

    const l = (116 * fy) - 16;
    const a = 500 * (fx - fy);
    const b_val = 200 * (fy - fz);

    return {
      l: Math.round(l * 10) / 10,
      a: Math.round(a * 10) / 10,
      b: Math.round(b_val * 10) / 10
    };
  },

  // LAB转RGB
  labToRgb: function(l, a, b) {
    // 转换为XYZ
    const y = (l + 16) / 116;
    const x = a / 500 + y;
    const z = y - b / 200;

    const xn = 95.047;
    const yn = 100.000;
    const zn = 108.883;

    const x3 = Math.pow(x, 3);
    const y3 = Math.pow(y, 3);
    const z3 = Math.pow(z, 3);

    const xr = x3 > 0.008856 ? x3 : (x - 16/116) / 7.787;
    const yr = y3 > 0.008856 ? y3 : (y - 16/116) / 7.787;
    const zr = z3 > 0.008856 ? z3 : (z - 16/116) / 7.787;

    const X = xr * xn;
    const Y = yr * yn;
    const Z = zr * zn;

    // 转换为RGB
    let r = X * 0.032406 - Y * 0.015372 - Z * 0.004986;
    let g = -X * 0.009689 + Y * 0.018758 + Z * 0.000415;
    let b_val = X * 0.000557 - Y * 0.002040 + Z * 0.010570;

    // 应用gamma校正
    r = r > 0.0031308 ? 1.055 * Math.pow(r, 1/2.4) - 0.055 : 12.92 * r;
    g = g > 0.0031308 ? 1.055 * Math.pow(g, 1/2.4) - 0.055 : 12.92 * g;
    b_val = b_val > 0.0031308 ? 1.055 * Math.pow(b_val, 1/2.4) - 0.055 : 12.92 * b_val;

    // 限制在[0,1]范围内
    r = Math.max(0, Math.min(1, r));
    g = Math.max(0, Math.min(1, g));
    b_val = Math.max(0, Math.min(1, b_val));

    return {
      r: Math.round(r * 255),
      g: Math.round(g * 255),
      b: Math.round(b_val * 255)
    };
  },

  // 根据背景色计算文字颜色（黑色或白色）
  getTextColorForBackground: function(hexColor) {
    // 移除#号
    hexColor = hexColor.replace(/^#/, '');

    // 解析RGB值
    let r = parseInt(hexColor.substring(0, 2), 16);
    let g = parseInt(hexColor.substring(2, 4), 16);
    let b = parseInt(hexColor.substring(4, 6), 16);

    // 计算亮度 (基于人眼对不同颜色的感知)
    // 公式: (0.299*R + 0.587*G + 0.114*B)
    let brightness = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

    // 如果亮度大于0.5，返回黑色，否则返回白色
    return brightness > 0.5 ? '#000000' : '#FFFFFF';
  }
});
