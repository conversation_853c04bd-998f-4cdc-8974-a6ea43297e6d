// pages/template/template.js

Page({
  data: {
    // 模式切换相关
    currentMode: 'image', // 'image' 图片主题色, 'custom' 自定义色卡
    currentCustomTemplateIndex: 0, // 当前自定义模板索引

    // P04 SVG相关数据 - 确保初始化为空数组
    p04PetalTemplate: '', // 花瓣SVG模板
    p04CircleTemplate: '', // 圆形SVG模板
    p04PetalDataUrls: [], // 花瓣Data URLs - 初始化为空数组
    p04CircleDataUrls: [], // 圆形Data URLs - 初始化为空数组

    templates: [
      {
        id: 3,
        name: '色卡A (1:1)',
        selected: true,
        cropScale: '1:1',
        imagePath: '/images/templates/color_a.jpg'
      },
      {
        id: 4,
        name: '色卡B (1:1)',
        selected: false,
        cropScale: '1:1',
        imagePath: '/images/templates/color_b.jpg'
      },
      {
        id: 5,
        name: '色卡C (3:4)',
        selected: false,
        cropScale: '3:4',
        imagePath: '/images/templates/color_c.jpg'
      },
      {
        id: 6,
        name: '色卡D (3:4)',
        selected: false,
        cropScale: '3:4',
        imagePath: '/images/templates/color_d.jpg'
      },
      {
        id: 7,
        name: '色卡E (3:4)',
        selected: false,
        cropScale: '3:4',
        imagePath: '/images/templates/color_e.jpg'
      },
      {
        id: 8,
        name: '色卡F (16:9)',
        selected: false,
        cropScale: '16:9',
        imagePath: '/images/templates/color_f.jpg'
      },
      {
        id: 9,
        name: '色卡G (4:5)',
        selected: false,
        cropScale: '4:5',
        imagePath: '/images/templates/color_g.jpg'
      },
      {
        id: 10,
        name: '色卡H (9:16)',
        selected: false,
        cropScale: '9:16',
        imagePath: '/images/templates/color_h.jpg'
      },
      {
        id: 11,
        name: '色卡I (9:16)',
        selected: false,
        cropScale: '9:16',
        imagePath: '/images/templates/color_i.jpg'
      },
      {
        id: 999, // 使用一个特殊ID，确保不会与其他模板冲突
        name: '其他模板',
        selected: false,
        disabled: true, // 标记为禁用
        isComingSoon: true, // 标记为即将推出
        // 使用一个简单的灰色背景作为占位图
        imagePath: '/images/templates/color_a.jpg' // 使用现有图片作为占位
      }
    ],

    // 自定义色卡模板
    customTemplates: [
      {
        id: 101,
        name: '蜜桃汽水',
        selected: true,
        colors: ['#FFD9C5', '#FFB398', '#FF947A', '#FF708D'], // 新的默认4个颜色
        imagePath: '/images/templates/custom_template_1.jpg' // 需要创建预览图
      },
      {
        id: 102,
        name: '海盐气泡',
        selected: false,
        colors: ['#0B1F5E', '#1B3CBA', '#3A66E8', '#7AA3FF', '#BED8FF'], // 新的默认5个颜色
        imagePath: '/images/templates/custom_template_2.jpg' // 需要创建预览图
      },
      {
        id: 103,
        name: '落日漫旅',
        selected: false,
        colors: ['#FFC371', '#F9E0B7', '#C66767', '#8E7F3C'], // P03模板的4个颜色
        imagePath: '/images/templates/custom_template_3.jpg' // 需要创建预览图
      },
      {
        id: 104,
        name: '春日樱语',
        selected: false,
        colors: ['#F9DFE7', '#C2E0F5', '#E8D1F2', '#F8C8DC'], // P04模板的4个颜色
        imagePath: '/images/templates/custom_template_4.jpg' // 需要创建预览图
      }
    ]
  },

  onLoad() {
    // 页面加载时确保数组初始化
    try {
      this.setData({
        p04PetalDataUrls: [],
        p04CircleDataUrls: []
      });
    } catch (error) {
      console.error('onLoad 初始化失败:', error);
    }
  },

  onReady() {
    // 页面渲染完成
    console.log('onReady: 页面渲染完成');

    // 加载P04 SVG模板
    this.loadP04SVGTemplates();
  },

  onShow() {
    // 页面显示时重新生成P04 SVG Data URLs
    // 这样可以确保从其他页面返回时显示最新的颜色
    try {
      if (this.data && this.data.p04PetalTemplate) {
        this.generateP04SVGDataUrls();
      }
    } catch (error) {
      console.error('onShow 执行失败:', error);
    }
  },

  /**
   * 加载P04 SVG模板文件
   */
  loadP04SVGTemplates() {
    try {
      // 引入P04 SVG模板工具类
      const P04SvgTemplates = require('../../utils/p04SvgTemplates');

      // 直接从工具类获取模板内容
      const petalTemplate = P04SvgTemplates.getPetalTemplate();
      const circleTemplate = P04SvgTemplates.getCircleTemplate();

      this.setData({
        p04PetalTemplate: petalTemplate,
        p04CircleTemplate: circleTemplate
      });

      // 生成P04的SVG Data URLs
      this.generateP04SVGDataUrls();

    } catch (error) {
      console.error('加载P04 SVG模板失败:', error);
      // 确保即使失败也设置空数组，避免 undefined
      this.setData({
        p04PetalTemplate: '',
        p04CircleTemplate: '',
        p04PetalDataUrls: [],
        p04CircleDataUrls: []
      });
    }
  },

  /**
   * 生成P04 SVG Data URLs
   */
  generateP04SVGDataUrls() {
    const p04Template = this.data.customTemplates.find(t => t.id === 104);
    if (!p04Template) {
      console.warn('generateP04SVGDataUrls: 未找到P04模板');
      return;
    }

    const colors = p04Template.colors;
    if (!colors || !Array.isArray(colors) || colors.length === 0) {
      console.warn('generateP04SVGDataUrls: P04模板颜色数据无效:', colors);
      return;
    }

    try {
      // 引入P04 SVG模板工具类
      const P04SvgTemplates = require('../../utils/p04SvgTemplates');

      // 使用工具类生成Data URLs
      const petalDataUrls = P04SvgTemplates.generatePetalDataURLs(colors);
      const circleDataUrls = P04SvgTemplates.generateCircleDataURLs(colors);

      // 验证生成结果
      if (!petalDataUrls || !circleDataUrls) {
        console.error('generateP04SVGDataUrls: SVG Data URLs生成失败');
        return;
      }

      this.setData({
        p04PetalDataUrls: petalDataUrls,
        p04CircleDataUrls: circleDataUrls
      });

      console.log('P04 SVG Data URLs生成完成:', petalDataUrls.length, circleDataUrls.length);
    } catch (error) {
      console.error('生成P04 SVG Data URLs失败:', error);
      // 设置空数组作为默认值，避免后续错误
      this.setData({
        p04PetalDataUrls: [],
        p04CircleDataUrls: []
      });
    }
  },






  // 切换模式
  switchMode: function(e) {
    if (!e || !e.currentTarget || !e.currentTarget.dataset) {
      console.error('switchMode: 事件对象无效');
      return;
    }

    const mode = e.currentTarget.dataset.mode;
    this.setData({
      currentMode: mode
    });

    // 重置选择状态
    if (mode === 'image') {
      // 重置图片模板选择状态
      if (this.data.templates && Array.isArray(this.data.templates)) {
        const templates = this.data.templates.map(item => ({
          ...item,
          selected: item.id === 3 // 默认选择第一个模板
        }));
        this.setData({ templates });
      }
    } else {
      // 重置自定义模板选择状态和滑动索引
      if (this.data.customTemplates && Array.isArray(this.data.customTemplates)) {
        const customTemplates = this.data.customTemplates.map((item, index) => ({
          ...item,
          selected: index === 0 // 默认选择第一个自定义模板
        }));
        this.setData({
          customTemplates,
          currentCustomTemplateIndex: 0
        });
      }
    }
  },

  // 自定义模板滑动切换
  onCustomTemplateChange: function(e) {
    if (!e || !e.detail || typeof e.detail.current !== 'number') {
      console.error('onCustomTemplateChange: 事件对象无效');
      return;
    }

    if (!this.data.customTemplates || !Array.isArray(this.data.customTemplates)) {
      console.error('onCustomTemplateChange: 自定义模板数据无效');
      return;
    }

    const current = e.detail.current;
    const customTemplates = this.data.customTemplates.map((item, index) => ({
      ...item,
      selected: index === current
    }));

    this.setData({
      currentCustomTemplateIndex: current,
      customTemplates
    });
  },

  // 选择模板
  selectTemplate: function(e) {
    if (!e || !e.currentTarget || !e.currentTarget.dataset) {
      console.error('selectTemplate: 事件对象无效');
      return;
    }

    const id = parseInt(e.currentTarget.dataset.id);
    const mode = this.data.currentMode;

    if (mode === 'image') {
      // 图片主题色模式
      if (!this.data.templates || !Array.isArray(this.data.templates)) {
        console.error('selectTemplate: 图片模板数据无效');
        return;
      }

      const clickedTemplate = this.data.templates.find(item => item && item.id == id);
      if (clickedTemplate && clickedTemplate.isComingSoon) {
        wx.showToast({
          title: '更多模板开发中，敬请期待...',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      const templates = this.data.templates.map(function(item) {
        if (!item) return null;
        return {
          id: item.id,
          name: item.name,
          cropScale: item.cropScale,
          imagePath: item.imagePath,
          selected: item.id === id,
          disabled: item.disabled,
          isComingSoon: item.isComingSoon
        };
      }).filter(item => item !== null);

      this.setData({ templates: templates });
    } else {
      // 自定义色卡模式
      if (!this.data.customTemplates || !Array.isArray(this.data.customTemplates)) {
        console.error('selectTemplate: 自定义模板数据无效');
        return;
      }

      const customTemplates = this.data.customTemplates.map(function(item) {
        if (!item) return null;
        return {
          id: item.id,
          name: item.name,
          colors: item.colors,
          imagePath: item.imagePath,
          selected: item.id === id
        };
      }).filter(item => item !== null);

      this.setData({ customTemplates: customTemplates });
    }
  },

  // 下一步操作
  goToNext: function() {
    const mode = this.data.currentMode;

    if (mode === 'image') {
      this.goToCrop();
    } else {
      this.goToCustomEditor();
    }
  },

  // 选择图片并直接裁剪
  goToCrop: function() {
    // 获取选中的模板
    var that = this;
    var selectedTemplate = null;

    // 安全检查模板数组
    if (!this.data.templates || !Array.isArray(this.data.templates) || this.data.templates.length === 0) {
      wx.showToast({
        title: '模板数据异常，请重新进入页面',
        icon: 'none'
      });
      return;
    }

    // 使用循环代替find方法
    for (var i = 0; i < this.data.templates.length; i++) {
      if (this.data.templates[i] && this.data.templates[i].selected) {
        selectedTemplate = this.data.templates[i];
        break;
      }
    }

    if (!selectedTemplate) {
      wx.showToast({
        title: '请选择模板',
        icon: 'none'
      });
      return;
    }

    // 调用微信选择图片API
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: function(res) {
        // 安全检查返回结果
        if (!res || !res.tempFiles || !Array.isArray(res.tempFiles) || res.tempFiles.length === 0) {
          wx.showToast({
            title: '图片选择失败，请重试',
            icon: 'none'
          });
          return;
        }

        var tempFilePath = res.tempFiles[0].tempFilePath;
        if (!tempFilePath) {
          wx.showToast({
            title: '图片路径获取失败，请重试',
            icon: 'none'
          });
          return;
        }

        // 设置裁剪比例
        var cropScale = selectedTemplate.cropScale || '1:1'; // 使用模板中定义的裁剪比例

        console.log('选择模板:', selectedTemplate.name, '裁剪比例:', cropScale);

        // 直接调用裁剪接口
        wx.cropImage({
          src: tempFilePath,
          cropScale: cropScale, // 根据模板设置裁剪比例
          success: function(cropRes) {
            // 裁剪成功后直接跳转到取色页面
            // 注意：此处必须使用 JavaScript API 而非 navigator 组件，原因：
            // 1. 在异步回调中进行跳转
            // 2. 需要传递动态生成的图片路径参数
            // 3. 需要传递模板ID参数
            wx.navigateTo({
              url: '/card-package/pages/colorPicker/colorPicker?imagePath=' + encodeURIComponent(cropRes.tempFilePath) + '&templateId=' + selectedTemplate.id,
            });
          },
          fail: function(err) {
            wx.showToast({
              title: '裁剪失败',
              icon: 'none'
            });
            console.error('裁剪失败:', err);

            // 裁剪失败时，尝试直接使用原图
            wx.showModal({
              title: '提示',
              content: '图片裁剪失败，是否直接使用原图？',
              success: function(res) {
                if (res.confirm) {
                  // 用户点击确定，直接使用原图
                  // 使用 navigator 组件的方式跳转，但由于这里是 JS 代码，仍需使用 wx.navigateTo
                  wx.navigateTo({
                    url: '/card-package/pages/colorPicker/colorPicker?imagePath=' + encodeURIComponent(tempFilePath) + '&templateId=' + selectedTemplate.id,
                  });
                }
              }
            });
          }
        });
      }
    });
  },

  // 跳转到自定义色卡编辑页面
  goToCustomEditor: function() {
    // 安全检查自定义模板数组
    if (!this.data.customTemplates || !Array.isArray(this.data.customTemplates) || this.data.customTemplates.length === 0) {
      wx.showToast({
        title: '自定义模板数据异常，请重新进入页面',
        icon: 'none'
      });
      return;
    }

    // 获取当前滑动位置的模板
    const currentIndex = this.data.currentCustomTemplateIndex || 0;
    const selectedTemplate = this.data.customTemplates[currentIndex];

    if (!selectedTemplate) {
      wx.showToast({
        title: '请选择模板',
        icon: 'none'
      });
      return;
    }

    // 安全检查模板数据
    if (!selectedTemplate.id || !selectedTemplate.colors) {
      wx.showToast({
        title: '模板数据不完整，请重新选择',
        icon: 'none'
      });
      return;
    }

    // 跳转到自定义色卡编辑页面
    // 注意：此处必须使用 JavaScript API 而非 navigator 组件，原因：
    // 1. 需要动态传递模板ID参数
    // 2. 需要传递复杂的颜色数组JSON数据
    // 3. 在函数中进行条件跳转
    wx.navigateTo({
      url: '/card-package/pages/customColorEditor/customColorEditor?templateId=' + selectedTemplate.id + '&colors=' + encodeURIComponent(JSON.stringify(selectedTemplate.colors))
    });
  }
})
