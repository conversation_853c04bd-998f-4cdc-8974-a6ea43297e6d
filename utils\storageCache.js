// utils/storageCache.js - Storage 缓存管理器
// 解决重复获取相同 key 的 storage 信息问题

class StorageCache {
  constructor() {
    // 内存缓存
    this.cache = new Map();
    // 缓存过期时间配置（毫秒）
    this.expireTime = {
      'adFreeStatus': 10 * 60 * 1000, // 10分钟 - 去广告状态变化不频繁
      'skinToneColorsOne': 60 * 60 * 1000, // 60分钟 - 色卡数据变化很少
      'skinToneColorsTwo': 60 * 60 * 1000, // 60分钟 - 色卡数据变化很少
      'skinToneTest_dailyClick': 24 * 60 * 60 * 1000, // 24小时 - 每日点击计数
      'colorPicker_dailyClick': 24 * 60 * 60 * 1000, // 24小时 - 每日点击计数
      'customColorEditor_dailyClick': 24 * 60 * 60 * 1000, // 24小时 - 每日点击计数
      'savedGradients': 30 * 60 * 1000, // 30分钟 - 保存的渐变
      'userSettings': 10 * 60 * 1000, // 10分钟 - 用户设置
      'default': 5 * 60 * 1000 // 默认5分钟
    };
    
    // 性能统计
    this.stats = {
      cacheHits: 0,
      cacheMisses: 0,
      storageReads: 0,
      storageWrites: 0
    };
  }

  /**
   * 获取缓存项
   * @param {string} key 
   * @returns {Object|null}
   */
  getCacheItem(key) {
    const item = this.cache.get(key);
    if (!item) return null;

    // 检查是否过期
    if (Date.now() > item.expireAt) {
      this.cache.delete(key);
      return null;
    }

    return item;
  }

  /**
   * 设置缓存项
   * @param {string} key 
   * @param {any} value 
   * @param {number} customExpireTime 自定义过期时间（毫秒）
   */
  setCacheItem(key, value, customExpireTime) {
    const expireTime = customExpireTime || this.expireTime[key] || this.expireTime.default;
    const item = {
      value,
      timestamp: Date.now(),
      expireAt: Date.now() + expireTime
    };
    this.cache.set(key, item);
  }

  /**
   * 同步获取存储数据（带缓存）
   * @param {string} key 
   * @param {any} defaultValue 默认值
   * @returns {any}
   */
  getStorageSync(key, defaultValue = null) {
    try {
      // 先检查缓存
      const cacheItem = this.getCacheItem(key);
      if (cacheItem) {
        this.stats.cacheHits++;
        console.log(`[StorageCache] 缓存命中: ${key}`);
        return cacheItem.value;
      }

      // 缓存未命中，从存储读取
      this.stats.cacheMisses++;
      this.stats.storageReads++;
      console.log(`[StorageCache] 从存储读取: ${key}`);
      
      const value = wx.getStorageSync(key);
      const result = value !== '' ? value : defaultValue;
      
      // 存入缓存
      this.setCacheItem(key, result);
      
      return result;
    } catch (error) {
      console.error(`[StorageCache] 获取存储失败: ${key}`, error);
      return defaultValue;
    }
  }

  /**
   * 异步获取存储数据（带缓存）
   * @param {string} key 
   * @param {any} defaultValue 默认值
   * @returns {Promise<any>}
   */
  getStorage(key, defaultValue = null) {
    return new Promise((resolve) => {
      try {
        // 先检查缓存
        const cacheItem = this.getCacheItem(key);
        if (cacheItem) {
          this.stats.cacheHits++;
          console.log(`[StorageCache] 缓存命中: ${key}`);
          resolve(cacheItem.value);
          return;
        }

        // 缓存未命中，异步从存储读取
        this.stats.cacheMisses++;
        this.stats.storageReads++;
        console.log(`[StorageCache] 异步从存储读取: ${key}`);
        
        wx.getStorage({
          key,
          success: (res) => {
            const result = res.data !== undefined ? res.data : defaultValue;
            // 存入缓存
            this.setCacheItem(key, result);
            resolve(result);
          },
          fail: () => {
            resolve(defaultValue);
          }
        });
      } catch (error) {
        console.error(`[StorageCache] 异步获取存储失败: ${key}`, error);
        resolve(defaultValue);
      }
    });
  }

  /**
   * 同步设置存储数据
   * @param {string} key 
   * @param {any} value 
   */
  setStorageSync(key, value) {
    try {
      this.stats.storageWrites++;
      wx.setStorageSync(key, value);
      // 更新缓存
      this.setCacheItem(key, value);
      console.log(`[StorageCache] 同步设置存储: ${key}`);
    } catch (error) {
      console.error(`[StorageCache] 同步设置存储失败: ${key}`, error);
    }
  }

  /**
   * 异步设置存储数据
   * @param {string} key 
   * @param {any} value 
   * @returns {Promise<void>}
   */
  setStorage(key, value) {
    return new Promise((resolve, reject) => {
      try {
        this.stats.storageWrites++;
        wx.setStorage({
          key,
          data: value,
          success: () => {
            // 更新缓存
            this.setCacheItem(key, value);
            console.log(`[StorageCache] 异步设置存储: ${key}`);
            resolve();
          },
          fail: (error) => {
            console.error(`[StorageCache] 异步设置存储失败: ${key}`, error);
            reject(error);
          }
        });
      } catch (error) {
        console.error(`[StorageCache] 异步设置存储异常: ${key}`, error);
        reject(error);
      }
    });
  }

  /**
   * 移除存储数据
   * @param {string} key 
   */
  removeStorageSync(key) {
    try {
      wx.removeStorageSync(key);
      this.cache.delete(key);
      console.log(`[StorageCache] 移除存储: ${key}`);
    } catch (error) {
      console.error(`[StorageCache] 移除存储失败: ${key}`, error);
    }
  }

  /**
   * 清除指定 key 的缓存
   * @param {string} key 
   */
  clearCache(key) {
    if (key) {
      this.cache.delete(key);
      console.log(`[StorageCache] 清除缓存: ${key}`);
    } else {
      this.cache.clear();
      console.log(`[StorageCache] 清除所有缓存`);
    }
  }

  /**
   * 预加载常用数据
   * @param {Array<string>} keys 需要预加载的 key 列表
   */
  async preloadData(keys = ['adFreeStatus']) {
    console.log(`[StorageCache] 预加载数据:`, keys);
    const promises = keys.map(key => this.getStorage(key));
    try {
      await Promise.all(promises);
      console.log(`[StorageCache] 预加载完成`);
    } catch (error) {
      console.error(`[StorageCache] 预加载失败:`, error);
    }
  }

  /**
   * 获取性能统计
   * @returns {Object}
   */
  getStats() {
    const total = this.stats.cacheHits + this.stats.cacheMisses;
    const hitRate = total > 0 ? (this.stats.cacheHits / total * 100).toFixed(2) : 0;
    
    return {
      ...this.stats,
      hitRate: `${hitRate}%`,
      cacheSize: this.cache.size
    };
  }

  /**
   * 清理过期缓存
   */
  cleanExpiredCache() {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, item] of this.cache.entries()) {
      if (now > item.expireAt) {
        this.cache.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`[StorageCache] 清理过期缓存: ${cleanedCount} 项`);
    }
  }

  /**
   * 预加载常用数据到缓存
   * 在应用启动时调用，避免后续重复访问
   */
  async preloadCommonData() {
    const commonKeys = [
      'adFreeStatus',
      'skinToneColorsOne',
      'skinToneColorsTwo'
    ];

    console.log('[StorageCache] 开始预加载常用数据...');

    try {
      const promises = commonKeys.map(key =>
        this.getStorage(key).catch(error => {
          console.log(`[StorageCache] 预加载 ${key} 失败:`, error);
          return null;
        })
      );

      await Promise.all(promises);
      console.log('[StorageCache] 常用数据预加载完成');
    } catch (error) {
      console.error('[StorageCache] 预加载失败:', error);
    }
  }

  /**
   * 批量获取存储数据
   * @param {Array<string>} keys 要获取的键数组
   * @returns {Promise<Object>} 键值对对象
   */
  async batchGetStorage(keys) {
    try {
      const promises = keys.map(async key => {
        const value = await this.getStorage(key);
        return { key, value };
      });

      const results = await Promise.all(promises);
      const data = {};

      results.forEach(({ key, value }) => {
        data[key] = value;
      });

      console.log(`[StorageCache] 批量获取完成: ${keys.length} 项`);
      return data;
    } catch (error) {
      console.error('[StorageCache] 批量获取失败:', error);
      throw error;
    }
  }

  /**
   * 批量设置存储数据
   * @param {Object} data 键值对对象
   * @returns {Promise<void>}
   */
  async batchSetStorage(data) {
    try {
      const promises = Object.entries(data).map(([key, value]) =>
        this.setStorage(key, value)
      );

      await Promise.all(promises);
      console.log(`[StorageCache] 批量设置完成: ${Object.keys(data).length} 项`);
    } catch (error) {
      console.error('[StorageCache] 批量设置失败:', error);
      throw error;
    }
  }

  /**
   * 获取缓存使用情况统计
   * @returns {Object} 统计信息
   */
  getCacheStats() {
    const now = Date.now();
    let validCount = 0;
    let expiredCount = 0;
    let totalSize = 0;

    for (const [key, item] of this.cache.entries()) {
      if (now > item.expireAt) {
        expiredCount++;
      } else {
        validCount++;
      }

      // 估算数据大小
      try {
        totalSize += JSON.stringify(item.data).length;
      } catch (e) {
        // 忽略无法序列化的数据
      }
    }

    return {
      ...this.stats,
      cacheSize: this.cache.size,
      validCount,
      expiredCount,
      totalSize,
      hitRate: this.stats.cacheHits / (this.stats.cacheHits + this.stats.cacheMisses) || 0
    };
  }
}

// 创建全局实例
const storageCache = new StorageCache();

// 定期清理过期缓存
setInterval(() => {
  storageCache.cleanExpiredCache();
}, 5 * 60 * 1000); // 每5分钟清理一次

module.exports = {
  storageCache,
  StorageCache
};
