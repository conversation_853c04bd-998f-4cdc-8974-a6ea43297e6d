/* pages/colorPicker/colorPicker.wxss */
.page-wrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  box-sizing: border-box;
  background-color: transparent; /* 移除背景色，使其透明 */
  /* 顶部内边距现在通过内联样式动态设置 */
  padding-bottom: 180rpx; /* 增加底部内边距，确保内容不被底部按钮遮挡 */
}

/* 颜色选择区域 */
.colors-section {
  margin-bottom: 10rpx; /* 减少底部边距，使图片位置上移 */
  padding-bottom: 0; /* 减少底部内边距 */
}

.colors-title {
  font-size: 24rpx;
  color: #333;
  margin: 12rpx auto 16rpx;
  padding: 16rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f7f7f7;
  max-width: 94%;
  border-left: 2rpx solid #07c160;
  /* 调整高度 - 进一步增加高度以适应更大的按钮 */
  min-height: 60rpx;
}

.title-left {
  display: flex;
  align-items: flex-start;
  flex: 1;
}

.title-text {
  display: flex;
  flex-direction: column;
  line-height: 1.4;
}

.title-text text:last-child {
  padding-left: 0; /* 确保第二行与第一行左对齐 */
}

/* 算法切换按钮 - 扁平但更明显的风格 */
.algorithm-switch {
  font-size: 24rpx;
  color: #07c160;
  padding: 12rpx 20rpx;
  margin-left: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  border: 1px solid #07c160;
  border-radius: 6rpx;
  transition: all 0.2s;
  background-color: rgba(7, 193, 96, 0.08);
  /* 调整尺寸 - 进一步增大按钮使其更容易点击 */
  height: 36rpx;
  width: 180rpx;
  white-space: nowrap;
  box-sizing: content-box;
  /* 添加轻微阴影效果，使按钮更加突出 */
  box-shadow: 0 2rpx 6rpx rgba(7, 193, 96, 0.15);
}

/* 使用hover-class替代active，效果更好 */
.algorithm-switch-hover {
  background-color: rgba(7, 193, 96, 0.25);
  color: #06ad56;
  transform: scale(0.97);
  box-shadow: 0 2rpx 4rpx rgba(7, 193, 96, 0.15) inset;
}

/* 按钮文字 */
.switch-text {
  line-height: 1.3;
  font-size: 24rpx;
  font-weight: 500; /* 稍微加粗文字，使其更加明显 */
}

/* 极简的信息点 */
.info-dot {
  width: 6rpx;
  height: 6rpx;
  margin-right: 8rpx;
  margin-top: 10rpx; /* 对准第一行文字 */
  background-color: #07c160;
  border-radius: 50%;
  flex-shrink: 0;
}

.colors-subtitle {
  font-size: 22rpx;
  color: #888;
  text-align: center;
  margin-bottom: 15rpx;
  padding: 0 30rpx;
  animation: fade-in 0.3s ease-out;
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.colors-row {
  padding: 0 20rpx; /* 增加左右内边距 */
  position: relative;
  z-index: 5;
  margin-top: 5rpx; /* 减少上边距 */
  min-height: 160rpx; /* 确保有足够高度容纳颜色信息 */
}

/* 颜色容器 - 负责水平布局 */
.colors-container {
  display: flex;
  justify-content: space-between; /* 5个颜色均匀分布 */
  align-items: flex-start; /* 顶部对齐 */
  flex-wrap: nowrap; /* 不允许换行，确保5个颜色在一行 */
  width: 100%;
  transition: opacity 0.3s ease; /* 添加透明度过渡 */
}

/* 颜色容器过渡状态 */
.colors-container.transitioning {
  opacity: 0.7; /* 过渡时稍微降低透明度 */
}

/* 颜色项容器 */
.color-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1; /* 使用flex: 1让每个颜色项平均分配空间 */
  max-width: 130rpx; /* 限制最大宽度 */
  min-width: 100rpx; /* 设置最小宽度 */
  margin: 0 5rpx; /* 简化边距设置 */
  height: 140rpx; /* 减小高度，因为移除了提示文字 */
  position: relative; /* 为绝对定位的子元素提供参考 */
  transition: transform 0.1s ease, opacity 0.3s ease; /* 添加透明度过渡 */
  touch-action: none; /* 防止触摸事件被系统处理 */
}

/* 颜色项过渡状态 */
.color-item.color-transitioning {
  opacity: 0.8; /* 过渡时稍微降低透明度 */
  transform: scale(0.98); /* 稍微缩小 */
}

/* 拖拽状态样式 */
.color-item.dragging {
  opacity: 0.9;
  transform: scale(1.1);
  z-index: 100;
}

.color-block {
  width: 100%; /* 使用100%宽度，适应父容器 */
  max-width: 120rpx; /* 限制最大宽度 */
  height: 80rpx;
  border-radius: 12rpx;
  position: relative;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease, background-color 0.4s ease; /* 增强背景色过渡 */
  overflow: hidden; /* 确保内容不会溢出 */
  cursor: pointer; /* 指示可点击 */
  margin-top: 10rpx; /* 添加顶部边距 */
}

/* 添加棋盘格背景，便于查看半透明颜色 */
.color-block::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(45deg, #eee 25%, transparent 25%),
    linear-gradient(-45deg, #eee 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, #eee 75%),
    linear-gradient(-45deg, transparent 75%, #eee 75%);
  background-size: 16rpx 16rpx;
  background-position: 0 0, 0 8rpx, 8rpx -8rpx, -8rpx 0px;
  z-index: -1;
  opacity: 0.5;
}

/* 悬停效果 */
.color-block:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.color-block.selected {
  transform: scale(1.08);
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.2);
  border: 2rpx solid white;
}

/* 选中状态的点击效果 */
.color-block.selected:active {
  transform: scale(1.04);
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.15);
}

.color-number {
  position: absolute;
  right: 8rpx;
  bottom: 8rpx;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.9);
  color: #333;
  font-size: 22rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
}

/* 图片预览区域 */
.image-section {
  margin-top: 0; /* 减少顶部边距，使图片位置上移 */
  margin-bottom: 40rpx;
  background-color: transparent; /* 确保背景透明 */
  box-shadow: none; /* 确保没有阴影 */
  border: none; /* 确保没有边框 */
}

/* 图片标题已移除 */

.image-container {
  width: 100%;
  display: flex;
  justify-content: center;
  position: relative;
  min-height: 200rpx; /* 确保在加载时有足够的高度 */
  touch-action: none; /* 禁用默认触摸行为，防止滚动 */
  background-color: transparent; /* 确保背景透明 */
  box-shadow: none; /* 确保没有阴影 */
  border: none; /* 确保没有边框 */
}

.preview-image {
  width: 690rpx;
  height: calc(690rpx * 4 / 3); /* 保持3:4比例 */
  border-radius: 0; /* 移除圆角 */
  object-fit: contain; /* 使用contain确保图片完整显示 */
  box-shadow: none; /* 移除阴影 */
  border: none; /* 移除边框 */
  background-color: transparent; /* 移除背景色，使其透明 */
}

/* 特别为裁剪后的图片添加样式 */
.cropped-image {
  box-shadow: none !important; /* 强制移除阴影 */
  border: none !important; /* 强制移除边框 */
  background-color: transparent !important; /* 强制移除背景色 */
  border-radius: 0 !important; /* 强制移除圆角 */
}

/* 图片点击提示已移除 */

/* 放大镜容器 */
.magnifier-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw; /* 使用视口单位 */
  height: 100vh;
  z-index: 9999; /* 提高z-index确保在最上层 */
  pointer-events: none; /* 确保不会阻止点击事件 */
}

/* 连接线 */
.connector-line {
  position: absolute;
  height: 2px; /* 使用px单位 */
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.9);
  z-index: 9998; /* 提高z-index */
}

/* 目标点 */
.target-point {
  position: absolute;
  width: 8px; /* 使用px单位 */
  height: 8px;
  border-radius: 50%;
  background-color: white;
  border: 1px solid black;
  transform: translate(-50%, -50%);
  z-index: 9997; /* 提高z-index */
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.9);
}

/* 放大镜效果 */
.magnifier {
  position: absolute;
  width: 90px; /* 使用px单位代替rpx，确保在不同设备上一致 */
  height: 90px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 0 0 1px black, 0 0 10px rgba(0, 0, 0, 0.6);
  overflow: hidden;
  transform: translate(-50%, -50%);
  background-color: rgba(255, 255, 255, 0.1); /* 添加背景色，确保元素可见 */
  z-index: 9999; /* 确保放大镜在最上层 */
}

.magnifier-content {
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 50%;
  position: relative;
}

/* 放大镜中的图片 */
.magnifier-image {
  position: absolute;
  transform-origin: 0 0;
  object-fit: cover;
  z-index: 1000;
}

/* 水平十字线 */
.magnifier-crosshair-h {
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: rgba(255, 255, 255, 0.8);
  transform: translateY(-50%);
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.8);
  z-index: 1002;
  pointer-events: none;
}

/* 垂直十字线 */
.magnifier-crosshair-v {
  position: absolute;
  top: 0;
  left: 50%;
  width: 2px;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  transform: translateX(-50%);
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.8);
  z-index: 1002;
  pointer-events: none;
}

/* 颜色指示器 */
.color-indicator {
  position: absolute;
  bottom: 10px;
  right: 10px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.5);
  z-index: 1003;
}

/* 图片加载状态 */
.image-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: transparent; /* 移除背景色，使其透明 */
  border-radius: 0; /* 移除圆角 */
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #07c160;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 颜色信息面板样式已移除 */

/* 这些样式已在上面重新定义，可以移除 */

.color-hint {
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
  display: flex;
  align-items: center;
}

.color-hint::before {
  content: "👆";
  margin-right: 6rpx;
  font-size: 22rpx;
}

.color-hint-secondary {
  font-size: 22rpx;
  color: #888;
  margin-top: 8rpx;
  font-style: italic;
}

/* 颜色信息样式 */
.color-info {
  width: 100%;
  position: absolute;
  top: 90rpx; /* 位于颜色块下方 */
  left: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: fade-in 0.3s ease-out;
  z-index: 5;
}

.color-hex {
  font-size: 22rpx;
  font-weight: bold;
  color: #333;
  background-color: #f5f5f5;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  text-align: center;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

.color-hex:active {
  background-color: #e8e8e8;
}

/* 提示文字样式已移除 */

/* 取色器相关样式已移除 */

.btn-container {
  padding: 20rpx 30rpx 50rpx; /* 增加底部内边距，确保白色背景延伸到屏幕底部 */
  width: 100%;
  box-sizing: border-box;
  position: fixed;
  bottom: 0; /* 改回0，让容器紧贴屏幕底部 */
  left: 0;
  background-color: white;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.next-btn {
  background-color: #07c160;
  color: white;
  border-radius: 8rpx; /* 统一圆角 */
  font-size: 30rpx; /* 统一字体大小 */
  padding: 18rpx 0; /* 统一内边距 */
  width: 100%;
  box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.15); /* 统一阴影 */
  transition: all 0.2s ease;
  font-weight: 500;
}

.next-btn:active {
  opacity: 0.9;
  transform: scale(0.98);
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 颜色加载状态样式 */
.colors-loading {
  min-height: 200rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.colors-loading .loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.colors-error {
  text-align: center;
  padding: 40rpx;
  color: #999;
}

/* 算法选择弹窗样式 */
.algorithm-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.algorithm-container {
  width: 90%;
  max-width: 650rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  max-height: 80vh;
  animation: scaleIn 0.2s ease;
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.algorithm-header {
  padding: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #e0e0e0;
  background-color: #f8f8f8;
}

.algorithm-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  text-align: center;
}

.algorithm-close {
  font-size: 36rpx;
  color: #999;
  line-height: 1;
  padding: 10rpx;
  margin: -10rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.algorithm-close-hover {
  opacity: 0.7;
}

.close-icon {
  font-size: 36rpx;
  line-height: 1;
}

.algorithm-content {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
  background-color: #f8f8f8;
}

.algorithm-item {
  padding: 20rpx;
  margin-bottom: 16rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  transition: all 0.2s;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.algorithm-item:last-child {
  margin-bottom: 0;
}

.algorithm-item-hover {
  transform: scale(0.98);
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
}

.algorithm-item.selected {
  background-color: rgba(7, 193, 96, 0.05);
  border: 1rpx solid #07c160;
  box-shadow: 0 2rpx 10rpx rgba(7, 193, 96, 0.1);
}

.algorithm-item.selected::after {
  content: "✓";
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  color: #07c160;
  font-weight: bold;
  font-size: 28rpx;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.algorithm-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 12rpx;
  padding-right: 40rpx; /* 为勾选图标留出空间 */
}

.algorithm-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  padding-right: 40rpx; /* 为勾选图标留出空间 */
}

/* 由于我们已经将适用场景合并到描述中，这个样式可以保留但不再单独使用 */
.algorithm-usage {
  font-size: 24rpx;
  color: #07c160;
  line-height: 1.5;
  display: inline;
}

/* 为了让"适用场景"文本在描述中突出显示 */
.algorithm-desc text {
  color: #07c160;
  font-weight: 500;
}

.algorithm-footer {
  padding: 24rpx;
  display: flex;
  justify-content: space-between;
  background-color: #f8f8f8;
  border-top: 1rpx solid #e0e0e0;
}

.algorithm-cancel, .algorithm-confirm {
  flex: 1;
  padding: 16rpx 0;
  font-size: 30rpx;
  border-radius: 8rpx;
  margin: 0 10rpx;
  text-align: center;
  transition: all 0.2s;
  line-height: 1.4;
}

.algorithm-cancel {
  background-color: #f5f5f5;
  color: #666;
  border: 1rpx solid #e0e0e0;
}

.algorithm-cancel-hover {
  background-color: #e8e8e8;
}

.algorithm-confirm {
  background-color: #07c160;
  color: #fff;
  border: 1rpx solid #07c160;
  box-shadow: 0 2rpx 6rpx rgba(7, 193, 96, 0.3);
}

.algorithm-confirm-hover {
  background-color: #06ad56;
}
