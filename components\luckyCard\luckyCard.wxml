<!-- 幸运签卡片组件 - 重新设计的简化版本 -->
<view class="lucky-card-container {{isVisible ? 'visible' : ''}} {{isEntering ? 'entering' : ''}}">
  <!-- 光效背景 -->
  <view class="card-glow"></view>

  <!-- 关闭按钮 -->
  <view class="close-btn" bindtap="closeCard">×</view>

  <!-- 卡片主体 -->
  <view class="lucky-card {{flipped ? 'flipped' : ''}} {{isEntering ? 'entering' : ''}}" style="border-color: {{luckyData.hex || luckyData.color || '#F7D000'}};">
    <!-- 卡片正面 -->
    <view class="card-front">

    <!-- 左上角颜色方块 -->
    <view class="yellow-square" style="background-color: {{luckyData.hex || luckyData.color || '#F7D000'}};"></view>

    <!-- 左上角运势文字 -->
    <view class="fortune-text-left">{{luckyData.fortune || '大吉'}}</view>

    <!-- 日期 -->
    <view class="date-text">{{currentDate}}</view>

    <!-- 右侧竖排颜色名称 -->
    <view class="color-name-vertical" style="color: {{luckyData.hex || luckyData.color || '#F7D000'}};">{{luckyData.colorName || '不焦绿'}}</view>

    <!-- 中间签文区域 -->
    <view class="fortune-content-center">
      <view class="fortune-title">【{{luckyData.title || '搞钱神旺签'}}】</view>
      <view class="fortune-text-main">{{formattedContent || '两眼盯铜板，一心钻钱眼。\n朝朝算收益，夜夜梦财源。'}}</view>
    </view>

    <!-- 底部色值信息 -->
    <view class="color-values">
      <view class="color-row">
        <text class="color-label">RGB</text>
        <text class="color-value">{{luckyData.rgb || '247, 208, 0'}}</text>
      </view>
      <view class="color-row">
        <text class="color-label">HEX</text>
        <text class="color-value">{{luckyData.hex || '#F7D000'}}</text>
      </view>
    </view>

    <!-- 底部提示文字 -->
    <view class="bottom-tip">每日一签，仅供娱乐</view>
  </view>

  <!-- 卡片背面 -->
  <view class="card-back">
    <!-- 解读内容 -->
    <view class="interpretation-content">{{luckyData.interpretation && luckyData.interpretation.content ? luckyData.interpretation.content : '【解签】\n"财运亨通" 无疑！走路算 "步数换钱法"，逛街满眼 "商机雷达"，聊天三句不离 "理财、副业、创收"，人生信条："理财使我快乐"～'}}</view>

    <!-- 底部提示文字 -->
    <view class="bottom-tip-back">每日一签，仅供娱乐</view>
  </view>

  </view>

  <!-- 外部按钮区域 -->
  <view class="external-buttons {{showButtons ? 'show' : ''}}" wx:if="{{showButtons}}">
    <button class="external-btn primary" bindtap="flipCard">
      <text class="btn-icon">{{flipped ? '←' : '○'}}</text>
      <text class="btn-text">{{flipped ? '返回签文' : '查看解读'}}</text>
    </button>
    <button class="external-btn secondary" open-type="share">
      <text class="btn-icon">↗</text>
      <text class="btn-text">分享好运</text>
    </button>
  </view>
</view>

<!-- Canvas元素已临时移除，避免_getData错误 -->
<!--
<canvas
  type="2d"
  id="luckyShareCanvas"
  style="width: 500px; height: 800px; position: fixed; top: -1000px; left: -1000px; z-index: -1; opacity: 0; pointer-events: none;"
></canvas>
-->
