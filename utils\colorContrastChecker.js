// 颜色对比度检查工具
// 用于检查幸运签颜色在白色背景上的可读性

// 将十六进制颜色转换为RGB
function hexToRgb(hex) {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
}

// 计算相对亮度
function getLuminance(r, g, b) {
  const [rs, gs, bs] = [r, g, b].map(c => {
    c = c / 255;
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
  });
  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
}

// 计算对比度
function getContrastRatio(color1, color2) {
  const lum1 = getLuminance(color1.r, color1.g, color1.b);
  const lum2 = getLuminance(color2.r, color2.g, color2.b);
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);
  return (brightest + 0.05) / (darkest + 0.05);
}

// 检查颜色对比度是否符合WCAG标准
function checkContrast(colorHex, backgroundHex = '#FFFFFF') {
  const color = hexToRgb(colorHex);
  const background = hexToRgb(backgroundHex);
  
  if (!color || !background) {
    return { valid: false, error: '无效的颜色值' };
  }
  
  const ratio = getContrastRatio(color, background);
  
  return {
    valid: true,
    ratio: ratio,
    passAA: ratio >= 4.5,      // WCAG AA标准
    passAAA: ratio >= 7,       // WCAG AAA标准
    passLarge: ratio >= 3,     // 大文本AA标准
    recommendation: ratio < 3 ? 'poor' : ratio < 4.5 ? 'fair' : ratio < 7 ? 'good' : 'excellent'
  };
}

// 建议更好的颜色
function suggestBetterColor(originalHex) {
  const original = hexToRgb(originalHex);
  if (!original) return null;
  
  // 如果颜色太浅，建议加深
  const luminance = getLuminance(original.r, original.g, original.b);
  
  if (luminance > 0.7) {
    // 颜色太亮，建议加深
    const factor = 0.7; // 降低亮度
    const newR = Math.round(original.r * factor);
    const newG = Math.round(original.g * factor);
    const newB = Math.round(original.b * factor);
    
    return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
  }
  
  return originalHex; // 颜色已经足够深
}

// 检查所有幸运签颜色
function checkAllLuckyColors(luckyCards) {
  const results = [];
  const problematicColors = [];
  
  luckyCards.forEach(card => {
    const result = checkContrast(card.color);
    
    if (result.valid) {
      const cardResult = {
        id: card.id,
        colorName: card.colorName,
        color: card.color,
        fortune: card.fortune,
        ...result
      };
      
      results.push(cardResult);
      
      // 如果对比度不够，添加到问题列表
      if (!result.passLarge) {
        const suggestion = suggestBetterColor(card.color);
        problematicColors.push({
          ...cardResult,
          suggestedColor: suggestion
        });
      }
    }
  });
  
  return {
    all: results,
    problematic: problematicColors,
    summary: {
      total: results.length,
      passAA: results.filter(r => r.passAA).length,
      passAAA: results.filter(r => r.passAAA).length,
      passLarge: results.filter(r => r.passLarge).length,
      poor: results.filter(r => r.recommendation === 'poor').length
    }
  };
}

module.exports = {
  hexToRgb,
  getLuminance,
  getContrastRatio,
  checkContrast,
  suggestBetterColor,
  checkAllLuckyColors
};
