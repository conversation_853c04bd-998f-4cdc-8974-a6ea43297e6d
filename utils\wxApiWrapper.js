/**
 * 微信API安全包装器
 * 为所有微信API调用提供统一的错误处理和Promise化
 */

// 导航相关API包装
const navigation = {
  navigateTo: (options) => {
    return new Promise((resolve, reject) => {
      wx.navigateTo({
        ...options,
        success: resolve,
        fail: (err) => {
          console.error('navigateTo失败:', err);
          reject(new Error(`页面跳转失败: ${err.errMsg || '未知错误'}`));
        }
      });
    });
  },

  redirectTo: (options) => {
    return new Promise((resolve, reject) => {
      wx.redirectTo({
        ...options,
        success: resolve,
        fail: (err) => {
          console.error('redirectTo失败:', err);
          reject(new Error(`页面重定向失败: ${err.errMsg || '未知错误'}`));
        }
      });
    });
  },

  switchTab: (options) => {
    return new Promise((resolve, reject) => {
      wx.switchTab({
        ...options,
        success: resolve,
        fail: (err) => {
          console.error('switchTab失败:', err);
          reject(new Error(`切换标签页失败: ${err.errMsg || '未知错误'}`));
        }
      });
    });
  },

  navigateBack: (options = {}) => {
    return new Promise((resolve, reject) => {
      wx.navigateBack({
        delta: 1,
        ...options,
        success: resolve,
        fail: (err) => {
          console.error('navigateBack失败:', err);
          reject(new Error(`返回上一页失败: ${err.errMsg || '未知错误'}`));
        }
      });
    });
  }
};

// UI相关API包装
const ui = {
  showToast: (options) => {
    return new Promise((resolve, reject) => {
      wx.showToast({
        duration: 2000,
        ...options,
        success: resolve,
        fail: (err) => {
          console.error('showToast失败:', err);
          reject(new Error(`显示提示失败: ${err.errMsg || '未知错误'}`));
        }
      });
    });
  },

  showModal: (options) => {
    return new Promise((resolve, reject) => {
      wx.showModal({
        ...options,
        success: resolve,
        fail: (err) => {
          console.error('showModal失败:', err);
          reject(new Error(`显示模态框失败: ${err.errMsg || '未知错误'}`));
        }
      });
    });
  },

  showLoading: (options) => {
    return new Promise((resolve, reject) => {
      wx.showLoading({
        title: '加载中...',
        mask: true,
        ...options,
        success: resolve,
        fail: (err) => {
          console.error('showLoading失败:', err);
          reject(new Error(`显示加载提示失败: ${err.errMsg || '未知错误'}`));
        }
      });
    });
  },

  hideLoading: () => {
    return new Promise((resolve, reject) => {
      wx.hideLoading({
        success: resolve,
        fail: (err) => {
          console.error('hideLoading失败:', err);
          // hideLoading失败不应该阻断流程
          resolve();
        }
      });
    });
  },

  setNavigationBarTitle: (options) => {
    return new Promise((resolve, reject) => {
      wx.setNavigationBarTitle({
        ...options,
        success: resolve,
        fail: (err) => {
          console.warn('设置导航栏标题失败:', err);
          // 导航栏标题设置失败不应该阻断流程
          resolve();
        }
      });
    });
  }
};

// 存储相关API包装
const storage = {
  setStorage: (key, data) => {
    return new Promise((resolve, reject) => {
      wx.setStorage({
        key,
        data,
        success: resolve,
        fail: (err) => {
          console.error('setStorage失败:', err);
          reject(new Error(`存储数据失败: ${err.errMsg || '未知错误'}`));
        }
      });
    });
  },

  getStorage: (key) => {
    return new Promise((resolve, reject) => {
      wx.getStorage({
        key,
        success: (res) => resolve(res.data),
        fail: (err) => {
          console.error('getStorage失败:', err);
          reject(new Error(`获取存储数据失败: ${err.errMsg || '未知错误'}`));
        }
      });
    });
  },

  removeStorage: (key) => {
    return new Promise((resolve, reject) => {
      wx.removeStorage({
        key,
        success: resolve,
        fail: (err) => {
          console.error('removeStorage失败:', err);
          reject(new Error(`删除存储数据失败: ${err.errMsg || '未知错误'}`));
        }
      });
    });
  }
};

// 媒体相关API包装
const media = {
  getImageInfo: (src) => {
    return new Promise((resolve, reject) => {
      // 添加超时机制
      const timeout = setTimeout(() => {
        reject(new Error('获取图片信息超时'));
      }, 10000);

      wx.getImageInfo({
        src,
        success: (res) => {
          clearTimeout(timeout);
          resolve(res);
        },
        fail: (err) => {
          clearTimeout(timeout);
          console.error('getImageInfo失败:', err);
          reject(new Error(`获取图片信息失败: ${err.errMsg || '未知错误'}`));
        }
      });
    });
  },

  saveImageToPhotosAlbum: (filePath) => {
    return new Promise((resolve, reject) => {
      wx.saveImageToPhotosAlbum({
        filePath,
        success: resolve,
        fail: (err) => {
          console.error('saveImageToPhotosAlbum失败:', err);
          reject(new Error(`保存图片到相册失败: ${err.errMsg || '未知错误'}`));
        }
      });
    });
  },

  chooseImage: (options = {}) => {
    return new Promise((resolve, reject) => {
      wx.chooseImage({
        count: 1,
        sizeType: ['original', 'compressed'],
        sourceType: ['album', 'camera'],
        ...options,
        success: resolve,
        fail: (err) => {
          if (err.errMsg && err.errMsg.includes('cancel')) {
            reject(new Error('用户取消选择图片'));
          } else {
            console.error('chooseImage失败:', err);
            reject(new Error(`选择图片失败: ${err.errMsg || '未知错误'}`));
          }
        }
      });
    });
  }
};

// 权限相关API包装
const permission = {
  getSetting: () => {
    return new Promise((resolve, reject) => {
      wx.getSetting({
        success: resolve,
        fail: (err) => {
          console.error('getSetting失败:', err);
          reject(new Error(`获取设置失败: ${err.errMsg || '未知错误'}`));
        }
      });
    });
  },

  authorize: (scope) => {
    return new Promise((resolve, reject) => {
      wx.authorize({
        scope,
        success: resolve,
        fail: (err) => {
          console.error('authorize失败:', err);
          reject(new Error(`授权失败: ${err.errMsg || '未知错误'}`));
        }
      });
    });
  },

  openSetting: () => {
    return new Promise((resolve, reject) => {
      wx.openSetting({
        success: resolve,
        fail: (err) => {
          console.error('openSetting失败:', err);
          reject(new Error(`打开设置失败: ${err.errMsg || '未知错误'}`));
        }
      });
    });
  }
};

// 系统信息相关API包装
const system = {
  /**
   * 获取系统信息（废弃方法，建议使用组合API）
   * @deprecated 建议使用 getWindowInfo, getDeviceInfo, getAppBaseInfo 等组合API
   */
  getSystemInfo: () => {
    return new Promise((resolve, reject) => {
      wx.getSystemInfo({
        success: resolve,
        fail: (err) => {
          console.error('getSystemInfo失败:', err);
          reject(new Error(`获取系统信息失败: ${err.errMsg || '未知错误'}`));
        }
      });
    });
  },

  /**
   * 获取窗口信息
   */
  getWindowInfo: () => {
    return new Promise((resolve, reject) => {
      try {
        const windowInfo = wx.getWindowInfo();
        resolve(windowInfo);
      } catch (err) {
        console.error('getWindowInfo失败:', err);
        reject(new Error(`获取窗口信息失败: ${err.message || '未知错误'}`));
      }
    });
  },

  /**
   * 获取设备信息
   */
  getDeviceInfo: () => {
    return new Promise((resolve, reject) => {
      try {
        const deviceInfo = wx.getDeviceInfo();
        resolve(deviceInfo);
      } catch (err) {
        console.error('getDeviceInfo失败:', err);
        reject(new Error(`获取设备信息失败: ${err.message || '未知错误'}`));
      }
    });
  },

  /**
   * 获取应用基础信息
   */
  getAppBaseInfo: () => {
    return new Promise((resolve, reject) => {
      try {
        const appBaseInfo = wx.getAppBaseInfo();
        resolve(appBaseInfo);
      } catch (err) {
        console.error('getAppBaseInfo失败:', err);
        reject(new Error(`获取应用基础信息失败: ${err.message || '未知错误'}`));
      }
    });
  },

  /**
   * 获取系统设置
   */
  getSystemSetting: () => {
    return new Promise((resolve, reject) => {
      try {
        const systemSetting = wx.getSystemSetting();
        resolve(systemSetting);
      } catch (err) {
        console.error('getSystemSetting失败:', err);
        reject(new Error(`获取系统设置失败: ${err.message || '未知错误'}`));
      }
    });
  },

  /**
   * 获取应用授权设置
   */
  getAppAuthorizeSetting: () => {
    return new Promise((resolve, reject) => {
      try {
        const appAuthorizeSetting = wx.getAppAuthorizeSetting();
        resolve(appAuthorizeSetting);
      } catch (err) {
        console.error('getAppAuthorizeSetting失败:', err);
        reject(new Error(`获取应用授权设置失败: ${err.message || '未知错误'}`));
      }
    });
  },

  /**
   * 获取完整系统信息（推荐方法）
   * 使用新的组合API获取完整的系统信息
   */
  getCompleteSystemInfo: async () => {
    try {
      const [windowInfo, deviceInfo, appBaseInfo, systemSetting, appAuthorizeSetting] = await Promise.all([
        system.getWindowInfo(),
        system.getDeviceInfo(),
        system.getAppBaseInfo(),
        system.getSystemSetting(),
        system.getAppAuthorizeSetting()
      ]);

      // 组合成完整的系统信息对象
      return {
        ...windowInfo,
        ...deviceInfo,
        ...appBaseInfo,
        ...systemSetting,
        ...appAuthorizeSetting,
        // 保持向后兼容的字段映射
        windowWidth: windowInfo.windowWidth,
        windowHeight: windowInfo.windowHeight,
        screenWidth: windowInfo.screenWidth,
        screenHeight: windowInfo.screenHeight,
        statusBarHeight: windowInfo.statusBarHeight,
        safeArea: windowInfo.safeArea,
        platform: deviceInfo.platform,
        system: deviceInfo.system,
        pixelRatio: deviceInfo.pixelRatio,
        version: appBaseInfo.version,
        SDKVersion: appBaseInfo.SDKVersion
      };
    } catch (err) {
      console.error('getCompleteSystemInfo失败:', err);
      throw new Error(`获取完整系统信息失败: ${err.message || '未知错误'}`);
    }
  }
};

module.exports = {
  navigation,
  ui,
  storage,
  media,
  permission,
  system
};
