// components/color-card-custom-p03/color-card-custom-p03.js - P03样式自定义色卡组件
const { validateCanvasContext, safeClearCanvas, safeGetCanvasContext, getDevicePixelRatio, safeSetCanvasSize } = require('../../utils/canvasUtils.js');

Component({
  properties: {
    colors: {
      type: Array,
      value: ['#FFC371', '#F9E0B7', '#C66767', '#8E7F3C']
    },
    title: {
      type: String,
      value: '落日漫旅'
    },
    subTitle: {
      type: String,
      value: '@KALA配色'
    },
    topBackgroundColor: {
      type: String,
      value: '#F7E8E4'
    },
    bottomBackgroundColor: {
      type: String,
      value: '#FFFFFF'
    },
    fontColor: {
      type: String,
      value: '#C66767'
    },
    codeColor: {
      type: String,
      value: '#7D7D7D'
    }
  },

  data: {
    canvasWidth: 800, // 缩小到原始尺寸的50%，避免超出限制
    canvasHeight: 1066 // 缩小到原始尺寸的50%，保持比例
  },

  lifetimes: {
    attached() {
      console.log('P03自定义色卡组件已加载');
    },

    ready() {
      console.log('P03自定义色卡组件布局完成，开始生成色卡 - 版本2024.1');
      setTimeout(() => {
        this.generateColorCard();
      }, 100);
    }
  },

  methods: {
    // 获取设备像素比
    getDevicePixelRatio() {
      try {
        const deviceInfo = wx.getDeviceInfo();
        return deviceInfo.pixelRatio || 2;
      } catch (error) {
        console.warn('获取设备像素比失败，使用默认值:', error);
        return 2; // 默认值
      }
    },

    // 生成P03样式自定义色卡
    async generateColorCard() {
      try {
        const { colors, title, subTitle, topBackgroundColor, bottomBackgroundColor, fontColor, codeColor } = this.properties;
        const { canvasWidth, canvasHeight } = this.data;

        console.log('开始生成P03样式自定义色卡:', { colors, title, topBackgroundColor, bottomBackgroundColor, fontColor, codeColor });

        // 验证颜色数据
        if (!colors || !Array.isArray(colors) || colors.length === 0) {
          console.error('P03色卡生成失败：颜色数据无效');
          this.triggerEvent('generated', { path: '' });
          return;
        }

        // 创建Canvas上下文 - 修复Promise错误处理
        const canvas = await this.getCanvasNode();
        if (!canvas || !canvas.node) {
          console.error('P03色卡生成失败：Canvas节点获取失败');
          this.triggerEvent('generated', { path: '', error: 'Canvas节点获取失败' });
          return;
        }

        const ctx = safeGetCanvasContext(canvas.node, '2d', 'P03色卡');

        // 验证上下文是否正确获取
        if (!ctx) {
          console.error('P03色卡生成失败：Canvas上下文获取失败');
          this.triggerEvent('generated', { path: '' });
          return;
        }

        const dpr = getDevicePixelRatio();

        // 设置Canvas尺寸
        if (!safeSetCanvasSize(canvas.node, ctx, canvasWidth, canvasHeight, dpr, 'P03色卡')) {
          console.error('P03色卡生成失败：Canvas尺寸设置失败');
          this.triggerEvent('generated', { path: '' });
          return;
        }

        console.log('P03 Canvas设置完成，开始绘制');

        // 绘制P03样式色卡
        this.drawP03Card(ctx, canvasWidth, canvasHeight, colors, title, subTitle, topBackgroundColor, bottomBackgroundColor, fontColor, codeColor);

        console.log('P03绘制完成，准备保存');

        // 保存Canvas
        setTimeout(() => {
          this.saveCanvas(canvas.node);
        }, 300);

      } catch (error) {
        console.error('P03色卡生成过程中发生错误:', error);
        this.handleCanvasError(error);
      }
    },

    // 获取Canvas节点 - 带超时和错误处理
    getCanvasNode: function() {
      return new Promise((resolve, reject) => {
        const query = wx.createSelectorQuery().in(this);

        // 设置超时机制
        const timeout = setTimeout(() => {
          reject(new Error('P03 Canvas节点获取超时'));
        }, 5000);

        query.select('#customColorCardP03Canvas')
          .fields({ node: true, size: true })
          .exec((res) => {
            clearTimeout(timeout);

            console.log('P03 Canvas查询结果:', res);
            if (res && res[0] && res[0].node) {
              resolve(res[0]);
            } else {
              reject(new Error('P03 Canvas节点获取失败：节点不存在或无效'));
            }
          });
      });
    },

    // Canvas错误处理
    handleCanvasError: function(error) {
      console.error('P03 Canvas操作错误:', error);

      let errorMessage = 'P03色卡生成失败';
      if (error.message) {
        if (error.message.includes('超时')) {
          errorMessage = 'P03 Canvas初始化超时';
        } else if (error.message.includes('节点')) {
          errorMessage = 'P03 Canvas节点获取失败';
        } else if (error.message.includes('上下文')) {
          errorMessage = 'P03 Canvas上下文获取失败';
        }
      }

      // 触发错误事件，让父组件知道生成失败
      this.triggerEvent('generated', {
        path: '',
        error: errorMessage
      });
    },

    // 绘制P03样式色卡
    drawP03Card(ctx, width, height, colors, title, subTitle, topBgColor, bottomBgColor, titleColor, codeColor) {
      // 验证上下文并清空画布
      if (!safeClearCanvas(ctx, width, height, 'P03色卡绘制')) {
        return;
      }

      // 绘制整体背景
      ctx.fillStyle = '#FFFFFF';
      ctx.fillRect(0, 0, width, height);

      // 绘制上半部分背景 (按P03比例 1052/2132)
      const topHeight = Math.floor(height * (1052 / 2132));
      ctx.fillStyle = topBgColor || '#F7E8E4';
      ctx.fillRect(0, 0, width, topHeight);

      // 绘制下半部分背景
      ctx.fillStyle = bottomBgColor || '#FFFFFF';
      ctx.fillRect(0, topHeight, width, height - topHeight);

      // 绘制主标题 (调整位置为上方)
      const titleY = Math.floor(height * (380 / 2132)); // 调整Y位置，为副标题留出空间
      const titleFontSize = Math.floor(84 * width / 800); // 基于新的Canvas尺寸计算字体大小
      ctx.fillStyle = titleColor || '#C66767';
      ctx.font = `bold ${titleFontSize}px Arial, sans-serif`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(title || '落日漫旅', width / 2, titleY);

      // 绘制副标题 (在主标题下方，位置再下移，字号再调小)
      const subTitleY = Math.floor(height * (540 / 2132)); // 位置再下移
      const subTitleFontSize = Math.floor(32 * width / 800); // 字号再调小
      ctx.fillStyle = titleColor || '#C66767';
      ctx.globalAlpha = 0.8; // 设置透明度
      ctx.font = `normal ${subTitleFontSize}px Arial, sans-serif`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      // 绘制副标题（只有当副标题不为空时才绘制）
      if (subTitle && subTitle !== '' && subTitle.trim().length > 0) {
        ctx.fillText(subTitle, width / 2, subTitleY);
      }
      ctx.globalAlpha = 1.0; // 重置透明度

      // 绘制旋转方形色块 - 动态布局支持2-5个颜色
      const diamondY = Math.floor(height * (894 / 2132));
      const diamondSize = Math.floor(175 * width / 800); // 基于新的Canvas尺寸缩放

      // 根据颜色数量动态计算位置
      let diamondPositions = [];
      const colorCount = colors.length;

      if (colorCount === 4) {
        // 4个颜色使用P03的精确位置（基于新的Canvas尺寸）
        diamondPositions = [
          Math.floor(83 * width / 800), // 第1个方形 (166/2)
          Math.floor(236 * width / 800), // 第2个方形 (472/2)
          Math.floor(389 * width / 800), // 第3个方形 (778/2)
          Math.floor(542 * width / 800) // 第4个方形 (1084/2)
        ];
      } else {
        // 其他数量的颜色居中布局
        let gap, totalWidth, margin, startX;

        if (colorCount === 3) {
          // 3个颜色：使用旋转后宽度计算，更保守的布局
          const rotatedDiamondWidth = Math.floor(diamondSize * Math.sqrt(2));
          gap = Math.floor(153 * width / 800); // 基于新Canvas尺寸 (306/2)
          totalWidth = colorCount * rotatedDiamondWidth + (colorCount - 1) * gap;
          margin = Math.floor(75 * width / 800); // 基于新Canvas尺寸 (150/2)
          const maxWidth = width - 2 * margin;

          if (totalWidth > maxWidth) {
            gap = Math.floor((maxWidth - colorCount * rotatedDiamondWidth) / (colorCount - 1));
            gap = Math.max(gap, Math.floor(10 * width / 800)); // 基于新Canvas尺寸 (20/2)
          }

          const actualTotalWidth = colorCount * diamondSize + (colorCount - 1) * gap;
          startX = (width - actualTotalWidth) / 2;

        } else if (colorCount === 5) {
          // 5个颜色：使用大重叠的超紧凑布局
          margin = Math.floor(5 * width / 800); // 使用最小边距，基于新Canvas尺寸
          const maxWidth = width - 2 * margin;

          // 使用更大的重叠比例来确保完全显示
          gap = Math.floor(-diamondSize * 0.25); // 25%重叠，更激进的重叠

          totalWidth = colorCount * diamondSize + (colorCount - 1) * gap;
          startX = (width - totalWidth) / 2;

          // 如果仍然超出，进一步增加重叠
          if (totalWidth > maxWidth) {
            gap = Math.floor(-diamondSize * 0.35); // 35%重叠
            totalWidth = colorCount * diamondSize + (colorCount - 1) * gap;
            startX = (width - totalWidth) / 2;
          }

          console.log(`5个颜色大重叠布局 - 边距: ${margin}, 重叠间距: ${gap}, 总宽度: ${totalWidth}, 起始X: ${startX}, 重叠比例: ${Math.abs(gap/diamondSize*100).toFixed(1)}%`);

        } else {
          // 2个颜色：使用标准布局
          gap = Math.floor(153 * width / 800); // 基于新Canvas尺寸 (306/2)
          totalWidth = colorCount * diamondSize + (colorCount - 1) * gap;
          margin = Math.floor(25 * width / 800); // 基于新Canvas尺寸 (50/2)
          const maxWidth = width - 2 * margin;

          if (totalWidth > maxWidth) {
            gap = Math.floor((maxWidth - colorCount * diamondSize) / (colorCount - 1));
            gap = Math.max(gap, Math.floor(5 * width / 800)); // 基于新Canvas尺寸 (10/2)
            totalWidth = colorCount * diamondSize + (colorCount - 1) * gap;
          }

          startX = (width - totalWidth) / 2;
        }

        console.log(`P03动态布局 - 颜色数量: ${colorCount}, 菱形尺寸: ${diamondSize}, 间距: ${gap}, 总宽度: ${totalWidth}, 起始X: ${startX}, 边距: ${margin}`);

        for (let i = 0; i < colorCount; i++) {
          diamondPositions.push(startX + i * (diamondSize + gap));
        }
      }

      colors.forEach((color, index) => {
        // 使用计算的位置，方形的中心点
        const x = diamondPositions[index] + diamondSize / 2;
        const y = diamondY + diamondSize / 2;

        console.log(`绘制第${index + 1}个菱形 - 颜色: ${color}, 位置: (${x}, ${y}), 尺寸: ${diamondSize}`);

        ctx.save();
        ctx.translate(x, y);
        ctx.rotate(Math.PI / 4); // 45度旋转
        ctx.fillStyle = color || '#FFC371';

        // 绘制带圆角的菱形，而不是直角矩形
        const cornerRadius = Math.floor(11.5 * width / 800); // 基于新Canvas尺寸缩放圆角半径 (23/2)
        this.drawRoundedRect(ctx, -diamondSize / 2, -diamondSize / 2, diamondSize, diamondSize, cornerRadius);

        ctx.restore();
      });

      // 绘制颜色代码 - 动态布局支持2-5个颜色
      const codeY = Math.floor(height * (1380 / 2132));
      const codeFontSize = Math.floor(21 * width / 800); // 基于新Canvas尺寸计算字体大小 (42/2)
      ctx.fillStyle = codeColor || '#7D7D7D';
      ctx.font = `bold ${codeFontSize}px Arial, sans-serif`;
      ctx.textAlign = 'center'; // 改为居中对齐，便于动态布局
      ctx.textBaseline = 'top';

      // 根据颜色数量动态计算位置
      let codePositions = [];

      if (colorCount === 4) {
        // 4个颜色使用P03的精确位置（转换为中心点，基于新Canvas尺寸）
        codePositions = [
          Math.floor(128 * width / 800) + Math.floor(85.5 * width / 800) / 2, // 第1个颜色代码中心 (256/2 + 171/2)
          Math.floor(278.5 * width / 800) + Math.floor(90 * width / 800) / 2, // 第2个颜色代码中心 (557/2 + 180/2)
          Math.floor(432 * width / 800) + Math.floor(89.5 * width / 800) / 2, // 第3个颜色代码中心 (864/2 + 179/2)
          Math.floor(585 * width / 800) + Math.floor(91 * width / 800) / 2 // 第4个颜色代码中心 (1170/2 + 182/2)
        ];
      } else {
        // 其他数量的颜色与菱形位置对应
        codePositions = diamondPositions.map(pos => pos + diamondSize / 2);
      }

      colors.forEach((color, index) => {
        const x = codePositions[index];
        const colorText = (color || '#FFC371').toUpperCase();

        console.log(`绘制第${index + 1}个颜色代码 - 文字: ${colorText}, 位置: (${x}, ${codeY})`);

        ctx.fillText(colorText, x, codeY);
      });

      // 绘制圆形色块 - 动态布局支持2-5个颜色
      const circleY = Math.floor(height * (1568 / 2132));
      const circleRadius = Math.floor(39.5 * width / 800); // 基于新Canvas尺寸 (79/2)

      // 根据颜色数量动态计算位置
      let circlePositions = [];

      if (colorCount === 4) {
        // 4个颜色使用P03的精确位置（基于新Canvas尺寸）
        circlePositions = [
          Math.floor(234.5 * width / 800), // 第1个圆形 (469/2)
          Math.floor(318.5 * width / 800), // 第2个圆形 (637/2)
          Math.floor(402.5 * width / 800), // 第3个圆形 (805/2)
          Math.floor(486.5 * width / 800)  // 第4个圆形 (973/2)
        ];
      } else {
        // 其他数量的颜色与菱形位置对应
        circlePositions = diamondPositions.map(pos => pos + (diamondSize - circleRadius * 2) / 2);
      }

      colors.forEach((color, index) => {
        // 使用计算的位置，圆形的中心点
        const x = circlePositions[index] + circleRadius;
        const y = circleY + circleRadius;

        console.log(`绘制第${index + 1}个圆形 - 颜色: ${color}, 位置: (${x}, ${y}), 半径: ${circleRadius}`);

        ctx.beginPath();
        ctx.arc(x, y, circleRadius, 0, 2 * Math.PI);
        ctx.fillStyle = color || '#FFC371';
        ctx.fill();
      });
    },

    // 绘制圆角矩形的方法
    drawRoundedRect(ctx, x, y, width, height, radius) {
      ctx.beginPath();
      ctx.moveTo(x + radius, y);
      ctx.lineTo(x + width - radius, y);
      ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
      ctx.lineTo(x + width, y + height - radius);
      ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
      ctx.lineTo(x + radius, y + height);
      ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
      ctx.lineTo(x, y + radius);
      ctx.quadraticCurveTo(x, y, x + radius, y);
      ctx.closePath();
      ctx.fill();
    },

    // 保存Canvas - 修复Promise错误处理
    async saveCanvas(canvasNode) {
      try {
        const { canvasWidth, canvasHeight } = this.data;

        // 添加超时控制的Canvas保存
        const res = await Promise.race([
          new Promise((resolve, reject) => {
            wx.canvasToTempFilePath({
              canvas: canvasNode,
              x: 0,
              y: 0,
              width: canvasWidth,
              height: canvasHeight,
              destWidth: canvasWidth,
              destHeight: canvasHeight,
              fileType: 'png',
              quality: 1,
              success: resolve,
              fail: reject
            }, this);
          }),
          new Promise((_, reject) => {
            setTimeout(() => {
              reject(new Error('P03 Canvas保存超时'));
            }, 10000);
          })
        ]);

        console.log('P03色卡保存成功:', res.tempFilePath);
        this.triggerEvent('generated', { path: res.tempFilePath });
      } catch (err) {
        console.error('保存P03样式自定义色卡失败:', err);
        this.handleCanvasError(err);
      }
    }
  }
});
