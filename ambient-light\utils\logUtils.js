/**
 * 日志工具函数
 * 在生产环境中禁用控制台日志输出
 */

// 是否为调试模式，在生产环境中应设置为false
const DEBUG_MODE = false;

/**
 * 记录日志信息
 * 在生产环境中不会输出到控制台
 * @param {string} message - 日志消息
 * @param {any} data - 附加数据（可选）
 */
function log(message, data) {
  if (DEBUG_MODE) {
    if (data !== undefined) {
      console.log(message, data);
    } else {
      console.log(message);
    }
  }
}

/**
 * 记录警告信息
 * 在生产环境中不会输出到控制台
 * @param {string} message - 警告消息
 * @param {any} data - 附加数据（可选）
 */
function warn(message, data) {
  if (DEBUG_MODE) {
    if (data !== undefined) {
      console.warn(message, data);
    } else {
      console.warn(message);
    }
  }
}

/**
 * 记录错误信息
 * 在生产环境中不会输出到控制台
 * @param {string} message - 错误消息
 * @param {any} data - 附加数据（可选）
 * @param {boolean} showToast - 是否显示错误提示（默认为false）
 */
function error(message, data, showToast = false) {
  if (DEBUG_MODE) {
    if (data !== undefined) {
      console.error(message, data);
    } else {
      console.error(message);
    }
  }
  
  // 可选：显示错误提示
  if (showToast) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  }
}

/**
 * 处理错误并显示提示
 * @param {string} message - 错误消息
 * @param {any} err - 错误对象（可选）
 * @param {string} toastType - 提示类型：'toast'或'modal'（默认为'toast'）
 */
function handleError(message, err, toastType = 'toast') {
  if (DEBUG_MODE && err) {
    console.error(message, err);
  }
  
  if (toastType === 'modal') {
    wx.showModal({
      title: '提示',
      content: message,
      showCancel: false
    });
  } else {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  }
}

module.exports = {
  log,
  warn,
  error,
  handleError
};
