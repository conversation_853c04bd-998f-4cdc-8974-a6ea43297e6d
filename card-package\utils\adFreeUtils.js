/**
 * 去广告功能工具函数 - 优化版本
 * 使用缓存机制避免重复调用 getStorageSync
 */

const { storageCache } = require('./storageCache');
const logUtils = require('./logUtils');

/**
 * 检查是否已激活去广告功能（带缓存）
 * @returns {boolean} 是否已激活去广告
 */
function isAdFreeActivated() {
  try {
    const adFreeStatus = storageCache.getStorageSync('adFreeStatus', false);
    return adFreeStatus === true;
  } catch (error) {
    logUtils.error('检查去广告状态失败', error);
    return false;
  }
}

/**
 * 激活去广告功能
 * @param {string} activationCode 激活码
 * @returns {boolean} 是否激活成功
 */
function activateAdFree(activationCode) {
  if (activationCode === 'KALA666') {
    try {
      storageCache.setStorageSync('adFreeStatus', true);
      logUtils.log('[AdFreeUtils] 去广告功能已激活');
      return true;
    } catch (error) {
      logUtils.error('激活去广告功能失败', error);
      return false;
    }
  }
  return false;
}

/**
 * 取消激活去广告功能
 * @returns {boolean} 是否取消成功
 */
function deactivateAdFree() {
  try {
    storageCache.setStorageSync('adFreeStatus', false);
    logUtils.log('[AdFreeUtils] 去广告功能已取消激活');
    return true;
  } catch (error) {
    logUtils.error('取消激活去广告功能失败', error);
    return false;
  }
}

module.exports = {
  isAdFreeActivated,
  activateAdFree,
  deactivateAdFree
};
