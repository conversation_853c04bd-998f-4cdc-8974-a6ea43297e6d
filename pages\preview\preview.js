// pages/preview/preview.js
const { SafeNavigation } = require('../../utils/safeNavigation');

Page({
  data: {
    imagePath: '',
    templateId: 3, // 默认使用色卡A模板 (ID 3)
    colors: [],
    cardPath: '',
    pageReady: false, // 页面是否准备好渲染色卡组件
    showSaveSuccess: false, // 是否显示保存成功提示
    componentLoaded: false // 组件是否已动态加载
  },

  onLoad(options) {
    try {
      // 检查是否是自定义色卡模式
      const isCustom = options.isCustom === 'true';

      if (isCustom && options.colors) {
        // 自定义色卡模式
        let colors = [];

        console.log('原始颜色参数:', options.colors);

        try {
          colors = JSON.parse(decodeURIComponent(options.colors));
          console.log('解析后的颜色数组:', colors);
          console.log('颜色数组长度:', colors ? colors.length : 'undefined');

          if (!Array.isArray(colors)) {
            console.log('颜色数据不是数组，使用默认值');
            colors = ['#FFE6EB', '#FFE6EB', '#FFE6EB', '#FFE6EB'];
          }
        } catch (parseError) {
          console.error('解析颜色数据失败:', parseError);
          colors = ['#FFE6EB', '#FFE6EB', '#FFE6EB', '#FFE6EB'];
        }

        const templateId = parseInt(options.templateId) || 101;
        const title = options.title ? decodeURIComponent(options.title) : '草莓布丁';
        // 只有当subTitle参数存在且不为空时才解码，否则设置为null
        const subTitle = (options.subTitle && options.subTitle !== 'null' && options.subTitle !== '')
          ? decodeURIComponent(options.subTitle)
          : null;

        let updateData = {
          imagePath: '', // 自定义色卡不使用图片
          colors,
          templateId,
          cardPath: '',
          isCustom: true,
          customTitle: title,
          customSubTitle: subTitle
        };

        if (templateId === 102) {
          // 海盐气泡模板 (P01样式)
          const backgroundColor = options.backgroundColor ? decodeURIComponent(options.backgroundColor) : '#FFFFFF';
          const titleColor = options.titleColor ? decodeURIComponent(options.titleColor) : '#5865B1';
          updateData.customBackgroundColor = backgroundColor;
          updateData.customTitleColor = titleColor;
        } else if (templateId === 103) {
          // 落日漫旅模板 (P03样式)
          const topBackgroundColor = options.topBackgroundColor ? decodeURIComponent(options.topBackgroundColor) : '#F7E8E4';
          const bottomBackgroundColor = options.bottomBackgroundColor ? decodeURIComponent(options.bottomBackgroundColor) : '#FFFFFF';
          const fontColor = options.fontColor ? decodeURIComponent(options.fontColor) : '#C66767';
          const codeColor = options.codeColor ? decodeURIComponent(options.codeColor) : '#7D7D7D';
          updateData.customTopBackgroundColor = topBackgroundColor;
          updateData.customBottomBackgroundColor = bottomBackgroundColor;
          updateData.customFontColor = fontColor;
          updateData.customCodeColor = codeColor;
        } else if (templateId === 104) {
          // 春日樱语模板 (P04样式)
          const topBackgroundColor = options.topBackgroundColor ? decodeURIComponent(options.topBackgroundColor) : '#FFF1F1';
          const bottomBackgroundColor = options.bottomBackgroundColor ? decodeURIComponent(options.bottomBackgroundColor) : '#FFFFFF';
          const fontColor = options.fontColor ? decodeURIComponent(options.fontColor) : '#E89EC3';
          updateData.customTopBackgroundColor = topBackgroundColor;
          updateData.customBottomBackgroundColor = bottomBackgroundColor;
          updateData.customFontColor = fontColor;
        } else {
          // 蜜桃汽水模板 (P02样式，默认)
          const topBackgroundColor = options.topBackgroundColor ? decodeURIComponent(options.topBackgroundColor) : '#FFF2EB';
          const bottomBackgroundColor = options.bottomBackgroundColor ? decodeURIComponent(options.bottomBackgroundColor) : '#FFFFFF';
          const fontColor = options.fontColor ? decodeURIComponent(options.fontColor) : '#EB898E';
          updateData.customTopBackgroundColor = topBackgroundColor;
          updateData.customBottomBackgroundColor = bottomBackgroundColor;
          updateData.customFontColor = fontColor;
        }

        // 设置页面数据 - 自定义色卡不需要imagePath
        console.log('预览页面设置的数据:', updateData);
        console.log('最终传递给组件的颜色数组:', updateData.colors);
        console.log('最终传递给组件的颜色数量:', updateData.colors ? updateData.colors.length : 'undefined');

        this.setData(updateData);

        // 延迟初始化容器，确保数据已设置
        setTimeout(() => {
          this.initializeCardContainer();
        }, 100);

      } else if (options.imagePath && options.colors) {
        // 图片主题色模式
        const imagePath = decodeURIComponent(options.imagePath);
        let colors = [];

        try {
          colors = JSON.parse(decodeURIComponent(options.colors));
          if (!Array.isArray(colors)) {
            colors = [];
          }
        } catch (parseError) {
          colors = ['#4A4130', '#5D2317', '#900407', '#D8DDE1', '#7E96B2'];
        }

        const templateId = parseInt(options.templateId) || 3;

        this.setData({
          imagePath,
          colors,
          templateId,
          cardPath: '',
          isCustom: false
        });

        // 延迟初始化容器，确保数据已设置
        setTimeout(() => {
          this.initializeCardContainer();
        }, 100);
      } else {
        // 缺少必要的参数，静默处理
      }

      // 异步获取导航栏高度，避免阻塞页面加载
      wx.nextTick(() => {
        this.getNavBarHeight();
      });
    } catch (error) {
      console.error('预览页面加载出错:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 获取导航栏高度
  getNavBarHeight() {
    try {
      // 使用默认值快速设置，避免阻塞
      this.setData({
        scrollPaddingTop: '20rpx'
      });

      // 异步获取精确值
      setTimeout(() => {
        try {
          // 使用新的API获取窗口信息
          const windowInfo = wx.getWindowInfo();
          const systemInfo = windowInfo;

          // 获取胶囊按钮位置信息
          let menuButtonInfo;
          try {
            menuButtonInfo = wx.getMenuButtonBoundingClientRect();
          } catch (err) {
            // 使用默认值
            menuButtonInfo = {
              top: systemInfo.statusBarHeight + 8,
              bottom: systemInfo.statusBarHeight + 8 + 32,
              height: 32
            };
          }

          // 计算导航栏高度
          const navBarHeight = menuButtonInfo.bottom;

          // 获取导航栏组件高度
          const query = wx.createSelectorQuery();
          query.select('navigation-bar').boundingClientRect();
          query.exec((res) => {
            if (res && res[0]) {
              const navBarComponent = res[0];
              const pagePadding = navBarComponent.height - 35;
              this.setData({
                scrollPaddingTop: pagePadding + 'px'
              });
            } else {
              this.setData({
                scrollPaddingTop: (navBarHeight - 35) + 'px'
              });
            }
          });
        } catch (err) {
          // 保持默认值
        }
      }, 50);
    } catch (err) {
      console.error('获取导航栏高度失败:', err);
      this.setData({
        scrollPaddingTop: '20rpx'
      });
    }
  },

  onReady() {
    // 在页面渲染完成后，延迟一段时间再初始化色卡组件
    // 这样可以避免组件初始化过早导致的问题
    setTimeout(() => {
      this.initializeCardContainer();
    }, 300); // 延迟300毫秒，确保页面已完全渲染
  },

  // 初始化色卡容器
  initializeCardContainer() {
    try {
      // 检查是否有必要的数据来渲染组件
      const { templateId, colors, isCustom, imagePath } = this.data;

      console.log('初始化色卡容器，当前数据:', {
        templateId,
        colors: colors?.length,
        isCustom,
        imagePath: imagePath ? '有图片' : '无图片',
        pageReady: this.data.pageReady
      });

      // 验证数据完整性
      if (!colors || colors.length === 0) {
        console.warn('颜色数据不完整，无法初始化色卡容器');
        return;
      }

      if (!isCustom && !imagePath) {
        console.warn('图片路径缺失，无法初始化色卡容器');
        return;
      }

      // 检查模板ID是否有效
      const validTemplateIds = isCustom
        ? [101, 102, 103, 104] // 自定义模式的有效模板ID
        : [3, 4, 5, 6, 7, 8, 9, 10, 11]; // 图片模式的有效模板ID

      if (!validTemplateIds.includes(templateId)) {
        console.warn('无效的模板ID:', templateId, '模式:', isCustom ? '自定义' : '图片');
        console.log('有效的模板ID:', validTemplateIds);
      }

      // 动态加载所需的组件
      this.loadRequiredComponent(templateId, isCustom, () => {
        // 组件加载完成后，延迟查询DOM
        setTimeout(() => {
          // 获取色卡容器
          const query = wx.createSelectorQuery();
          query.select('#cardContainer').boundingClientRect();
          query.exec((res) => {
            if (res && res[0]) {
              console.log('色卡容器已准备就绪:', res[0]);

              // 标记页面已准备好，可以渲染色卡组件
              this.setData({
                pageReady: true
              });
            } else {
              console.warn('未找到色卡容器，可能是模板ID不匹配或条件不满足');
              console.log('详细信息:', {
                templateId,
                isCustom,
                colors: colors?.length,
                imagePath: !!imagePath,
                validTemplateIds
              });

              // 直接设置页面准备状态，让组件尝试渲染
              this.setData({
                pageReady: true
              });
            }
          });
        }, 100); // 延迟100ms确保DOM渲染完成
      });
    } catch (error) {
      console.error('准备色卡容器时出错:', error);
      // 即使出错也设置页面准备状态
      this.setData({
        pageReady: true
      });
    }
  },

  // 准备组件渲染（替代动态加载）
  loadRequiredComponent(templateId, isCustom, callback) {
    try {
      console.log('准备渲染组件，模板ID:', templateId, '自定义模式:', isCustom);

      // 由于微信小程序不支持真正的动态组件加载，
      // 我们使用条件渲染来实现按需加载的效果
      this.setData({
        componentLoaded: true
      });

      if (callback) callback();
    } catch (error) {
      console.error('准备组件渲染时出错:', error);
      this.setData({
        componentLoaded: true
      });
      if (callback) callback();
    }
  },

  // 配色卡生成完成的回调
  onCardGenerated(e) {
    try {
      console.log('配色卡生成完成，接收到事件:', e);

      if (e && e.detail && e.detail.path) {
        const { path } = e.detail;

        // 验证路径是否有效
        if (typeof path === 'string' && path.length > 0) {
          this.setData({
            cardPath: path
          });
          console.log('配色卡路径已更新:', path);
        } else {
          console.warn('接收到无效的配色卡路径:', path);
          // 显示错误提示
          wx.showToast({
            title: '色卡生成失败',
            icon: 'none',
            duration: 2000
          });
          // 重试生成色卡
          this.retryGenerateCard();
        }
      } else {
        console.warn('配色卡生成事件缺少必要的数据:', e);
        // 显示错误提示
        wx.showToast({
          title: '色卡生成失败',
          icon: 'none',
          duration: 2000
        });
        // 重试生成色卡
        this.retryGenerateCard();
      }
    } catch (error) {
      console.error('处理配色卡生成事件时出错:', error);
      // 显示错误提示
      wx.showToast({
        title: '色卡生成失败',
        icon: 'none',
        duration: 2000
      });
      // 重试生成色卡
      this.retryGenerateCard();
    }
  },

  // 重试生成色卡
  retryGenerateCard() {
    // 重置页面准备状态，触发组件重新渲染
    this.setData({
      pageReady: false
    });

    // 延迟一段时间后重新初始化色卡组件
    setTimeout(() => {
      this.setData({
        pageReady: true
      });
    }, 500);
  },

  // 保存配色卡到相册
  saveToAlbum() {
    // 检查cardPath是否存在
    if (!this.data.cardPath) {
      // 如果配色卡路径不存在，提示用户等待生成完成
      wx.showToast({
        title: '色卡制作中，请稍候',
        icon: 'none',
        duration: 2000
      });
      return; // 直接返回，不执行保存操作
    }

    // 显示加载提示
    wx.showLoading({
      title: '保存中...',
      mask: true
    });

    // 直接调用保存图片API
    wx.saveImageToPhotosAlbum({
      filePath: this.data.cardPath,
      success: () => {
        wx.hideLoading();

        // 显示自定义保存成功提示
        this.setData({
          showSaveSuccess: true
        });

        // 2秒后隐藏提示
        setTimeout(() => {
          this.setData({
            showSaveSuccess: false
          });
        }, 2000);
      },
      fail: (err) => {
        wx.hideLoading();

        // 如果是因为用户拒绝授权导致的失败
        if (err.errMsg.indexOf('auth deny') >= 0 || err.errMsg.indexOf('auth failed') >= 0) {
          wx.showModal({
            title: '提示',
            content: '需要您授权保存图片到相册',
            confirmText: '去授权',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.openSetting({
                  success: (settingRes) => {
                    if (settingRes.authSetting['scope.writePhotosAlbum']) {
                      // 获得授权后重新保存
                      this.saveToAlbum();
                    }
                  }
                });
              }
            }
          });
        } else {
          wx.showToast({
            title: '保存失败',
            icon: 'none',
            duration: 2000
          });
        }
      }
    });
  },

  // 预览图片
  previewImage() {
    if (this.data.cardPath) {
      wx.previewImage({
        urls: [this.data.cardPath],
        current: this.data.cardPath,
        showmenu: true,
        success: () => {
          console.log('预览图片成功');
        },
        fail: (err) => {
          console.error('预览图片失败:', err);
          wx.showToast({
            title: '预览失败',
            icon: 'none'
          });
        }
      });
    }
  },

  // 返回首页
  goToHome() {
    SafeNavigation.goHome();
  }
})
